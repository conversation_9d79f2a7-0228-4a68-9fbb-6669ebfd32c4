const mysqlDB = require("../common/mysql_pool");
const {getDaoShard} = require("../utils/sharding");

function getCommandReportSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  let timeSql = "";
  if (queryParams.startTime && queryParams.endTime) {
    timeSql += "and gcm.send_time between :start_time and :end_time";
  }

  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.plate_no,gd.imei,gd.device_name,gd.device_type,gd.device_model,
  gcm.cmd_code,gcm.send_data,gcm.send_content,gcm.send_time,gcm.cmd_status,gcm.send_num,gcm.return_data,
  gcm.return_time,gcm.create_user,gcm.create_time
  from gps_command gcm
  join gps_device gd on gcm.imei=gd.imei and gd.is_settle=0 and gd.is_delete=0
  join uc_organ uo on gd.organ_id=uo.id ${
    queryParams.organId === -1 ? "" : organSql
  }
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
  }
  where (:search_key='' or concat(gd.plate_no,gd.device_name,gd.imei,gcm.create_user) like concat('%',:search_key,'%')) ${timeSql} ${
    queryParams.imei === "" ? "" : " and gcm.imei=:imei"
  }
  order by gcm.create_time desc,gd.plate_no`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertCommandReportSql(insertData) {
  const sql = `insert into gps_command(imei,cmd_seq,cmd_code,send_data,cmd_status,remark,create_user,create_time)
  select :imei,ifnull(max(cmd_seq),0)+1,:cmd_code,:send_data,:cmd_status,:remark,:user_name,now()
  from gps_command
  where imei=:imei`;

  return mysqlDB.insert(sql, insertData);
}

function updateCommandReportSql(updateData) {
  let sql = `update gps_command
  set cmd_status=3
  where imei=:imei and cmd_code=:cmd_code and id<:id and cmd_status<2`;

  if (updateData.sendData) {
    sql += " and left(send_data,2)=:send_data";
  }

  return mysqlDB.update(sql, updateData);
}

module.exports = {
  getCommandReportSqlList,
  insertCommandReportSql,
  updateCommandReportSql,
};
