const mysqlDB = require("../common/mysql_pool");

function getPermissionSqlList(queryParams) {
  const sql = `select up.id,up.id per_id,up.parent_id,up.per_code,up.per_name,up.path,up.per_url,up.per_icon,
  up.per_type,up.sort,up.system_type,up.status,up.remark,up.create_user,up.create_time,
  if(count(urp.per_id)=0 and up.per_type='BUTTON',1,0) is_can_delete
  from uc_permission up
  ${
    queryParams.roleIds === ""
      ? "left join uc_role_per urp on urp.per_id=up.id"
      : "join uc_role_per urp on urp.per_id=up.id and :role_ids like concat('%,',urp.role_id,',%')"
  }
  where (:status=-1 or up.status=:status) and (:system_type=-1 or up.system_type=:system_type) and (:parent_id=-1 or up.parent_id=:parent_id)
  group by up.id
  order by up.system_type,up.sort,up.per_url`;

  return mysqlDB.all(sql, queryParams);
}

function getPermissionSqlData(queryParams) {
  const sql = `select up.id per_id,up.path
  from uc_permission up
  where up.id =:per_id`;

  // const sql = `select up.id per_id,up.path,if(count(urp.per_id)=0,1,0) is_can_delete
  // from uc_permission up
  // left join uc_role_per urp on urp.per_id=up.id
  // where up.id =:per_id
  // group by up.id
  // order by up.sort`;

  return mysqlDB.one(sql, queryParams);
}

function getPermissionRecordsSql(queryParams) {
  const sql = `select count(*) record,id per_id
  from uc_permission
  where per_name=:per_name`;

  return mysqlDB.one(sql, queryParams);
}

function getRolePermissionSqlList() {
  const sql = `select rp.role_id,rp.per_id
  from uc_role_per rp
  join uc_role ur on rp.role_id=ur.id and ur.status=1 `;

  return mysqlDB.all(sql, {});
}

function insertPermissionSql(permissionData) {
  const sqlTasks = [];
  const insertSql = `insert into uc_permission
  (parent_id,per_code,per_name,per_url,per_icon,per_type,sort,system_type,status,remark,create_user,create_time)
  values
  (:parent_id,:per_code,:per_name,:per_url,:per_icon,:per_type,:sort,:system_type,:status,:remark,:create_user,now())`;

  const pathSql = `update uc_permission
  set path = concat(:path,(select last_insert_id()),',')
  where id = (select last_insert_id())`;

  sqlTasks.push({ sql: insertSql, params: permissionData });
  sqlTasks.push({ sql: pathSql, params: permissionData });

  return mysqlDB.executeTransaction(sqlTasks);
}

function updatePermissionSql(permissionData) {
  const updateSql = `update uc_permission
   set per_name=:per_name,per_code=:per_code,per_url=:per_url,per_icon=:per_icon,per_type=:per_type,sort=:sort,status=:status,
   remark=:remark,update_user=:update_user,update_time=now()
   where id=:per_id`;

  return mysqlDB.update(updateSql, permissionData);
}

function batchInsertPermissionSql(insertList) {
  const sqlTasks = [];
  const insertSql = `insert into uc_permission
  (parent_id,per_code,per_name,per_url,per_icon,per_type,sort,system_type,status,remark,create_user,create_time)
  select :parent_id,:per_code,:per_name,:per_url,:per_icon,:per_type,:sort,:system_type,:status,:remark,:create_user,now()
  from uc_permission
  where (select count(*) from uc_permission where parent_id=:parent_id and per_name=:per_name and per_url=:per_url and system_type=:system_type) = 0
  limit 1`;

  const pathSql = `update uc_permission
  set path=concat(:path,(select last_insert_id()),',')
  where id=(select last_insert_id())`;

  insertList.forEach((e) => {
    sqlTasks.push({ sql: insertSql, params: e });
    sqlTasks.push({ sql: pathSql, params: e });
  });

  return mysqlDB.executeTransaction(sqlTasks);
}

function getPermissionByIdsSql(queryParams) {
  const sql = `select id per_id,path,status from uc_permission where id in (${queryParams.perIds}) and id!=:per_id`;

  return mysqlDB.all(sql, queryParams);
}

function setPermissionEnableSql(updateData) {
  const sql =
    'update uc_permission set status=:status where path like concat("%",:path,"%")';

  return mysqlDB.update(sql, updateData);
}

// 目前存在：用户有绑定权限时不可删除BUG
// function deleteButtonTypePermissionSql(updateData) {
//
//   const sql = `delete up1 from uc_permission up1
//   join (
//     select up.id per_id,up.per_type,if(count(urp.per_id)=0,1,0) is_can_delete
//     from uc_permission up
//     left join uc_role_per urp on urp.per_id=up.id
//     where up.id=:per_id
//     group by up.id
//   ) up2 on up2.per_id=up1.id and up2.is_can_delete=1
//   where up1.id=:per_id`;
//
//   return mysqlDB.del(sql, updateData);
// }

function deleteBatchPermissionSql(deleteList) {
  const sqlTasks = [];

  const deleteRoleSql = `delete urp from uc_role_per urp
  join uc_permission up on urp.per_id=up.id and up.path like concat('%,',:perId,',%')`;

  const deletePerSql = `delete from uc_permission
  where path like concat('%,',:perId,',%')`;

  deleteList.forEach((e) => {
    sqlTasks.push({ sql: deleteRoleSql, params: { perId: e } });
    sqlTasks.push({ sql: deletePerSql, params: { perId: e } });
  });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getPermissionSqlList,
  getPermissionSqlData,
  getPermissionByIdsSql,
  getPermissionRecordsSql,
  getRolePermissionSqlList,
  insertPermissionSql,
  updatePermissionSql,
  batchInsertPermissionSql,
  setPermissionEnableSql,
  deleteBatchPermissionSql,
};
