const mysqlDB = require("../common/mysql_pool");
const {getDaoShard} = require("../utils/sharding");

function getOperationReportSqlList(queryParams, currentUser) {
  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo2.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo2.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select gol.organ_name,gol.plate_no,gol.imei,gol.device_name,
  gol.operation_type,gol.content,gol.remark,gol.create_user,gol.create_time,uo2.organ_name as device_organ_name
  from gps_operation_log gol
  inner join uc_organ uo on gol.organ_id=uo.id
  inner join gps_device gd on gd.imei=gol.imei
  inner join uc_organ uo2 on gd.organ_id=uo2.id and uo2.path like concat('%,',:current_organ_id,',%') ${
      queryParams.organId === -1 ? "" : organSql
  }
  where gol.create_time between :start_time and :end_time and (:user_id=0 or gol.user_id=:user_id) and (:operation_type='' or operation_type=:operation_type)
  ${queryParams.imei === "" ? "" : " and gol.imei=:imei"}
  order by gol.create_time desc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

module.exports = {
  getOperationReportSqlList,
};
