const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const geography = require("../proxy/geography_js");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");
const siteConfig = require("../config/config_site");
const fenceDao = require("../lib/dao/fence_dao");

/**
 * 获取围栏列表
 * 请求参数:
 "searchKey": "搜索信息",
 "organId": "机构Id",
 "isShare": "是否分享"
 *
 */
async function getFenceListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "isShare",
      title: "是否分享",
      rule: [{ name: "isIntFormat", opt: { min: -1, max: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }
  queryParams.status = 1;
  const [fenceList, keyValConfig, keyIdenConfig] = await Promise.all([
    fenceDao.getFenceSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "SHAPE_TYPE",
      "DATA_SOURCE",
      "IN_OUT",
    ]),
    global.context.getPropertymapKeyIden(["SHAPE_TYPE"]),
  ]);

  fenceList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.shapeTypeName = keyValConfig.SHAPE_TYPE[e.shapeType] || "";
    e.shapeTypeIden = keyIdenConfig.SHAPE_TYPE[e.shapeType] || "";
    e.dataSourceName = keyValConfig.DATA_SOURCE[e.dataSource] || "";
    e.inOutName = keyValConfig.IN_OUT[e.inOut] || "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return fenceList;
}

/**
 * 围栏数据验证
 *
 */
async function _validateFenceRequest(requestData) {
  const rules = [
    {
      field: "fenceId",
      title: "围栏Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    },
    {
      field: "organId",
      title: "所属车组",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "i",
    },
    {
      field: "shapeType",
      title: "形状类型",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "shapeScope",
      title: "形状范围",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    /*    { field: 'stopMinutes', title: '停留分钟数', rule: [{ name: 'isIntFormat', opt: { min: 0 } }], msg: '', required: true },*/
    {
      field: "isShare",
      title: "是否分享",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "inOut",
      title: "禁止进出",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  let [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  commFunc.formatNullToString(model);
  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "DATA_SOURCE",
    "SHAPE_TYPE",
  ]);
  model.dataSource = nameKeyConfig.DATA_SOURCE.HT;

  // 百度地图时，转化坐标
  if (`${model.shapeType}` === nameKeyConfig.SHAPE_TYPE.YX && model.isBaidu) {
    const lngLat = JSON.parse(model.shapeData);
    const [lng, lat] = geography.convertBaiduToGps(lngLat.lng, lngLat.lat);
    model.shapeData = JSON.stringify({ lng, lat });
  }

  if (`${model.shapeType}` === nameKeyConfig.SHAPE_TYPE.JX && model.isBaidu) {
    const lngLats = JSON.parse(model.shapeData);
    lngLats.forEach((f) => {
      const [lng, lat] = geography.convertBaiduToGps(f.lng, f.lat);
      f.lng = lng;
      f.lat = lat;
    });
    model.shapeData = JSON.stringify(lngLats);
  }

  if (`${model.shapeType}` === nameKeyConfig.SHAPE_TYPE.XZQ) {
  }

  return [undefined, model];
}

/**
 * 新增围栏
 * 请求参数:
 "organId": "机构Id",
 "fenceName": "围栏名称",
 "shapeType": "形状类型",
 "shapeData": "围栏数据",
 "stopMinutes": "停留分钟数",
 "isShare": "是否分享",
 "inOut": "禁止进出",
 "remark": "备注",
 "userName": "操作人"
 *
 */
async function insertFenceLogic(insertData) {
  insertData.opt = "i";
  const [err, model] = await _validateFenceRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await fenceDao.insertFenceSql(model);
}

/**
 * 新增围栏
 * 请求参数:
 "fenceId": "围栏Id",
 "organId": "机构Id",
 "fenceName": "围栏名称",
 "shapeType": "形状类型",
 "shapeData": "围栏数据",
 "stopMinutes": "停留分钟数",
 "isShare": "是否分享",
 "inOut": "禁止进出",
 "remark": "备注",
 "userName": "操作人"
 *
 */
async function updateFenceLogic(updateData) {
  updateData.opt = "u";
  const [err, model] = await _validateFenceRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const result = await fenceDao.updateFenceSql(model);
  const fenceDeviceList = await fenceDao.getFenceDeviceSqlList({
    fenceId: model.fenceId,
  });
  if (fenceDeviceList.length > 0) {
    await context.redisClient.publish(siteConfig.redisChannel.fence, "");
  }
  return result;
}

/**
 * 删除围栏
 * 请求参数:
 "fenceIds": "围栏Ids"
 *
 */
async function deleteFenceLogic(deleteData) {
  if (
    !validator.isArray(deleteData.fenceIds, { min: 1 }, [
      { rule: "isIntFormat", opt: { min: 1 }, required: true },
    ])
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  const fenceDeviceList = await fenceDao.getFenceDeviceSqlList({
    fenceId: deleteData.fenceIds[0],
  });
  if (fenceDeviceList.length > 0) {
    throw new RetJson(
      i18n.SYS_ERROR_CODE,
      "该围栏已经绑定了设备，请先清空绑定"
    );
  }

  const result = await fenceDao.deleteFenceSql(deleteData);
  return result;
}

/**
 * 获取围栏设备列表
 * 请求参数:
 "fenceId": "围栏Id"
 *
 */
async function getFenceDeviceListLogic(queryParams) {
  const rules = [
    {
      field: "fenceId",
      title: "围栏Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [fenceDeviceList, keyValConfig] = await Promise.all([
    fenceDao.getFenceDeviceSqlList(queryParams),
    global.context.getPropertymapKeyVal(["DATA_CLASS"]),
  ]);

  fenceDeviceList.forEach((e, i) => {
    e.index = i + 1;
    commFunc.formatNullToString(e);
    e.dataClassName = keyValConfig.DATA_CLASS[e.dataClass] || "";
  });

  return fenceDeviceList;
}

/**
 * 新增围栏设备
 * 请求参数:
 "fenceId": "围栏Id",
 "organId": "所属车组",
 "imei": "设备号",
 "dataClass": "数据类别",
 "userName": "操作人"
 *
 */
async function insertFenceDeviceLogic(insertList) {
  const rules = [
    {
      field: "fenceId",
      title: "围栏Id",
      rule: "isIntFormat",
      opt: { min: 1 },
      msg: "",
      required: true,
    },
    {
      field: "organId",
      title: "所属车组",
      rule: "isIntFormat",
      opt: { min: 0 },
      msg: "",
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "" },
    {
      field: "dataClass",
      title: "数据类别",
      rule: "isIntFormat",
      opt: { min: 1 },
      msg: "",
      required: true,
    },
  ];

  if (!validator.isArray(insertList, { min: 1 }, rules)) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_FIELD_ERROR);
  }

  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "DATA_TYPE",
  ]);
  if (
    insertList.filter(
      (f) =>
        `${f.dataClass}` === nameKeyConfig.DATA_TYPE.CO &&
        `${f.organId}` === "0"
    ).length > 0
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_FIELD_ERROR);
  }
  if (
    insertList.filter(
      (f) => `${f.dataClass}` === nameKeyConfig.DATA_TYPE.SB && !f.imei
    ).length > 0
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_FIELD_ERROR);
  }

  const result = await fenceDao.insertFenceDeviceSql(insertList);
  await context.redisClient.publish(siteConfig.redisChannel.fence, "");
  return result;
}

/**
 * 删除围栏设备
 * 请求参数:
 "fenceDeviceIds": "围栏设备Ids"
 *
 */
async function deleteFenceDeviceLogic(deleteData) {
  if (!validator.isIntArray(deleteData.fenceDeviceIds, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  const result = await fenceDao.deleteFenceDeviceSql(deleteData);
  await context.redisClient.publish(siteConfig.redisChannel.fence, "");
  return result;
}

module.exports = {
  getFenceListLogic,
  insertFenceLogic,
  updateFenceLogic,
  deleteFenceLogic,

  getFenceDeviceListLogic,
  insertFenceDeviceLogic,
  deleteFenceDeviceLogic,
};
