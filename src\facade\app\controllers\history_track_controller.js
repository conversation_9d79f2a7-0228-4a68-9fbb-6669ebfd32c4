const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const deviceBusiness = require("../business/device_business");
const trackBusiness = require("../business/history_track_business");
const validator = require("../lib/common/validator_extend");

/**
 * @api {GET} /historytrack/gethistorytracklist 轨迹管理-获取历史轨迹分页列表
 * @apiName gethistorytracklist
 * @apiGroup HistoryTrack
 * @apiVersion  1.0.0
 * @apiPermission HT-10
 *
 * @apiParam  {string} imei 设备号
 * @apiParam  {Number} locType 定位类型
 * @apiParam  {string} startTime 开始时间
 * @apiParam  {string} endTime 结束时间
 *
 * @apiParamExample  {json} 请求示例:
 {
 }
 *
 * @apiSuccessExample {json} 响应示例:
 {
     "errcode":0,
     "errmsg":"操作成功",
     "retobj":{
     }
   }
 */
exports.getHistoryTrackList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "轨迹管理-获取历史轨迹分页列表",
  buttonPerm: "HT-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;

      const queryParams = {
        imei: reqQueryParams.imei || "",
        locType: reqQueryParams.locType || "0",
        startTime: reqQueryParams.startTime || "",
        endTime: reqQueryParams.endTime || "",
      };

      if (!validator.isIntFormat(queryParams.locType, { min: 0 })) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_DATA_ERROR
        );
        return;
      }

      if (!validator.isIMEI(queryParams.imei)) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_DATA_ERROR
        );
        return;
      }

      if (
        !(
          validator.isDateFormat(queryParams.startTime, "YYYYMMDDHHmmss") &&
          validator.isDateFormat(queryParams.endTime, "YYYYMMDDHHmmss")
        )
      ) {
        ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.LOG_TIME_FORMAT_ERROR);
        return;
      }

      if (
        moment(queryParams.startTime).format("X") >=
        moment(queryParams.endTime).format("X")
      ) {
        ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.LOG_TIME_FORMAT_ERROR);
        return;
      }

      if (
        moment(queryParams.endTime).format("X") -
          moment(queryParams.startTime).format("X") >
        90 * 24 * 3600
      ) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.TRACK_TIME_FORMAT_ERROR
        );
        return;
      }

      queryParams.locType = Number.parseInt(queryParams.locType, 10);
      const currentDevice = await deviceBusiness.getDeviceDataLogic({
        imei: queryParams.imei,
        organId: currentUser.organId,
      });
      const trackList = await trackBusiness.getHistoryTrackListLogic(
        queryParams
      );
      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE, {
        currentDevice,
        trackList,
      });
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MONGODB",
        "getHistoryTrackList",
        "获取历史轨迹分页列表错误:",
        err
      );

      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
