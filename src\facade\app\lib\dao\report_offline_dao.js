const {getDaoShard} = require("../utils/sharding");
const mysqlDB = require("../common/mysql_pool");

function getOfflineReportSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.plate_no,gd.attention,
  gd.imei,gd.device_name,gd.device_type,gd.device_model,gd.sim_no,gd.iccid,
  gd.last_online_time,gd.last_offline_time
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id  ${
    queryParams.organId === -1 ? "" : organSql
  }
  ${
      // 子账户需要连表, 白名单内的的设备才能被查看
      currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
  }
  where gd.last_offline_time between :start_time and :end_time
  and gd.is_settle=0 and gd.is_delete=0
  ${queryParams.imei === "" ? "" : " and gd.imei=:imei"}
  ${queryParams.deviceType === -1 ? "" : " and gd.device_type=:device_type"}
  order by gd.last_offline_time desc,gd.plate_no`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

module.exports = {
  getOfflineReportSqlList,
};
