const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const offlineBusiness = require("../business/report_offline_business");

/**
 * @api {GET} /report/getofflinereportlist 报警记录-获取离线报表
 * @apiName getofflinereportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission ROF-10
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} startSecond 开始秒
 * @apiParam  {Number} endSecond 结束秒
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getOfflineReportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-获取离线报表",
  buttonPerm: "ROF-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: validator.isInt01(reqQueryParams.hasSubOrgan)
          ? Number.parseInt(reqQueryParams.hasSubOrgan)
          : 0,
        imei: (reqQueryParams.imei || "").trim(),
        startSecond: reqQueryParams.startSecond,
        endSecond: reqQueryParams.endSecond,
        deviceType: validator.isIntFormat(reqQueryParams.deviceType, { min: 1 })
          ? Number.parseInt(reqQueryParams.deviceType, 10)
          : -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const offlineList = await offlineBusiness.getOfflineListLogic(
        queryParams,
        currentUser
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        offlineList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getOfflineReportList",
          "获取离线报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOfflineReportList",
        "获取离线报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /report/exportofflinereportlist 报警记录-导出离线报表
 * @apiName exportofflinereportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission ROF-11
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {boolean} pagination=true 分页: 默认true开启, false关闭
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportOfflineReportList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-导出离线报表",
  buttonPerm: "ROF-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        imei: (requestBody.imei || "").trim(),
        startSecond: requestBody.startSecond,
        endSecond: requestBody.endSecond,
        deviceType: validator.isIntFormat(requestBody.deviceType, { min: 1 })
          ? Number.parseInt(requestBody.deviceType, 10)
          : -1,
        pagination: requestBody.pagination !== false || false,
        currentPage: requestBody.currentPage || 1,
        pageSize: requestBody.pageSize,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      const offlineList = await offlineBusiness.getOfflineListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "设备名称",
        "设备号",
        "设备类型",
        "设备型号",
        "SIM卡号",
        "最后在线时间",
        "离线时长",
        "位置",
      ]);

      offlineList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.deviceName,
          e.imei,
          e.deviceTypeName,
          e.deviceModelName,
          e.simNo,
          e.lastOnlineTime,
          e.offlineLong,
          e.currentAddress,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `离线报表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportOfflineReportList",
          "导出离线报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportOfflineReportList",
        "导出离线报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
