const mysqlDB = require("../common/mysql_pool");

function getUserDataByUserNameSql(queryParams) {
  const sql = `select uu.id user_id,uu.user_code,uu.user_name,uu.login_name,uu.password,uu.realname,uu.type,
  uu.organ_id,uo.parent_id organ_parent_id,uo.role_ids organ_role_list,
  uu.status,uu.role_ids user_role_list,uu.is_super
  from uc_user uu
  join uc_organ uo on uu.organ_id=uo.id
  where uu.login_name=:user_name and uu.status=1`;

  return mysqlDB.one(sql, queryParams);
}

module.exports = {
  getUserDataByUserNameSql,
};
