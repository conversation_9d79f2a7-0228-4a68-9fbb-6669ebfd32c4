import schedule from "node-schedule";
import moment from "moment";
import alarmBusiness from "./facade/app/business/report_alarm_business";
import axios from "axios";
import { parseRego } from "./foundation/service/map";
import { md5 } from "utility";
import { Repository } from "typeorm";
import { Car } from "./domain/entity/Car";
import { Organ } from "./domain/entity/Organ";
import { injectable, inject } from "inversify";

interface Alarm {
  organName: string;
  id: null;
  plateNo: string;
  imei: string;
  deviceName: string;
  alarmId: number;
  alarmType: number;
  alarmTime: string;
  lat: number;
  lon: number;
  alarmAddress: string;
  locationType: number;
  handleStatus: number;
  handleUser: string;
  handleTime: string;
  alarmContent: string;
  isAlarmPhone: number;
  isAlarmSms: number;
  isAlarmWechat: number;
  alarmPhone: string;
  alarmSms: string;
  alarmWechatName: string;
  alarmWechatMsgId: string;
  alarmPhoneTypes: string;
  alarmSmsTypes: string;
  alarmWechatTypes: string;
  alarmTypeName: string;
  locationTypeName: string;
  handleStatusName: string;
  currentAddress: string;
  alarmTypes: any[];
}

@injectable()
export default class {
  @inject("env")
  env: any;

  @inject("carRepository")
  carRepository: Repository<Car>;

  @inject("organRepository")
  organRepository: Repository<Organ>;

  /**
   * 告警通知第三方
   * @param plateNo
   * @param info
   */
  async alarmToThirdParty(plateNo: string, info: any) {
    const device = await this.carRepository.findOne({ where: { plateNo } });
    if (!device) return;
    const organ = await this.organRepository.findOne(device.organId);
    if (!organ) return;
    if (!organ.alarmServer || organ.alarmServer === "") return;

    const data = JSON.stringify(info);
    const sign = md5(`${organ.alarmServer}:${data}:${organ.secret}`);

    try {
      const result = await axios.post(
        organ.alarmServer,
        { ...info },
        {
          headers: { "x-content-sign": sign },
        }
      );
      console.log("第三方告警:", result.data);
    } catch (e) {
      const { data, status, headers } = e.response;
      console.log("第三方出错:", data, status, headers);
    }
  }

  /**
   * 告警信息处理
   * @param alarm
   */
  async handleAlarm(alarm: Alarm) {
    if (!alarm.lat || !alarm.lon) return false;
    if (!alarm.alarmType) return false;

    try {
      const regeocode = await parseRego(alarm.lon, alarm.lat);
      const format_address = regeocode.formatted_address;

      if (format_address) {
        const alarUrl = "http://wx.350gps.cn/api/index/addTaskAction";
        const body: any = {
          platenum: alarm.plateNo, // 车牌号
          address: format_address,
          source: "GPS报警平台",
          alarminfo: alarm.alarmTypeName,
        };

        // 第三方告警
        // this.alarmToThirdParty(alarm.plateNo, body);

        // 手机
        if (
          !alarm.alarmPhoneTypes.length ||
          alarm.alarmPhoneTypes.split(",").includes(alarm.alarmType.toString())
        ) {
          if (alarm.isAlarmPhone && alarm.alarmPhone) {
            body.type = "Phone";
            body.targetAddress = alarm.alarmPhone;
            const result = await axios.get(alarUrl);
            console.log("电话告警:", result.data);
          }
        }
        // 短信
        if (
          !alarm.alarmSmsTypes.length ||
          alarm.alarmSmsTypes.split(",").includes(alarm.alarmType.toString())
        ) {
          if (alarm.isAlarmSms && alarm.alarmSms) {
            body.type = "Sms";
            body.targetAddress = alarm.alarmSms;
            const result = await axios.get(alarUrl);
            console.log("短信告警:", result.data);
          }
        }
        // 微信
        // if (
        //   !alarm.alarmWechatTypes.length ||
        //   alarm.alarmWechatTypes.split(",").includes(alarm.alarmType.toString())
        // ) {
        //   if (alarm.isAlarmWechat && alarm.alarmWechatMsgId) {
        //     body.type = "Wechat";
        //     body.targetAddress = alarm.alarmWechatMsgId;
        //     const result = await axios.post(alarUrl, body);
        //     console.log("微信告警:", result.data);
        //   }
        // }
      }
    } catch (err) {
      console.log("告警失败:", err.message);
    }
  }

  /**
   * 执行操作
   */
  async exec() {
    console.log("执行告警任务:" + new Date());

    // 生成请求参数
    let queryParams: any = {
      organId: 10,
      hasSubOrgan: 1,
      imei: "",
      alarmTypes: "",
      startTime: "",
      endTime: "",
      pagination: false,
      offset: 0,
      isAlarm: 1,
    };

    // 测试环境, 调试模式
    if (Number(this.env.ALAR_ENV_DEV) > 0) {
      queryParams.pagination = true;
      queryParams.currentPage = 0;
      queryParams.pageSize = 1;
      queryParams.startTime = "2020-01-01 00:01";
      queryParams.endTime = "2020-12-31 23:59";
    } else {
      queryParams.startTime = moment()
        .add(parseInt(this.env.ALAR_SCHEDULE_MIN), "minutes")
        .format("YYYY-MM-DD HH:mm");
      queryParams.endTime = moment().format("YYYY-MM-DD HH:mm");
    }

    if (this.env.ALAR_TYPES) {
      queryParams.alarmTypes = this.env.ALAR_TYPES.split(",");
    }

    // 查找告警数据
    const { rows } = await alarmBusiness.getAlarmReportListLogic(queryParams);
    rows.forEach((alarm: Alarm) => this.handleAlarm(alarm));
  }

  /**
   * 任务启动
   */
  run() {
    console.log(
      "定时任务报警启动: ",
      moment().format("YYYY-MM-DD HH:mm:ss"),
      this.env.ALAR_SCHEDULE_STR
    );
    // 每分钟的第30秒定时执行一次:
    schedule.scheduleJob(this.env.ALAR_SCHEDULE_STR, () => this.exec());
  }
}
