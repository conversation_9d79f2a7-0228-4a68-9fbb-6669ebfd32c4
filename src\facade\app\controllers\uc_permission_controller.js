const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const staticConfig = require("../config/config_static");
const permissionBusiness = require("../business/uc_permission_business");

/**
 * @api {GET} /ucpermission/getpermissionmenulist 权限管理-获取权限二级目录列表
 * @apiName getpermissionmenulist
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-11
 *
 * @apiParam  {Number} systemType 所属系统
 *
 * @apiParamExample  {json} 请求示例:
  Content-Type:application/json;charset=utf-8
  {
  }
 * @apiSuccessExample {Object} 响应示例:
  HTTP/1.1 200 OK
  {
    errcode : 0,
    errmsg : '操作成功',
    retobj : [
    {
      "id": 1,
      "perId": 1,
      "parentId": 0,
      "perCode": "",
      "perName": "厨盟用户中心",
      "path": "0,1",
      "perUrl": "",
      "perIcon": "",
      "perType": "",
      "sort": 0,
      "systemType": 1,
      "status": 1,
      "remark": null,
      "createUser": null,
      "createTime": null,
      "children": [
        {
          "id": 11,
          "perId": 11,
          "parentId": 1,
          "perCode": "",
          "perName": "系统设置",
          "path": "0,1,11",
          "perUrl": "",
          "perIcon": "",
          "perType": "MENU",
          "sort": 0,
          "systemType": 1,
          "status": 1,
          "remark": null,
          "createUser": null,
          "createTime": null
        }
      ]
    ]
  }
 */
exports.getPermissionMenuList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-获取权限树列表",
  buttonPerm: "UP-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const systemType = validator.isIntFormat(ctx.request.query.systemType, {
        min: 1,
      })
        ? ctx.request.query.systemType
        : -1;

      const treeData = await permissionBusiness.getPermissionMenuTree(
        systemType
      );
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        treeData
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getPermissionMenuList",
        "获取权限二级目录列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {GET} /ucpermission/getpermissionbuttonlist 权限管理-获取权限按钮列表
 * @apiName getpermissionbuttonlist
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-12
 *
 * @apiParam  {Number} parentId 父Id
 *
 * @apiParamExample  {json} 请求示例:
 Content-Type:application/json;charset=utf-8
 {
 }
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
   errcode : 0,
   errmsg : '操作成功',
   retobj : [
   {
     "id": 1,
     "perId": 1,
     "parentId": 0,
     "perCode": "",
     "perName": "厨盟用户中心",
     "path": "0,1",
     "perUrl": "",
     "perIcon": "",
     "perType": "",
     "sort": 0,
     "systemType": 1,
     "status": 1,
     "remark": null,
     "createUser": null,
     "createTime": null
   ]
 }
*/
exports.getPermissionButtonList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-获取权限按钮列表",
  buttonPerm: "UP-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const parentId = validator.isIntFormat(ctx.request.query.parentId, {
        min: 1,
      })
        ? ctx.request.query.parentId
        : -1;
      const buttonList = await permissionBusiness.getPermissionButtonList(
        parentId
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        buttonList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getPermissionButtonList",
        "获取权限按钮列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {POST} /ucpermission/insertpermission 权限管理-添加权限
 * @apiName insertpermission
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-13
 *
 * @apiParam  {Number} parentId 父Id
 * @apiParam  {String} perCode 权限编码
 * @apiParam  {String} perName 权限名称 必需值
 * @apiParam  {String} path 路径 必需值
 * @apiParam  {String} perUrl 权限地址 必需值
 * @apiParam  {String} perIcon 权限图标
 * @apiParam  {String} perType 权限类型 必需值
 * @apiParam  {Number} sort 排序
 * @apiParam  {Number} systemType 所属系统 必需值
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 * @apiParam  {String} remark 备注
 *
 * @apiParamExample  {json} 请求示例:
    Content-Type:application/json;charset=utf-8
    {
      "parentId": 15,
      "perCode": "",
      "perName": "xxx",
      "path": "0,1,11,15",
      "perUrl": "/log/index",
      "perIcon": "",
      "perType": "BUTTON",
      "sort": "2",
      "systemType": 1,
      "status": 1,
      "remark": ""
    }
 *
 * @apiSuccessExample {Object} 响应示例:
    HTTP/1.1 200 OK
    {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
    }
 */
exports.insertPermission = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-添加权限",
  buttonPerm: "UP-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      await permissionBusiness.insertPermissionLogic(
        requestData,
        currentUser.loginName
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertPermission",
        "新增权限表记录成功：",
        JSON.stringify(requestData)
      );

      await global.context.redisClient.del(
        staticConfig.redisPrename.permission
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertPermission",
          "新增权限表记录失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertPermission",
        "新增权限表记录时数据库出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucpermission/updatepermission 权限管理-更新权限
 * @apiName updatepermission
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-14
 *
 * @apiParam  {Number} perId 权限Id 必需值
 * @apiParam  {Number} parentId 父Id
 * @apiParam  {String} perCode 权限编码
 * @apiParam  {String} perName 权限名称 必需值
 * @apiParam  {String} path 路径 必需值
 * @apiParam  {String} perUrl 权限地址 必需值
 * @apiParam  {String} perIcon 权限图标
 * @apiParam  {String} perType 权限类型 必需值
 * @apiParam  {Number} sort 排序
 * @apiParam  {Number} systemType 所属系统 必需值
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 * @apiParam  {String} remark 备注
 *
 * @apiParamExample  {json} 请求示例:
    Content-Type:application/json;charset=utf-8
    {
      "perId": 10,
      "parentId": 15,
      "perCode": "",
      "perName": "xxx",
      "path": "0,1,11,15",
      "perUrl": "/log/index",
      "perIcon": "",
      "perType": "BUTTON",
      "sort": "2",
      "systemType": 1,
      "status": 1,
      "remark": ""
    }
 *
 * @apiSuccessExample {Object} 响应示例:
    HTTP/1.1 200 OK
    {
      errcode : 0,
      errmsg : '操作成功',
      retobj : { changedRows: 1 }
    }
 */
exports.updatePermission = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-更新权限",
  buttonPerm: "UP-14",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      if (!validator.isIntFormat(`${requestData.perId}`, { min: 1 })) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_ID_ERROR
        );
        return;
      }

      await permissionBusiness.updatePermissionLogic(
        requestData,
        currentUser.loginName
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updatePermission",
        "更新权限表记录成功：",
        JSON.stringify(requestData)
      );

      await global.context.redisClient.del(
        staticConfig.redisPrename.permission
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updatePermission",
          "更新权限表记录失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updatePermission",
        "更新权限表记录时数据库出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /ucpermission/refreshpermission 权限管理-刷新权限
 * @apiName refreshpermission
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-15
 *
 * @apiSuccessExample {Object} 响应示例:
    HTTP/1.1 200 OK
    {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
    }
 */
exports.refreshPermission = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-刷新权限",
  buttonPerm: "UP-15",
  handler: async (ctx) => {
    await global.context.redisClient.del(staticConfig.redisPrename.permission);
    ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
  },
};

/**
 * @api {POST} /ucpermission/batchinsertpermission 权限管理-批量添加权限
 * @apiName batchinsertpermission
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-16
 *
 * @apiParam  {Number} parentId 父Id
 * @apiParam  {String} path 路径 必需值
 * @apiParam  {Number} systemType 所属系统 必需值
 * @apiParam  {Array} routeList:[
 *    {perName 权限名称 必需值, perUrl权限地址 必需值, buttonPerm: 按钮权限值}
 * ] 路由列表 必需值 详见请求示例
 *
 * @apiParamExample  {json} 请求示例:
    Content-Type:application/json;charset=utf-8
    {
      "parentId": 15,
      "path": "0,1,3,4",
      "systemType": 1,
      "routeList": [
        {"perName":"获取员工列表","perUrl":"/user/getuserlist", "buttonPerm": ""}
      ]
    }
 *
 * @apiSuccessExample {Object} 响应示例:
    HTTP/1.1 200 OK
    {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
    }
 */
exports.batchInsertPermission = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-批量添加权限",
  buttonPerm: "UP-16",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      await permissionBusiness.batchInsertPermissionLogic(
        requestData,
        currentUser.loginName
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "batchInsertPermission",
        "批量新增权限表记录成功：",
        JSON.stringify(requestData)
      );

      await global.context.redisClient.del(
        staticConfig.redisPrename.permission
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "batchInsertPermission",
          "批量新增权限表记录失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "batchInsertPermission",
        "批量新增权限表记录时数据库出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucpermission/setpermissionenable 权限管理-设置启用禁用
 * @apiName setpermissionenable
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-17
 *
 * @apiParam  {Number} perId 权限Id 必需值
 * @apiParam  {Number=0,1} status 状态 必需值
 *
 * @apiParamExample  {json} 请求示例:
   Content-Type:application/json;charset=utf-8
   {
    "perId" : "20",
    "status" : "1"
   }
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": ""
   }
 */
exports.setPermissionEnable = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-设置启用禁用",
  buttonPerm: "UP-17",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      if (
        !(
          validator.isIntFormat(requestData.perId, { min: 1 }) &&
          validator.isInt01(requestData.status)
        )
      ) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_FIELD_ERROR
        );
        return;
      }

      await permissionBusiness.setPermissionEnableLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "setPermissionEnable",
        "设置权限启用禁用成功，",
        JSON.stringify(requestData)
      );

      await global.context.redisClient.del(
        staticConfig.redisPrename.permission
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "setPermissionEnable",
          "设置权限启用禁用失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "setPermissionEnable",
        "设置权限启用禁用出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucpermission/deletebuttontypepermission 权限管理-删除按钮权限
 * @apiName deletebuttontypepermission
 * @apiGroup UcPermission
 * @apiVersion  1.0.0
 * @apiPermission UP-18
 *
 * @apiParam  {Array} perIds 权限Id列表 必需值
 *
 * @apiParamExample  {json} 请求示例:
   Content-Type:application/json;charset=utf-8
   {
     "perIds" : [20]
   }
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": ""
   }
 */
exports.deleteBatchPermission = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "权限管理-删除按钮权限",
  buttonPerm: "UP-18",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      if (!validator.isIntArray(requestData.perIds)) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_FIELD_ERROR
        );
        return;
      }

      const deleteResult = await permissionBusiness.deleteBatchPermissionLogic(
        requestData.perIds
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteButtonTypePermission",
        "删除按钮权限成功，",
        JSON.stringify(deleteResult)
      );

      await global.context.redisClient.del(
        staticConfig.redisPrename.permission
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteButtonTypePermission",
          "删除按钮权限失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteButtonTypePermission",
        "删除按钮权限出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
