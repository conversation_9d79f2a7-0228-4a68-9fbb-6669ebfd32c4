module.exports = {
  logSource: "GPS-BACK",
  apiReqConfig: {
    GPSMONITORAPP: {
      apikey: { key: "apikey", val: "" },
      secretkey: { key: "secretkey", val: "" },
      timestamp: { key: "timestamp", val: "X" },
      sign: { key: "sign", val: "" },
      gatherMethod: "both",
      gatherSign: "=",
      urlencode: true,
      returnFormat: { errcode: "errcode", errmsg: "errmsg", retobj: "retobj" },
    },
    SHOWAPI: {
      reqBaseUrl: "https://route.showapi.com/",
      apikey: { key: "showapi_appid", val: "38698" },
      secretkey: {
        key: "showapi_secretkey",
        val: "53bb05ddd8cd4b249e54b71388773094",
      },
      timestamp: { key: "showapi_timestamp", val: "YYYYMMDDHHmmss" },
      sign: { key: "showapi_sign", val: "" },
      gatherMethod: "right",
      gatherSign: "",
      urlencode: false,
      returnFormat: {
        errcode: "showapi_res_code",
        errmsg: "showapi_res_error",
        retobj: "showapi_res_body",
      },
    },
    GEOCODER: {
      reqBaseUrl: "http://apis.map.qq.com/ws/geocoder/v1/",
      apikey: { key: "key", val: "please input key" },
      secretkey: { key: "", val: "" },
      timestamp: { key: "", val: "" },
      sign: { key: "sign", val: "" },
      gatherMethod: "",
      gatherSign: "",
      urlencode: false,
      returnFormat: { errcode: "status", errmsg: "message", retobj: "result" },
    },
  },
  redisChannel: {
    template: "w_temp",
    command: "topic_device_command",
    fence: "topic_device_fence",
  },
  apiTokenConfig: [
    {
      apikey: "",
      secretkey: "",
      sysName: "redis-listener",
    },
  ],
  apiReqPath: {
    showapiHzzpy: "99-38",
  },
};
