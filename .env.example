# 修改node默认时区
SERVICE_STATUS="dev"
TZ="Asia/Shanghai"

# 数据库
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"
MYSQL_DATABASE="gps_monitor"
DEVICE_STATE_TIME="300"

# redis
REDIS_HOST="localhost"
REDIS_PASS=""
# mongo
MONGO_HOST="localhost"
MONGO_USER="root"
MONGO_PASSWORD="123456"
MONGO_DATABASE="gps_monitor"

# 报警相关
ALAR_ENV_DEV="1"
# ALAR_TYPES=21,24,27
# 报警定时任务规则
ALAR_SCHEDULE_STR="0 * * * * *"
# startTIme前多少时间
ALAR_SCHEDULE_MIN="-1"