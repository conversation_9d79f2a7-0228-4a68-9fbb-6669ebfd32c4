const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");

const stopDao = require("../lib/dao/report_stop_dao");

/**
 * 获取停车记录
 * 请求参数:
 "organId": "机构Id",
 "imei": "IMEI号",
 "deviceType": "设备类型",
 "deviceModel": "设备型号",
 "stopSecond": "大于N秒",
 "startSecond": "小于N秒",
 "pagination": true,
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getStopListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: -1 } }],
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "" },
    {
      field: "deviceType",
      title: "设备类型",
      rule: "isIntFormat",
      msg: "",
      required: true,
    },
    {
      field: "deviceModel",
      title: "设备型号",
      rule: "isIntFormat",
      msg: "",
      required: true,
    },
    {
      field: "stopSecond",
      title: "停留时间",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.startSecond = queryParams.startSecond || "";
  const [stopList, keyValConfig] = await Promise.all([
    stopDao.getStopReportSqlList(queryParams, currentUser),
    global.context.getPropertymapKeyVal(["DEVICE_TYPE", "DEVICE_MODEL"]),
  ]);

  stopList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.stopStartTime = e.stopStartTime
      ? moment.unix(e.stopStartTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.stopSecond = e.stopSecond ? commFunc.secondsToHumanize(e.stopSecond) : "";
  });

  return stopList;
}

/**
 * 获取停车记录
 * 请求参数:
 "organId": "机构Id",
 "endSecond": "小于N秒",
 "startSecond": "大于N秒",
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getHomeStopListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "startSecond",
      title: "开始时间",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "endSecond",
      title: "结束时间",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [stopList, keyValConfig] = await Promise.all([
    stopDao.getHomeStopReportSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "ATTENTION",
    ]),
  ]);

  stopList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.attentionName = keyValConfig.ATTENTION[e.attention] || "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.stopStartTime = e.stopStartTime
      ? moment.unix(e.stopStartTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.stopSecond = e.stopSecond ? commFunc.secondsToHumanize(e.stopSecond) : "";
  });

  return stopList;
}

module.exports = {
  getStopListLogic,
  getHomeStopListLogic,
};
