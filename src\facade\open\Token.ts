import jwt from "jsonwebtoken";
import { Repository } from "typeorm";
import { injectable, inject } from "inversify";
import { Config } from "../../config";
import { Organ } from "../../domain/entity/Organ";
import { Context, Token } from "../../types";

/**
 * 授权接口
 */
@injectable()
export default class {
  @inject("config")
  config: Config;

  @inject("organRepository")
  organRepository: Repository<Organ>;

  /**
   * 创建访问凭据
   * @param ctx
   */
  async create(ctx: Context) {
    // 参数校验
    if (!ctx.request.body) {
      ctx.body = {
        error: "参数不为空",
      };
      ctx.status = 400;
      return;
    }
    const { id, secret } = ctx.request.body;
    if (!id || !secret) {
      ctx.body = { error: "参数缺失" };
      ctx.status = 400;
      return;
    }

    // 查找组织
    const organ = await this.organRepository.findOne(id);
    if (!organ) {
      ctx.body = { error: "应用不存在" };
      ctx.status = 400;
      return;
    }

    // 无密钥判定
    if (!organ.secret || organ.secret === "") {
      ctx.body = {
        error: "应用未启用OpenAPI",
      };
      ctx.status = 403;
      return;
    }

    // 密钥检查
    if (organ.secret !== secret) {
      ctx.body = { error: "密钥错误" };
      ctx.status = 403;
      return;
    }

    // 证书签发
    const now = Math.floor(Date.now() / 1000);
    const sign: Token = {
      iss: "OpenApiServer",
      sub: "OpenApiUser",
      aud: organ.id,
      exp: now + 7200,
      nbf: now,
      iat: now,
    };

    // 返回结果
    const token = jwt.sign(sign, this.config.security.secret);
    ctx.body = { token, expired_at: now + 7200 };
  }
}
