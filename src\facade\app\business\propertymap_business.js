const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const { env } = require("process");
const staticConfig = require("../config/config_static");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");
const propertymapDao = require("../lib/dao/propertymap_dao");

// 取缓存数据
async function _getPropertymapCacheList() {
  let propertymapCache = await global.context.redisClient.get(
    staticConfig.redisPrename.propertymap
  );
  propertymapCache = JSON.parse(propertymapCache || "[]");
  if (propertymapCache.length > 0) {
    return propertymapCache;
  }

  let propertymapSql = await propertymapDao.getPropertymapSqlList({
    status: 1,
    code: "",
    pagination: false,
  });
  propertymapSql = propertymapSql.rows.map((pm) => ({
    id: pm.id,
    code: pm.code,
    key: pm.key,
    val: pm.val,
    name: pm.name,
    parentId: pm.parentId,
    sort: pm.sort,
    remark: pm.remark,
  }));

  // 拆分设备
  const deviceTypeList = propertymapSql.filter((f) => f.code === "DEVICE_TYPE");
  const modelTypeList = propertymapSql
    .filter((f) => f.code === "DEVICE_MODEL")
    .map((m) => {
      const deviceType = deviceTypeList.find((fd) => m.parentId === fd.id);
      return {
        id: global.context.idGenerator.nextId(),
        code: "DEVICE_MODEL_TYPE",
        key: m.key,
        val: deviceType ? deviceType.key : 0,
        name: "",
        parentId: 0,
        sort: m.sort,
        remark: "",
      };
    });
  propertymapSql = propertymapSql.concat(modelTypeList);
  await global.context.redisClient.set(
    staticConfig.redisPrename.propertymap,
    propertymapSql
  );
  return propertymapSql;
}

// 通过code取键值对
async function getPropertymapKeyValLogic(codes) {
  try {
    const propertymapCache = await _getPropertymapCacheList();
    const propertymapData = {};
    for (const code of codes) {
      const propertymapVal = {};
      propertymapCache
        .filter((f) => f.code === `${code}`)
        .forEach((f) => {
          propertymapVal[f.key] = f.val;
        });
      propertymapData[code] = propertymapVal;
    }
    return propertymapData;
  } catch (err) {
    global.context.logger.error(
      "SYSTEM",
      "SYS",
      "getPropertymapValLogic",
      "获取属性映射键值对缓存值出错:",
      err
    );
    return {};
  }
}

// 通过code取名键对
async function getPropertymapNameKeyLogic(codes) {
  try {
    const propertymapCache = await _getPropertymapCacheList();
    const propertymapData = {};
    for (const code of codes) {
      const propertymapVal = {};
      propertymapCache
        .filter((f) => f.code === `${code}`) //  && validator.isEnglishLetter(f.name)
        .forEach((f) => {
          propertymapVal[f.name] = f.key;
        });
      propertymapData[code] = propertymapVal;
    }
    return propertymapData;
  } catch (err) {
    global.context.logger.error(
      "SYSTEM",
      "SYS",
      "getPropertymapNameKeyLogic",
      "获取属性映射名键对缓存值出错:",
      err
    );
    return {};
  }
}

// 通过code取键名对
async function getPropertymapKeyIdenLogic(codes) {
  try {
    const propertymapCache = await _getPropertymapCacheList();
    const propertymapData = {};
    for (const code of codes) {
      const propertymapVal = {};
      propertymapCache
        .filter(
          (f) => f.code === `${code}` && validator.isEnglishLetter(f.name)
        )
        .forEach((f) => {
          propertymapVal[f.key] = `${code}.${f.name}`;
        });
      propertymapData[code] = propertymapVal;
    }
    return propertymapData;
  } catch (err) {
    global.context.logger.error(
      "SYSTEM",
      "SYS",
      "getPropertymapKeyIdenLogic",
      "获取属性映射键名对缓存值出错:",
      err
    );
    return {};
  }
}

// 获取属性映射下拉列表
async function getPropertymapDropDownListLogic(codes) {
  try {
    const propertymapCache = await _getPropertymapCacheList();
    const propertymapData = {};
    for (const code of codes) {
      propertymapData[code] = propertymapCache
        .filter((f) => f.code === `${code}`)
        .map((m) => ({
          name: m.name,
          key: m.key,
          val: m.val,
          sort: m.sort,
          remark: m.remark,
        }));
    }

    if (`,${codes},`.indexOf(",KITCHENCLASSTREE,") !== -1) {
      const rootNode = commFunc.buildTreeData(propertymapCache, {
        id: 0,
        parentId: 0,
        children: [],
      });
      propertymapData.KITCHENCLASSTREE = rootNode.children.filter(
        (f) => f.code === "KITCHENCLASSA"
      );
    }

    if (`,${codes},`.indexOf(",KITCHENAREATREE,") !== -1) {
      const rootNode = commFunc.buildTreeData(propertymapCache, {
        id: 0,
        parentId: 0,
        children: [],
      });
      propertymapData.KITCHENAREATREE = rootNode.children.filter(
        (f) => f.code === "KITCHENAREAA"
      );
    }

    return propertymapData;
  } catch (err) {
    global.context.logger.error(
      "SYSTEM",
      "SYS",
      "getPropertymapDropDownListLogic",
      "获取属性映射下拉列表缓存值出错:",
      err
    );
    return {};
  }
}

// 获取属性映射列表
async function getPropertymapListLogic(queryParams) {
  const propertymapList = await propertymapDao.getPropertymapSqlList(
    queryParams
  );

  propertymapList.rows.forEach((f, i) => {
    f.index = queryParams.offset + i + 1;
    f.createTime = f.createTime
      ? moment(f.createTime).format("YYYY-MM-DD HH:mm")
      : "";
    f.updateTime = f.updateTime
      ? moment(f.updateTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return propertymapList;
}

function _validatePropertymapData(requestData) {
  const rules = [
    {
      field: "parentId",
      title: "属性父Id",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
    },
    {
      field: "sort",
      title: "排序",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
    },

    { field: "val", title: "属性值", rule: [], msg: "", required: true },
    { field: "key", title: "属性键", rule: [], msg: "", required: true },
    {
      field: "code",
      title: "属性编码",
      rule: [{ name: "isEnglishLetter" }],
      msg: "值必须为英文字母或下划线",
    },
    {
      field: "status",
      title: "状态",
      rule: [{ name: "isInt01" }],
      msg: "",
      required: true,
    },

    {
      field: "id",
      title: "属性Id",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
      opt: "u",
    },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  model.name = model.name || "";
  model.parentId = model.parentId || 0;
  model.sort = model.sort || 0;
  model.remark = model.remark || "";

  return [undefined, model];
}

// 新增属性映射数据
async function insertPropertymapLogic(insertData, userName) {
  insertData.opt = "i";
  const [errmsg, insertModel] = _validatePropertymapData(insertData);
  if (errmsg) {
    throw new RetJson(i18n.SYS_ERROR_CODE, errmsg);
  }

  insertModel.userName = userName;

  const countResult = await propertymapDao.getPropertymapRecordSql(insertData);
  if (countResult && countResult.record > 0) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  await propertymapDao.insertPropertymapSql(insertData);
  await global.context.redisClient.del(staticConfig.redisPrename.propertymap);
  await _getPropertymapCacheList();
}

// 更新属性映射数据
async function updatePropertymapLogic(updateData, userName) {
  updateData.opt = "u";
  const [errmsg, updateModel] = _validatePropertymapData(updateData);
  if (errmsg) {
    throw new RetJson(i18n.SYS_ERROR_CODE, errmsg);
  }
  updateModel.userName = userName;

  const countResult = await propertymapDao.getPropertymapRecordSql(updateData);
  if (
    countResult &&
    countResult.record > 0 &&
    Number.parseInt(updateModel.id, 10) !== countResult.id
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  await propertymapDao.updatePropertymapSql(updateData);
  await global.context.redisClient.del(staticConfig.redisPrename.propertymap);
  await _getPropertymapCacheList();
  return "";
}

// 刷新属性映射数据
async function refreshPropertymapLogic() {
  await global.context.redisClient.del(staticConfig.redisPrename.propertymap);
  await _getPropertymapCacheList();
}

module.exports = {
  getPropertymapKeyValLogic,
  getPropertymapNameKeyLogic,
  getPropertymapKeyIdenLogic,
  getPropertymapDropDownListLogic,

  getPropertymapListLogic,
  insertPropertymapLogic,
  updatePropertymapLogic,
  refreshPropertymapLogic,
};
