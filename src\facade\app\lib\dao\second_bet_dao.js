const mysqlDB = require("../common/mysql_pool");

function getSecondBetSqlList(queryParams) {
  const sql = `select gsb.id second_bet_id,gsb.organ_id,gsb.second_bet_name,gsb.second_bet_type,gsb.danger_level,
  gsb.shape_type,gsb.shape_scope,gsb.shape_data,gsb.address,gsb.stop_minutes,gsb.data_source,
  gsb.is_share,gsb.remark,gsb.create_user,gsb.create_time,uo.organ_name
  from gps_second_bet gsb
  join uc_organ uo on gsb.organ_id=uo.id
  where (:search_key='' or gsb.second_bet_name like concat('%',:search_key,'%')) 
  and gsb.organ_id=:organ_id and (:is_share=-1 or gsb.is_share=:is_share)
  and (:second_bet_type=-1 or gsb.second_bet_type=:second_bet_type) 
  and (:danger_level=-1 or gsb.danger_level=:danger_level)
  and (:shape_type=-1 or gsb.shape_type=:shape_type)`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertSecondBetSql(insertData) {
  const sql = `insert into gps_second_bet(organ_id,second_bet_name,second_bet_type,danger_level,shape_type,
  shape_scope,shape_data,address,stop_minutes,data_source,is_share,remark,create_user,create_time)
  values(:organ_id,:second_bet_name,:second_bet_type,:danger_level,:shape_type,:shape_scope,:shape_data,:address,
  :stop_minutes,:data_source,:is_share,:remark,:user_name,now())`;

  return mysqlDB.insert(sql, insertData);
}

function updateSecondBetSql(updateData) {
  const sql = `update gps_second_bet 
  set second_bet_name=:second_bet_name,second_bet_type=:second_bet_type,danger_level=:danger_level,
  shape_scope=:shape_scope,shape_data=:shape_data,address=:address,stop_minutes=:stop_minutes,
  data_source=:data_source,is_share=:is_share,remark=:remark,update_user=:user_name,update_time=now()
  where id=:second_bet_id`;

  return mysqlDB.update(sql, updateData);
}

function deleteSecondBetSql(deleteData) {
  const sql = `delete from gps_second_bet where id in(${deleteData.secondBetIds.join(
    ","
  )})`;

  return mysqlDB.del(sql, deleteData);
}

// 二押点绑定设备
function getOrganBetDeviceSqlList(queryParams) {
  const sql = `select gd.imei,gd.plate_no,gd.device_name,gc.car_owner
  from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  left join gps_second_bet_device gsbd on gsbd.imei=gd.imei
  where gd.is_settle=0 and gd.is_delete=0 and gd.organ_id in (${queryParams.organIds}) and isnull(gsbd.imei)
  order by gd.plate_no,gd.imei`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getSecondBetDeviceSqlList(queryParams) {
  const sql = `select gsbd.id bet_device_id,gd.plate_no,gsbd.imei,gd.device_name,gc.car_owner
  from gps_second_bet_device gsbd
  join gps_device gd on gsbd.imei=gd.imei and gd.is_settle=0 and gd.is_delete=0
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertSecondBetDeviceSql(insertList) {
  const sqlTasks = [];

  const sql = `insert into gps_second_bet_device(imei)
  values(:imei)`;

  for (const insertData of insertList) {
    sqlTasks.push({ sql: sql, params: insertData });
  }

  return mysqlDB.executeTransaction(sqlTasks);
}

function deleteSecondBetDeviceSql(deleteData) {
  const sql = `delete from gps_second_bet_device where id in(${deleteData.betDeviceIds.join(
    ","
  )})`;

  return mysqlDB.del(sql, deleteData);
}

module.exports = {
  getSecondBetSqlList,
  insertSecondBetSql,
  updateSecondBetSql,
  deleteSecondBetSql,

  getOrganBetDeviceSqlList,
  getSecondBetDeviceSqlList,
  insertSecondBetDeviceSql,
  deleteSecondBetDeviceSql,
};
