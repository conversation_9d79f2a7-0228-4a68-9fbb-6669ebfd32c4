import { createConnection } from "typeorm";
import delay from "delay";
import { Organ } from "../../domain/entity/Organ";
import { Car } from "../../domain/entity/Car";
import { Device } from "../../domain/entity/Device";
import { Config } from "../../config";
import { Container } from "inversify";
import { Location } from "../../domain/entity/Location";
import { Alarm } from "../../domain/entity/Alarm";

/**
 * 连接数据库
 * @param option
 */
const connect = async (option: any) => {
  let times = 1;
  while (times++) {
    try {
      const conn = await createConnection(option);
      const { host, port, database } = option;
      console.log("[连接成功]", `${host}:${port}`, database);
      return conn;
    } catch (err) {
      console.log("[连接失败]", times, err, option);
      if (times > 6) throw err;
      await delay(3000);
    }
  }
};

export default async (container: Container) => {
  // 连接数据库
  const config = container.get<Config>("config");
  const [mysql, mongo] = await Promise.all([
    connect({
      name: "mysql",
      type: "mysql",
      ...config.database,
      entities: [Organ, Car, Device, Alarm],
    }),
    connect({
      name: "mongo",
      type: "mongodb",
      ...config.mongo,
      authSource: "admin",
      autoReconnect: true,
      poolSize: 10,
      logging: "all",
      logger: "advanced-console",
      entities: [Location],
    }),
  ]);

  // 注册数据管理器
  const mysqlManager = mysql.manager;
  container.bind("mysqlManager").toConstantValue(mysqlManager);
  const mongoManager = mongo.mongoManager;
  container.bind("mongoManager").toConstantValue(mongoManager);

  // 注册mysql仓库
  container
    .bind("organRepository")
    .toConstantValue(mysqlManager.getRepository(Organ));
  container
    .bind("carRepository")
    .toConstantValue(mysqlManager.getRepository(Car));
  container
    .bind("deviceRepository")
    .toConstantValue(mysqlManager.getRepository(Device));
  container
    .bind("alarmRepository")
    .toConstantValue(mysqlManager.getRepository(Alarm));

  // 注册mongo仓库
  container
    .bind("locationRepository")
    .toConstantValue(mongoManager.getMongoRepository(Location));
};
