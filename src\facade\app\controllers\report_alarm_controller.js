const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const alarmBusiness = require("../business/report_alarm_business");

/**
 * @api {GET} /report/getalarmreportlist 报警记录-获取报警报表
 * @apiName getalarmreportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RA-10
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} alarmTypes 报警类型数组，格式：用,隔开
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 18:00
 * @apiParam  {String} endTime 结束时间，格式：2019-03-18 18:00
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getAlarmReportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-获取报警报表",
  buttonPerm: "RA-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: validator.isInt01(reqQueryParams.hasSubOrgan)
          ? Number.parseInt(reqQueryParams.hasSubOrgan)
          : 0,
        imei: (reqQueryParams.imei || "").trim(),
        handleStatus: reqQueryParams.handleStatus !== undefined ? Number.parseInt(reqQueryParams.handleStatus) : -1,
        alarmTypes: reqQueryParams.alarmTypes || "",
        startTime: reqQueryParams.startTime || "",
        endTime: reqQueryParams.endTime || "",
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, {min: 1})
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      queryParams.alarmTypes =
        queryParams.alarmTypes.length > 0
          ? queryParams.alarmTypes.split(",")
          : [];

      const alarmList = await alarmBusiness.getAlarmReportListLogic(
        queryParams,
        currentUser
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        alarmList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getAlarmReportList",
          "获取报警报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getAlarmReportList",
        "获取报警报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /report/exportalarmreportlist 报警记录-导出报警报表
 * @apiName exportalarmreportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RA-11
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} alarmTypes 报警类型数组，格式：用,隔开
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {boolean} pagination=true 分页: 默认true开启, false关闭
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportAlarmReportList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-导出报警报表",
  buttonPerm: "RA-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        imei: (requestBody.imei || "").trim(),
        alarmTypes: requestBody.alarmTypes || "",
        startTime: requestBody.startTime || "",
        endTime: requestBody.endTime || "",
        pagination: requestBody.pagination !== false || false,
        currentPage: requestBody.currentPage || 1,
        pageSize: requestBody.pageSize,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      queryParams.alarmTypes =
        queryParams.alarmTypes.length > 0
          ? queryParams.alarmTypes.split(",")
          : [];

      const alarmList = await alarmBusiness.getAlarmReportListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "设备名称",
        "设备号",
        "报警类型",
        "报警时间",
        "报警内容",
        "经度",
        "纬度",
        "报警地址",
        "定位类型",
        "处理状态",
        "处理人",
        "处理时间",
      ]);

      alarmList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.deviceName,
          e.imei,
          e.alarmTypeName,
          e.alarmTime,
          e.alarmContent,
          e.lon,
          e.lat,
          e.alarmAddress,
          e.locationTypeName,
          e.handleStatusName,
          e.handleUser,
          e.handleTime,
        ]);
      });

      const result = xlsx.build([{data: rows}]);
      const fileName = encodeURIComponent(
        `报警报表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportAlarmReportList",
          "获取报警报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportAlarmReportList",
        "获取报警报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
