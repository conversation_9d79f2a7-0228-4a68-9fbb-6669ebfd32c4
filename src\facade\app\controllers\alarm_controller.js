const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const alarmBusiness = require("../business/alarm_business");

/**
 * @api {GET} /alarm/getalarmlist 报警管理-获取报警列表
 * @apiName getalarmlist
 * @apiGroup Alarm
 * @apiVersion  1.0.0
 * @apiPermission DA-10
 *
 * @apiParam  {String} searchKey 搜索关键字，车牌号，IMEI，车组
 * @apiParam  {String} alarmTypes 报警类型
 * @apiParam  {String} alarmClass 报警类别
 * @apiParam  {String} alarmClassIden 报警类别标识
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getAlarmList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警管理-获取报警列表",
  buttonPerm: "DA-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: currentUser.organId,
        handleStatus: 0,
        alarmClass: reqQueryParams.alarmClass || -1,
        alarmClassIden: reqQueryParams.alarmClassIden || "",
        alarmTypes: reqQueryParams.alarmTypes || "",
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };

      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const alarmList = await alarmBusiness.getAlarmListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        alarmList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getAlarmList",
        "获取报警列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /alarm/exportalarmlist 报警管理-导出报警列表
 * @apiName exportalarmlist
 * @apiGroup Alarm
 * @apiVersion  1.0.0
 * @apiPermission DA-15
 *
 * @apiParam  {String} searchKey 搜索关键字，车牌号，IMEI，车组
 * @apiParam  {String} alarmTypes 报警类型
 * @apiParam  {String} alarmClass 报警类别
 * @apiParam  {String} alarmClassIden 报警类别标识
 * @apiParam  {String} alarmClassName 报警名称
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportAlarmList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警管理-导出报警列表",
  buttonPerm: "DA-15",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        searchKey: (requestBody.searchKey || "").trim(),
        organId: currentUser.organId,
        handleStatus: 0,
        alarmClass: requestBody.alarmClass || -1,
        alarmClassName: requestBody.alarmClassName || "",
        alarmTypes: requestBody.alarmTypes || "",
        pagination: false,
      };

      const alarmList = await alarmBusiness.getAlarmListLogic(queryParams);

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "车架号",
        "车主",
        "车辆备注",
        "设备号",
        "设备类型",
        "设备型号",
        "关注度",
        "关注度备注",
        "报警类型",
        "报警时间",
        "报警内容",
        "报警地址",
        requestBody.alarmClassIden === "SECONDBET" ? "二押点名" : "",
        requestBody.alarmClassIden === "REGION" ? "围栏名称" : "",
      ]);

      alarmList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.vinNo,
          e.carOwner,
          e.carRemark,
          e.imei,
          e.deviceTypeName,
          e.deviceModelName,
          e.attentionName,
          e.attentionRemark,
          e.alarmTypeName,
          e.alarmTime,
          e.alarmContent,
          e.alarmAddress,
          e.secondBetName,
          e.fenceName,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `${requestBody.alarmClassName} - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportAlarmList",
          "导出报警列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportAlarmList",
        "导出报警列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /alarm/updatealarm 报警管理-更新报警
 * @apiName updatealarm
 * @apiGroup Alarm
 * @apiVersion  1.0.0
 * @apiPermission DA-12
 *
 * @apiParam  {Array} alarmIds 报警Id列表
 * @apiParam  {String} handleUser 处理人
 * @apiParam  {String} handleMobile 处理人手机号
 * @apiParam  {String} problemType 问题类型
 * @apiParam  {String} handleRemark 处理备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateAlarm = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警管理-更新报警",
  buttonPerm: "DA-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.handleUser = requestData.handleUser || currentUser.loginName;
      requestData.handleMobile =
        requestData.handleMobile || currentUser.loginName;
      requestData.handleStatus = 1;

      const updateCount = await alarmBusiness.updateAlarmLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateAlarm",
        `更新报警成功 ${updateCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateAlarm",
          "更新报警失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateAlarm",
        "更新报警出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
