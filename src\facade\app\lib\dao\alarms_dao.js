const mysqlDB = require("../common/mysql_pool");

async function getAlarmSqlList(queryParams) {
  const now = Math.floor(Date.now()/1000);

  let leftSelect = "";
  let leftSql = "";
  if (queryParams.alarmClassIden === "REGION") {
    leftSelect = ",gf.fence_name ";
    leftSql = "left join gps_fence gf on ga.fence_id=gf.id";
  }
  if (queryParams.alarmClassIden === "SECONDBET") {
    leftSelect = ",gsb.second_bet_name ";
    leftSql = "left join gps_second_bet gsb on ga.second_bet_id=gsb.id";
  }

  const sql = `select ga.imei, ga.start_time, ga.end_time,
  ga.id alarm_id,ga.alarm_type,ga.alarm_time,ga.alarm_content,ga.alarm_address${leftSelect},ga.lon,ga.lat
  from gps_alarm_cycle ga
  ${leftSql}
  where imei in ( select gd.imei from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gc.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.is_settle=0 and gd.is_delete=0
  ${queryParams.searchKey ? " and concat(gd.plate_no,gd.imei,uo.organ_name) like concat('%','" + queryParams.searchKey + "','%')" : ""}
  ) ${
    !queryParams.alarmTypes
      ? ""
      : " and ga.alarm_type in(" + queryParams.alarmTypes + ") "
  }
  ${queryParams.handleStatus == -1 ? "" : " and ga.handle_status=" + queryParams.handleStatus}
  ${queryParams.alarmClass == -1 ? "" : " and ga.alarm_class=" + queryParams.alarmClass}
  and ga.alarm_time < ${now}
  order by ga.alarm_time desc`;

  // return mysqlDB.queryForPagination(sql, queryParams);
  const sqlLimit =
    queryParams && queryParams.hasOwnProperty("pagination") && !queryParams.pagination
      ? ""
      : " limit :offset, :pageSize";
  const rowsResult = await mysqlDB.all(sql + sqlLimit, queryParams);

  return {
    total: rowsResult.length < queryParams.pageSize ? queryParams.offset +  rowsResult.length : queryParams.offset + queryParams.pageSize * 2, // 伪造total
    rows: rowsResult || [],
  };
}

function updateAlarmSql(updateData) {
  const sqlTasks = [];

  const deviceSql = `update gps_device gd
  join gps_alarm_cycle ga on ga.imei=gd.imei and ga.id in(${updateData.alarmIds})
  set gd.alarm_type=0`;

  const alarmSql = `update gps_alarm_cycle
  set handle_status=:handle_status,handle_user=:handle_user,handle_mobile=:handle_mobile,
  problem_type=:problem_type,handle_remark=:handle_remark,handle_time=:handle_time
  where id in(${updateData.alarmIds})`;

  sqlTasks.push({sql: deviceSql, params: updateData});
  sqlTasks.push({sql: alarmSql, params: updateData});

  return mysqlDB.executeTransaction(sqlTasks);
}

function getAlarmSqlList(queryParams) {
  const sql = `select ga.imei
  from gps_alarm_cycle ga
  where ga.id in(${queryParams.alarmIds}) group by imei`;

  return mysqlDB.all(sql, queryParams);
}

function getAlarmCountSql(queryParams) {
  let whereHandleStatus = "";
  if (typeof queryParams.handleStatus == 'number') {
    whereHandleStatus = " and handle_status=:handle_status ";
  }
  const sql = `select count(ga.id) count
  from gps_alarm_cycle ga
  where ga.imei=:imei ${whereHandleStatus}`;

  return mysqlDB.one(sql, queryParams);
}

module.exports = {
  getAlarmSqlList,
  updateAlarmSql,
  getAlarmSqlList,
  getAlarmCountSql,
};
