import path from "path";
import staticConfig from "./config/config_static";
import KoaRouter from "koa-router";
import readPkg from "read-pkg";
import mountRoute from "./routes_mount";
import configRoute from "./config/config_route";
import { Context, Next } from "koa";
declare var global: any;

// 全局上下文
global.context = {};
Object.assign(global.context, require("./lib/common/global_data"));

// 读取版本
const version = `v${readPkg.sync().version}`;

// 全局配置中间件
export const globalMiddleware = async (ctx: Context, next: Next) => {
  const { socketConfig } = staticConfig;
  ctx.config = { siteTitle: "北乾GPS风险控制管理平台", version, socketConfig };
  await next();
};

// 注册路由
const router = new KoaRouter();
mountRoute.mountOutsideRoute(
  router,
  path.join(__dirname, "./controllers"),
  configRoute
);
mountRoute.mountInsideRoute(
  router,
  path.join(__dirname, "./controllers"),
  configRoute
);

// 导出路由中间件
export const routerMiddleware: any = router.routes();
export const allowedMethodsMiddleware: any = router.allowedMethods();
