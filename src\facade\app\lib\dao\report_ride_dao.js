const mysqlDB = require("../common/mysql_pool");
const {getDaoShard} = require("../utils/sharding");

function getRideReportSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.plate_no,gd.imei,gd.device_name,gr.start_time,gr.end_time,gr.total_time,gr.total_mileage
  from gps_riding gr
  inner join gps_device gd on gr.imei=gd.imei ${
    queryParams.imei === "" ? "" : "and gd.imei=:imei"
  } and gd.is_delete=0
  inner join uc_organ uo on gd.organ_id=uo.id ${
    queryParams.organId === -1 ? "" : organSql
  }
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
  }
  where gr.start_time between :start_time and :end_time
  order by gr.start_time desc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getMileageReportSqlList(queryParams) {
  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.plate_no,gd.imei,gd.device_name,gm.day_time,gm.mileage,gm.month_mileage,gm.year_mileage
  from gps_mileage gm
  inner join gps_device gd on gm.imei=gd.imei ${
    queryParams.imei === "" ? "" : "and gd.imei=:imei"
  } and gd.is_delete=0
  inner join uc_organ uo on gd.organ_id=uo.id ${
    queryParams.organId === -1 ? "" : organSql
  }
  where gm.day_time between :start_time and :end_time
  order by gm.day_time desc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

module.exports = {
  getRideReportSqlList,
  getMileageReportSqlList,
};
