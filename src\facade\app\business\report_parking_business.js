const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");

const parkingDao = require("../lib/dao/report_parking_dao");

/**
 * 获取停车记录
 * 请求参数:
 "organId": "机构Id",
 "imei": "IMEI号",
 "deviceType": "设备类型",
 "deviceModel": "设备型号",
 "parkingSecond": "大于N秒",
 "pagination": true,
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getParkingListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: -1 } }],
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "", required: true },
    {
      field: "parkingSecond",
      title: "停留时间",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [parkingList, keyValConfig] = await Promise.all([
    parkingDao.getParkingReportSqlList(queryParams),
    global.context.getPropertymapKeyVal(["DEVICE_TYPE", "DEVICE_MODEL"]),
  ]);

  parkingList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.parkingStartTime = e.parkingStartTime
      ? moment.unix(e.parkingStartTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.parkingSecond = e.parkingSecond
      ? moment.duration(e.parkingSecond, "seconds").humanize()
      : "";
  });

  return parkingList;
}

module.exports = {
  getParkingListLogic,
};
