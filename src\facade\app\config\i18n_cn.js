module.exports = {
  SYS_ERROR_MESSAGE: "系统错误，我们会尽快修复",
  SYS_SUCCESS_MESSAGE: "操作成功",
  SYS_SUCCESS_CODE: 0,
  SYS_ERROR_CODE: 1,

  SYS_LOGIN_AUTH_ERROR: "您还未登录系统，请先登录",
  SYS_PERMISSION_AUTH_ERROR: "您还没有访问权限",

  LOG_TIME_FORMAT_ERROR: "您选择的时间格式不正确",

  HTTP_REQUEST_ERROR: "HTTP请求错误",

  ACCOUNT_PASSWORD_SIMPLE: "您的新密码太简单",

  SIGN_IN_INPUT_ERROR: "登录名或密码格式错误.",
  SIGN_IN_CUSTOMER_NOT_EXIST: "客户不存在.",
  SIGN_IN_USER_NOT_EXIST: "账号不存在或被禁用.",
  SIGN_IN_PASSWORD_ERROR: "密码输入错误.",

  REQUEST_INPUT_FIELD_MUST: "必传字段",
  REQUEST_INPUT_DATA_EMPTY: "不能为空",
  REQUEST_INPUT_DATA_ERROR: "数据格式错误",
  REQUEST_INPUT_ID_ERROR: "传入Id不正确",
  REQUEST_INPUT_FIELD_ERROR: "传入字段不正确",
  REQUEST_INPUT_FIELD_DATA_ERROR: "传入字段值不正确或无效",
  REQUEST_INPUT_MISSING_PARAMETER: "缺少必传参数",
  ROUTE_PERMISSION_BUTTON_REPEAT: "生成的权限按钮重复",

  MODELS_UNDEFIND_FIELD: "验证模型的属性值错误",

  RESPONSE_DATA_OVERTWO_EXISTS: "已存在超过两条相同数据，请查正",
  RESPONSE_DATA_ALREADY_EXISTS: "数据已存在",
  RESPONSE_EXECUTE_ERROR: "系统执行操作时出错",
  RESPONSE_STATUS_NOT_ENABLE: "状态未启用",
  RESPONSE_DATA_NOT_FOUND: "未查询到相关信息",
  BEFORE_DELETE_STATUS_DISABLE: "数据删除前时请先禁用其状态",

  PROPERTYMAP_PERMISSION_CODE_ERROR: "您还没有此属性码的操作权限",

  ADDRESS_LATLON_HTTP_POINT_ERROR: "未在终端请求到您请求地址的坐标",

  RESPONSE_PERMISSION_HAS_ASSISGEND:
    "当前所选内容不可删除或已分配到角色，请设置禁用代替.",
  RESPONSE_PERMISSION_PARENT_DISABLED:
    "该节点的父节点已禁用，启用该节点，请先启用父节点.",

  BEFORE_DEL_ORGAN_DEL: "删除此车组前请先移除其名下所有车组与设备",
  CHANGE_ORGAN_PARENT: "不可将车组移至本级或它的下级车组中",

  HTTP_ADDRESS_REQUEST_ERROR: "未在终端请求到您所定位的地址",

  TRACK_TIME_FORMAT_ERROR: "轨迹查询间隔时间不能超过90天",

  BEFORE_DEL_CAR_DEL: "删除此车辆前请先移除其关联的所有设备",

  UPLOAD_NOT_FIND_FILE: "您上传的文件格式错误",
  UPLOAD_FILE_DATA_EMPTY: "您上传的文件数据不能为空",
  UPLOAD_FILE_IMEI_REPEAT: "您上传的文件中IMEI有重复",

  IMEI_NOT_COMMAND: "该设备未设置下发指令",
  COMMAND_FORMAT_ERROR: "该设备下发指令内容有误",
};
