import { Entity, PrimaryColumn, Column } from "typeorm";

@Entity({ name: "gps_device" })
export class Device {
  @PrimaryColumn()
  id: number;

  @Column({
    name: "organ_id",
    comment: "所属机构",
  })
  organId: number;

  @Column({
    name: "plate_no",
    comment: "车牌号",
  })
  plateNo: string;

  @Column({
    name: "imei",
    comment: "设备IMEI",
  })
  imei: string;

  @Column({
    name: "encryted_imei",
    comment: "加密IMEI",
  })
  encrytedImei: string;

  @Column({
    name: "device_name",
    comment: "设备名称",
  })
  name: string;

  @Column({
    name: "device_type",
    comment: "设备类型，具体数值看配置表",
  })
  type: number;

  @Column({
    name: "device_model",
    comment: "设备型号，具体数值看配置表",
  })
  model: number;

  @Column({
    name: "install_location",
    comment: "安装位置，具体数值看配置表",
  })
  installLocation: string;

  @Column({
    name: "installer",
    comment: "安装人员",
  })
  installer: string;

  @Column({
    name: "install_date",
    comment: "安装日期",
  })
  installAt: number;

  @Column({
    name: "sim_no",
    comment: "sim卡号",
  })
  simNo: string;

  @Column({
    name: "iccid",
    comment: "ICCID号",
  })
  iccId: string;

  @Column({
    name: "service_pswd",
    comment: "sim卡服务密码",
  })
  servicePassword: string;

  @Column({
    name: "sim_expire_time",
    comment: "sim卡到期日期",
  })
  simExpiredAt: number;

  @Column({
    name: "sim_active_date",
    comment: "sim激活日期",
  })
  simActiveDate: number;

  @Column({
    name: "sim_state",
    comment: "sim卡状态",
  })
  simState: number;

  @Column({
    name: "province",
    comment: "所在省份",
  })
  province: number;

  @Column({
    name: "alarm_type",
    comment: "报警类型Id",
  })
  alarmType: number;

  @Column({
    name: "last_signal_time",
    comment: "最后信号时间",
  })
  lastSignalAt: number;

  @Column({
    name: "last_location_time",
    comment: "最后定位时间",
  })
  lastLocationAt: number;

  @Column({
    name: "online_state",
    comment: "在线状态，具体数值看配置表",
  })
  onlineState: number;

  @Column({
    name: "last_online_time",
    comment: "最后在线时间",
  })
  lastOnlineAt: number;

  @Column({
    name: "last_offline_time",
    comment: "最后离线时间",
  })
  lastOfflineAt: number;

  @Column({
    name: "active_time",
    comment: "激活时间",
  })
  activeAt: number;

  @Column({
    name: "attention",
    comment: "关注度，具体数值看配置表",
  })
  attention: number;

  @Column({
    name: "attention_remark",
    comment: "关注备注",
  })
  attentionRemark: string;

  @Column({
    name: "is_settle",
    comment: "设备结清，0 否，1 是，与设备结清状态关联",
  })
  isSettle: boolean;

  @Column({
    name: "settle_remark",
    comment: "设备结清备注",
  })
  settleRemark: string;

  @Column({
    name: "settle_cancel_remark",
    comment: "撤消设备结清备注",
  })
  settleCancelRemark: string;

  @Column({
    name: "settle_time",
    comment: "结清时间",
  })
  settledAt: number;

  @Column({
    name: "is_delete",
    comment: "是否删除，0 否，1 是",
  })
  isDelete: boolean;

  @Column({
    name: "remark",
    comment: "备注",
  })
  remark: string;

  @Column({
    name: "create_user",
    comment: "创建人",
  })
  createUser: string;

  @Column({
    name: "create_time",
    comment: "创建时间",
  })
  createdAt: Date;

  @Column({
    name: "update_user",
    comment: "更新人",
  })
  updateUser: string;

  @Column({
    name: "update_time",
    comment: "更新时间",
  })
  updatedAt: Date;

  @Column({
    name: "patform_expiration_time",
    comment: "平台到期时间",
  })
  patformExpiredAt: Date;

  @Column({
    name: "is_alarm_phone",
    comment: "是否报警手机",
  })
  isAlarmPhone: boolean;

  @Column({
    name: "is_alarm_sms",
    comment: "是否报警短信",
  })
  isAlarmSms: boolean;

  @Column({
    name: "is_alarm_wechat",
    comment: "是否报警微信",
  })
  isAlarmWechat: boolean;

  @Column({
    name: "alarm_phone",
    comment: "报警手机号",
  })
  alarmPhone: string;

  @Column({
    name: "alarm_sms",
    comment: "报警短信手机号",
  })
  alarmSms: string;

  @Column({
    name: "alarm_wechat_name",
    comment: "报警微信名称",
  })
  alarmWechatName: string;

  @Column({
    name: "alarm_wechat_msg_id",
    comment: "报警微信通知消息id",
  })
  alarmWechatMsgId: string;

  @Column({
    name: "alarm_types",
    comment: "报警微信通知消息id",
  })
  alarmTypes: string;

  @Column({
    name: "alarm_phone_types",
    comment: "手机告警类型",
  })
  alarmPhoneTypes: string;

  @Column({
    name: "alarm_sms_types",
    comment: "短信告警类型",
  })
  alarmSmsTypes: string;

  @Column({
    name: "alarm_wechat_types",
    comment: "微信告警类型",
  })
  alarmWechatTypes: string;

  @Column({
    name: "expire_time",
    comment: "客户到期时间",
  })
  expiredAt: number;
}
