const moment = require("moment");
const mongo = require("../common/mongo_pool");

async function getHistoryTrackMongoList(condition, keyValConfig) {
  const collection = "location";

  const findParams = {
    sort: [["locTime", 1]],
  };

  const rows = await mongo.findItems(collection, condition, findParams);
  let results = rows || [];

  results.forEach((track) => {
    track.speed = track.speed || 0;
    track.stopPeriod = track.stopPeriod || 0;
    track.locationTypeName = keyValConfig.LOCATION_TYPE[track.locType] || "";
    track.locTime = track.locTime
      ? moment.unix(track.locTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    track.stopStartTime = track.stopStartTime
      ? moment.unix(track.stopStartTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    track.stopEndTime = track.stopEndTime
      ? moment.unix(track.stopEndTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    track.showTime =
      track.stopPeriod > 0
        ? `${track.stopStartTime}~~${track.stopEndTime}`
        : track.locTime;
    delete track.stopStartTime;
    delete track.stopEndTime;
  });
  // {
  //   "_id" : NumberLong(313608452194368),
  //   "imei" : "14200027160",
  //   "lon" : 119.351501,
  //   "lat" : 25.996467,
  //   "locType" : 3,
  //   "locTime" : NumberLong(1560765886665),
  //   "speed" : 0.0,
  //   "stopEndTime" : 1560767414370.0,
  //   "stopPeriod" : 1528,
  //   "stopStartTime" : 1560765886665.0
  // }
  return results;
}

module.exports = {
  getHistoryTrackMongoList,
};
