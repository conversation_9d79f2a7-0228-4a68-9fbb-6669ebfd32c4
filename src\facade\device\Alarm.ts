import { injectable, inject } from "inversify";
import { Repository } from "typeorm";
import { Context } from "../../types";
import { Alarm } from "../../domain/entity/Alarm";

/**
 * 告警对象
 */
interface AlarmType {
  alarmClass: number;
  alarmType: number;
  imei: string;
  alarmTime: number;
  alarmContent: string;
  fenceId: number;
  secondBetId: number;
}

/**
 * 设备状态汇报
 */
interface State {
  imei: string;
  locTime: number;
  alarms: AlarmType[];
  ACC: number;
  statusInfo_12: string;
  power: number;
  voltage: number;
  statusInfo_9: string;
  statusInfo_10: string;
  statusInfo_11: string;
  outVoltage: string;
  statusInfo_7: string;
  statusInfo_8: string;
  lon: number;
  lat: number;
  locType: number;
}

/**
 * 监控接口
 */
@injectable()
export default class {
  @inject("alarmRepository")
  alarmRepository: Repository<Alarm>;

  /**
   * 触发告警事件
   * @param ctx
   */
  async fire(ctx: Context) {
    ctx.status = 204;
    const state: State = {
      ...ctx.request.body,
      lon: Number(ctx.request.body.lon),
      lat: Number(ctx.request.body.lat),
      locType: Number(ctx.request.body.locType),
    };

    // 无告警信息
    if (!state.alarms) return;
    if (!state.alarms.length) return;

    // 告警信息处理
    for (const alarm of state.alarms) {
      // 创建实例
      const entity = new Alarm();

      // 基础数据生成
      entity.imei = state.imei;
      entity.alarmTime = state.locTime || Math.floor(Date.now() / 1000);
      entity.alarmClass = alarm.alarmClass;
      entity.alarmType = alarm.alarmType;
      entity.alarmContent = alarm.alarmContent || "";
      if (alarm.fenceId) entity.fenceId = alarm.fenceId;
      if (alarm.secondBetId) entity.secondBetId = alarm.secondBetId;
      entity.createdAt = new Date();

      // 定位数据生成
      if (state.locType > 0 && state.lon > 0) {
        entity.locationType = state.locType;
        entity.lon = state.lon;
        entity.lat = state.lat;
      }

      // 保存实体数据
      this.alarmRepository.insert(entity);
    }
  }
}
