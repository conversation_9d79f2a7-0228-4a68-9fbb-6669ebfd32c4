const Organ = require("../proxy/organ");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const organBusiness = require("../business/uc_organ_business");

/**
 * @api {GET} /ucorgan/getorgantree 机构管理-获取机构树列表
 * @apiName getorgantree
 * @apiGroup UcOrgan
 * @apiParam  {Number} organId 机构ID
 * @apiVersion  1.0.0
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
    }
  }
 */
exports.getOrganTree = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "机构管理-获取机构树列表",
  handler: async (ctx) => {
    const reqQueryParams = ctx.request.query;
    const currentUser = ctx.state.user;
    try {
      let organ = {};
      if(reqQueryParams.organId){
        const queryParams = {
          organId: reqQueryParams.organId || -1,
        };
        const currOrgan = await organBusiness.getOrganOneLogic(queryParams);
        if(!currOrgan){
          ctx.body = new RetJson(
            i18n.SYS_ERROR_CODE,
            "组织机构不存在",
            {}
          );
          return;
        }
        organ = new Organ(
          currOrgan.parentId || 0,
          currOrgan.organId || -1
        );
      }else{
        organ = new Organ(
          currentUser.organParentId || 0,
          currentUser.organId || -1
        );
      }

      const treeData = await organ.getFullTree.call(organ);
      if (treeData && treeData.hasOwnProperty("children")) {
        ctx.body = new RetJson(
          i18n.SYS_SUCCESS_CODE,
          i18n.SYS_SUCCESS_MESSAGE,
          treeData.children
        );
        return;
      }

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        []
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOrganTree",
        "获取机构树列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {GET} /ucorgan/getorganlist 机构管理-获取机构分页列表
 * @apiName getorganlist
 * @apiGroup UcOrgan
 * @apiVersion  1.0.0
 * @apiPermission UO-10
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配机构名，备注
 * @apiParam  {Number} parentId 上级机构
 * @apiParam  {Number=0,1} status 状态 0禁用、1启用
 * @apiParam  {Number} roleId 角色搜索
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
        {
          "organId": 10,
          "parentId": 0,
          "organName": "全站",
          "strRoleIds": "10",
          "level": 0,
          "sort": 0,
          "remark": "",
          "createUser": "gpsadmin",
          "createTime": "2018-11-23 22:58",
          "index": 1,
          "strRoleNames": ",超级管理员"
        }
      ]
    }
  }
 */
exports.getOrganList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "机构管理-获取机构分页列表",
  buttonPerm: "UO-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: currentUser.organId || -1,
        parentId: validator.isIntFormat(reqQueryParams.parentId)
          ? Number.parseInt(reqQueryParams.parentId)
          : currentUser.organId,
        status: validator.isInt01(reqQueryParams.status)
          ? reqQueryParams.status
          : -1,
        roleId: validator.isIntFormat(reqQueryParams.roleId, { min: 1 })
          ? reqQueryParams.roleId
          : -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const organList = await organBusiness.getOrganListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        organList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOrganList",
        "获取机构分页列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};

/**
 * @api {POST} /ucorgan/insertorgan 机构管理-新增机构
 * @apiName insertorgan
 * @apiGroup UcOrgan
 * @apiVersion  1.0.0
 * @apiPermission UO-11
 *
 * @apiParam  {String} organName 机构名称 必需值
 * @apiParam  {Number} parentId 父机构 必需值
 * @apiParam  {Number} sort 排序
 * @apiParam  {String} strRoleIds 角色Id列表 必需值 格式为: 2,1 必需值
 * @apiParam  {Number} status 状态 0：禁用、1：启用 必需值
 *
 * @apiParamExample  {json} 请求示例:
 Content-Type:application/json;charset=utf-8
 {
      userCode : 10000,
      userName : ''
   }
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertOrgan = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "机构管理-新增机构",
  buttonPerm: "UO-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertOrgan",
        "新增机构数据：",
        JSON.stringify(requestData)
      );

      await organBusiness.insertOrganLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertOrgan",
        "新增机构成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertOrgan",
          "新增机构失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertOrgan",
        "新增机构出错：",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucorgan/updateorgan 机构管理-编辑机构
 * @apiName updateorgan
 * @apiGroup UcOrgan
 * @apiVersion  1.0.0
 * @apiPermission UO-12
 *
 * @apiParam  {Number} organId 机构Id 必需值
 * @apiParam  {String} organName 机构名称 必需值
 * @apiParam  {Number} parentId 父机构 必需值
 * @apiParam  {String} sort 排序 必需值
 * @apiParam  {String} strRoleIds 角色Id列表 必需值 格式为: 2,1 必需值
 * @apiParam  {Number} status 状态 0：禁用、1：启用 必需值
 *
 * @apiParamExample  {json} 请求示例:
 Content-Type:application/json;charset=utf-8
 {
 }
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateOrgan = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "机构管理-编辑机构",
  buttonPerm: "UO-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      await organBusiness.updateOrganLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateOrgan",
        "更新机构成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateOrgan",
          "更新机构失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateOrgan",
        "更新机构出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucorgan/updateorganparent 机构管理-更改父车组
 * @apiName updateorgan
 * @apiGroup UcOrgan
 * @apiVersion  1.0.0
 * @apiPermission UO-14
 *
 * @apiParam  {Number} organId 机构Id 必需值
 * @apiParam  {Number} parentId 父机构 必需值
 * @apiParam  {String} parentName 父机构名称 必需值
 *
 * @apiParamExample  {json} 请求示例:
 Content-Type:application/json;charset=utf-8
 {
 }
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateOrganParent = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "机构管理-更改父车组",
  buttonPerm: "UO-14",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      await organBusiness.updateOrganParentLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateOrganParent",
        "更改父车组成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateOrganParent",
          "更改父车组失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateOrganParent",
        "更改父车组出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucorgan/deleteorgan 机构管理-删除机构
 * @apiName deleteorgan
 * @apiGroup UcOrgan
 * @apiVersion  1.0.0
 * @apiPermission UO-13
 *
 * @apiParam  {Number} organId 机构Id 必需值
 *
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteOrgan = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "机构管理-删除机构",
  buttonPerm: "UO-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      await organBusiness.deleteOrganLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteOrgan",
        "删除机构成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteOrgan",
          "删除机构失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteOrgan",
        "删除机构出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
