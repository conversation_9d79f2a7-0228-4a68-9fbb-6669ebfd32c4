const mysqlDB = require("../common/mysql_pool");

function getRouteSqlList(queryParams) {
  const joinSql = "left join uc_permission up on up.per_url=ur.route_path";
  const whereSql = `and up.id is null and ur.button_perm!=''`;
  const sql = `select ur.id route_id,ur.system_type,ur.button_perm,ur.http_method,ur.route_type,ur.route_path,
  ur.middlewares,ur.method_name,ur.method_desc,ur.status
  from uc_route ur
  ${queryParams.isPerm ? joinSql : ""}
  where (:system_type=-1 or ur.system_type=:system_type) and (:route_type=-1 or ur.route_type=:route_type)
  and (:http_method='' or ur.http_method=:http_method) and (:status=-1 or ur.status=:status)
  and (:search_key='' or concat(ur.route_path,ur.middlewares,ur.method_name,ur.method_desc) like concat('%',:search_key,'%'))
  ${queryParams.isPerm ? whereSql : ""}
  order by ur.system_type,ur.route_type desc,ur.route_path,ur.http_method`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertRouteSql(routeData) {
  const sqlTasks = [];
  const del = "delete from uc_route where system_type=:system_type";
  const sql = `insert into uc_route(system_type,route_type,button_perm,http_method,route_path,middlewares,method_name,method_desc,status)
  values (:system_type,:route_type,:button_perm,:http_method,:route_path,:middlewares,:method_name,:method_desc,1)`;

  sqlTasks.push({ sql: del, params: { systemType: routeData.systemType } });

  routeData.routes.forEach((e) => {
    const f = JSON.parse(JSON.stringify(e));
    f.middlewares = JSON.stringify(f.middlewares);
    sqlTasks.push({ sql: sql, params: f });
  });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getRouteSqlList,
  insertRouteSql,
};
