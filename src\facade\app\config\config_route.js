/**
 * 路由选项配置文件
 * Array excludeRules 要排除自动注册的路由路径 如：[ '/product/list']
 * Map对象 rewritePrefix 要重写的路径前缀 如：将/about/getabout 重写为 /getabout -- new Map([['/about', '']]),
 * Map对象 rewriteAll 要重写的路径 如：将/account/signin 重写为 /signin -- new Map([  ['/account/signin', '/signin']]),
 *
 * module.exports = {
 * rewritePrefix: new Map([
 *    ['/about', ''],
 *  ]),
 * rewriteAll: new Map([
 *    ['/account/signin', '/signin'],
 *    ['/account/signout', '/signout'],
 *  ]),
 * excludeRules: [
 *    '/product/list',
 * ],
 * };
 */

module.exports = {
  rewritePrefix: new Map([
    ["/account", ""],
    ["/reportalarm", "/report"],
    ["/reportcommand", "/report"],
    ["/reportoffline", "/report"],
    ["/reportoperation", "/report"],
    ["/reportstop", "/report"],
    ["/reportride", "/report"],
  ]),
  rewriteAll: new Map([]),
  excludeRules: ["/insertroute"],
};
