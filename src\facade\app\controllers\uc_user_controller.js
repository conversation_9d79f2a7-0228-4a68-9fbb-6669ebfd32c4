const pinyin = require("node-pinyin");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const userBusiness = require("../business/uc_user_business");
const userDeviceBusiness = require("../business/uc_user_device_business");

/**
 * @api {POST} /ucuser/updateuserdevicelist 用户管理-更新用户设备列表
 * @apiName updateuserdevicelist
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-10
 *
 * @apiParam  {Number} userId 用户Id 必需值
 * @apiParam  {array} addImeis 新增imeis
 * @apiParam  {array} delImeis 删除imeis
 *
 * @apiSuccessExample {json} 响应示例:
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {}
  }
 */
exports.updateUserDeviceList = {
  method: "POST",
  middlewares: ["loginRequired"],
  routeDesc: "用户管理-更新用户设备列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.body;
      const queryParams = {
        userId: validator.isIntFormat(reqQueryParams.userId)
          ? Number.parseInt(reqQueryParams.userId) : 0,
        addImeis: validator.isArray(reqQueryParams.addImeis)
          ? reqQueryParams.addImeis : [],
        delImeis: validator.isArray(reqQueryParams.delImeis)
          ? reqQueryParams.delImeis : [],
      };

      await userDeviceBusiness.batchUpdateUserDeviceLogic(queryParams, currentUser);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateUserDeviceList",
        "更新用户设备列表:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {});
    }
  },
};
/**
 * @api {GET} /ucuser/getuserdevicelist 用户管理-获取用户设备列表
 * @apiName getuserdevicelist
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-10
 *
 * @apiParam  {number} userId 用户Id
 * @apiParam  {number} organId 机构Id
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
        {
          "imei": 'xxxxxxx',
          "organId": 11,
          "createUser": "admin",
          "createTime": "",
        }
      ]
    }
  }
 */
exports.getUserDeviceList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "用户管理-获取用户设备列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        userId: validator.isIntFormat(reqQueryParams.userId)
          ? Number.parseInt(reqQueryParams.userId) : 0,
        searchOrganId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId) : -1,
        currentOrganId: currentUser.organId || -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await userDeviceBusiness.getUserDeviceListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getUserDeviceList",
        "获取用户分页列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};
/**
 * @api {GET} /ucuser/getdetails 用户管理-用户详情
 * @apiName getdetails
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-10
 *
 * @apiParam  {string} userCode 用户编号 精确匹配
 * @apiParam  {number} organId 机构Id
 *
 */
exports.getDetails = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "用户管理-用户详情",
  handler: async (ctx) => {
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        userId: reqQueryParams.userId
      };

      const userDetails = await userBusiness.getUserLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        userDetails
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDetails",
        "获取用户详情出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};
/**
 * @api {GET} /ucuser/getuserlist 用户管理-获取用户分页列表
 * @apiName getuserlist
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-10
 *
 * @apiParam  {string} searchKey 搜索关键词 匹配登录名、姓名、手机
 * @apiParam  {string} userCode 用户编号 精确匹配
 * @apiParam  {number} organId 机构Id
 * @apiParam  {number=0,1} status 状态 0禁用、1启用
 * @apiParam  {string} roleIds 角色搜索 多个角色id以英文逗号分隔 如：1,5,8
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
        {
          "userId": 50,
          "userCode": "0000",
          "userName": "admin",
          "loginName": "15100000000",
          "realname": "超级管理员",
          "sex": 1,
          "roleIds": "10,15,16,35,",
          "email": "",
          "organId": "机构Id",
          "organName": "机构名称",
          "lastLoginTime": "",
          "isSuper": 1,
          "status": 1,
          "remark": "",
          "createUser": "admin",
          "createTime": "",
          "index": 1,
          "roleNames": "超级管理员,业务员"
        }
      ]
    }
  }
 */
exports.getUserList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "用户管理-获取用户分页列表",
  buttonPerm: "UU-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        userCode: (reqQueryParams.userCode || "").trim(),
        searchOrganId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        currentOrganId: currentUser.organId || -1,
        status: validator.isInt01(reqQueryParams.status)
          ? reqQueryParams.status
          : -1,
        roleId: validator.isIntFormat(reqQueryParams.roleId, { min: 1 })
          ? `,${reqQueryParams.roleId},`
          : -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const userList = await userBusiness.getUserListLogic(queryParams, currentUser);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        userList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getUserList",
        "获取用户分页列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};

/**
 * @api {GET} /ucuser/getuserdropdownlist 用户管理-获取用户下拉列表
 * @apiName getuserdropdownlist
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} searchKey 模糊搜索关键词 匹配编号、账号、姓名、登录名
 * @apiParam  {String} strRoleId 一个或多个角色Id 以逗号分割
 *
 * @apiParamExample  {Object} 请求示例:
  {
    searchKey : ***********
    strRoleId : '30,31'
  }
 *
 * @apiSuccessExample {Object} 响应示例:
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
      {
        "userId": 80,
        "userCode": "0010",
        "userName": "",
        "loginName": "",
        "realname": "",
        "roleIds": "15,31",
        "roleNames": "业务员,企业客户"
      }
    ]
  }
 */
exports.getUserDropDownList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "用户管理-获取用户下拉列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        searchKey: (ctx.request.query.searchKey || "").trim(),
        currentOrganId: currentUser.organId || -1,
        strRoleId: validator.isCommaSeparated(ctx.request.query.strRoleId)
          ? ctx.request.query.strRoleId
          : "",
      };

      const dropDownList = await userBusiness.getUserDropDownListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        dropDownList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getUserDropDownList",
        "获取用户下拉列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {POST} /ucuser/insertuser 用户管理-新增用户
 * @apiName insertuser
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-11
 *
 * @apiParam  {String} userName 用户名
 * @apiParam  {String} loginName 登录手机 必需值
 * @apiParam  {String} realname 姓名 必需值
 * @apiParam  {Number} sex 性别，1男，2女，0保密
 * @apiParam  {String} strRoleId 角色Id 必需值 格式为: 2,1 必需值
 * @apiParam  {String} email 邮箱
 * @apiParam  {Number} isSuper 是否超级管理员，0否，1是 必需值
 * @apiParam  {Number} status 状态 0：禁用、1：启用 必需值
 *
 * @apiParamExample  {json} 请求示例:
   Content-Type:application/json;charset=utf-8
   {
      userCode : 10000,
      userName : ''
   }
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertUser = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "用户管理-新增用户",
  buttonPerm: "UU-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertUser",
        "新增用户数据：",
        JSON.stringify(requestData)
      );

      // if (!requestData.userName) {
      //   requestData.userName = (pinyin(requestData.realname, { style: 'normal' }) || []).map(m => m.join('')).join('');
      // }
      requestData.isChildAlarm = requestData.isChildAlarm || 0;
      requestData.isAlarmPhone = requestData.isAlarmPhone || 0;
      requestData.isAlarmSms = requestData.isAlarmSms || 0;
      requestData.isAlarmWechat = requestData.isAlarmWechat || 0;
      requestData.isAlarmEmail = requestData.isAlarmEmail || 0;
      requestData.isAlarmOfficialAccount = requestData.isAlarmOfficialAccount || 0;
      requestData.alarmPhone = requestData.alarmPhone || "";
      requestData.alarmSms = requestData.alarmSms || "";
      requestData.alarmWechatMsgId = requestData.alarmWechatMsgId || "";
      requestData.alarmEmail = requestData.alarmEmail || "";
      requestData.alarmOfficialAccountId = requestData.alarmOfficialAccountId || "";
      requestData.alarmPhoneTypes = requestData.alarmPhoneTypes || "";
      requestData.alarmSmsTypes = requestData.alarmSmsTypes || "";
      requestData.alarmWechatTypes = requestData.alarmWechatTypes || "";
      requestData.alarmEmailTypes = requestData.alarmEmailTypes || "";
      requestData.alarmOfficialAccoutTypes = requestData.alarmOfficialAccountTypes || "";

      let insertResult = await userBusiness.insertUserLogic(
        requestData,
        currentUser
      );
      insertResult = insertResult.user ? insertResult.user : "";
      insertResult = insertResult.insertId
        ? { insertId: insertResult.insertId, password: insertResult.password }
        : {};

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertUser",
        "新增用户结果：",
        JSON.stringify(insertResult)
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        insertResult
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertUser",
          "新增用户失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertUser",
        "新增用户出错：",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucuser/updateuser 用户管理-编辑用户
 * @apiName updateuser
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-12
 *
 * @apiParam  {Number} userId 用户Id 必需值
 * @apiParam  {String} userCode 用户编号 必需值
 * @apiParam  {String} userName 用户名 必需值
 * @apiParam  {String} loginName 登录手机 必需值
 * @apiParam  {String} realname 姓名 必需值
 * @apiParam  {Number} sex 性别，1男，2女，0保密
 * @apiParam  {String} strRoleId 角色Id 必需值 格式为: 2,1 必需值
 * @apiParam  {String} email 邮箱
 * @apiParam  {Number} isEmployee 是否是内部用户，1 是， 0 否 必需值
 * @apiParam  {Number} isSuper 是否超级管理员，0否，1是 必需值
 * @apiParam  {Number} status 状态 0：禁用、1：启用 必需值
 *
 * @apiParamExample  {json} 请求示例:
   Content-Type:application/json;charset=utf-8
   {
    userCode : 10000,
    userName : 'linwenbo'
   }
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateUser = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "用户管理-更新用户",
  buttonPerm: "UU-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      requestData.isChildAlarm = requestData.isChildAlarm || 0;
      requestData.isAlarmPhone = requestData.isAlarmPhone || 0;
      requestData.isAlarmSms = requestData.isAlarmSms || 0;
      requestData.isAlarmWechat = requestData.isAlarmWechat || 0;
      requestData.isAlarmEmail = requestData.isAlarmEmail || 0;
      requestData.isAlarmOfficialAccount = requestData.isAlarmOfficialAccount || 0;
      requestData.alarmPhone = requestData.alarmPhone || "";
      requestData.alarmSms = requestData.alarmSms || "";
      requestData.alarmWechatMsgId = requestData.alarmWechatMsgId || "";
      requestData.alarmEmail = requestData.alarmEmail || "";
      requestData.alarmOfficialAccountId = requestData.alarmOfficialAccountId || "";
      requestData.alarmPhoneTypes = requestData.alarmPhoneTypes || "";
      requestData.alarmSmsTypes = requestData.alarmSmsTypes || "";
      requestData.alarmWechatTypes = requestData.alarmWechatTypes || "";
      requestData.alarmEmailTypes = requestData.alarmEmailTypes || "";
      requestData.alarmOfficialAccoutTypes = requestData.alarmOfficialAccoutTypes || "";

      // 更新用户信息
      await userBusiness.updateUserLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateUser",
        "更新用户成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateUser",
          "更新用户失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateUser",
        "更新用户出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucuser/resetpassword 用户管理-重置密码
 * @apiName resetpassword
 * @apiGroup UcUser
 * @apiVersion  1.0.0
 * @apiPermission UU-13
 *
 * @apiParam  {Number} userId 用户Id 必需值
 *
 * @apiParamExample  {json} 请求示例:
   Content-Type:application/json;charset=utf-8
   {
     userId : 12
   }
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
    errcode : 0,
    errmsg : '操作成功',
    retobj : ''
   }
 */
exports.resetPassword = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "用户管理-重置密码",
  buttonPerm: "UU-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    const requestData = ctx.request.body;
    try {
      let setResult = await userBusiness.resetPasswordLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "resetPassword",
        `重置用户${requestData.userId}的密码成功`,
        JSON.stringify(requestData)
      );

      setResult =
        setResult && setResult.changedRows
          ? { changedRows: setResult.changedRows, password: setResult.password }
          : {};
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        setResult
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "resetPassword",
        `重置用户${requestData.userId}的密码失败:`,
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

// 新增测试数据用
exports.batchInsertUser = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "用户管理-新增测试数据用",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    const queryParams = ctx.request.query;
    try {
      const gCount = validator.isIntFormat(queryParams.count, { min: 10 })
        ? Number.parseInt(queryParams.count, 10)
        : 10;
      const ucRoleId = validator.isIntFormat(queryParams.role, { min: 1 })
        ? queryParams.role
        : 15;
      const organId = validator.isIntFormat(queryParams.organId, { min: 1 })
        ? queryParams.organId
        : 10;

      const insertResult = await userBusiness.batchInsertUserTestLogic(
        currentUser.loginName,
        gCount,
        ucRoleId,
        organId
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        insertResult
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "USER",
        "insertUser",
        "新增用户出错：",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
