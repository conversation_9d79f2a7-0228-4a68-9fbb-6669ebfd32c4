import axios from "axios";

export interface Rego {
  formatted_address: string;
  addressComponent: {
    city: string;
    citycode: string;
    province: string;
    adcode: string;
    district: string;
    towncode: string;
    streetNumber: {
      number: string;
      location: string;
      direction: string;
      distance: string;
      street: string;
    };
    country: string;
    township: string;
    businessAreas: any[];
    building: {
      name: any[];
      type: any[];
    };
    neighborhood: {
      name: any[];
      type: any[];
    };
  };
}

/**
 * 地址解析
 * @param lon
 * @param lat
 */
export const parseRego = async (lon: number, lat: number) => {
  const params = new URLSearchParams();
  params.set("key", "20e6143edbf17e1af84eb5e717c6efe8");
  params.set("s", "rsv3");
  params.set("language", "zh_cn");
  params.set("location", `${lon}, ${lat}`);
  const res = await axios.get(
    `https://restapi.amap.com/v3/geocode/regeo?${params}`
  );
  return res.data.regeocode;
};
