const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const deviceReportBusiness = require("../business/device_report_business");
const stopBusiness = require("../business/report_stop_business");

/**
 * @api {GET} /home/<USER>
 * @apiName getechartstatisticsreport
 * @apiGroup Home
 * @apiVersion  1.0.0
 * @apiPermission HM-10
 *
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getEchartStatisticsReport = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "首页-获取图形统计报表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        organId: validator.isIntFormat(currentUser.organId)
          ? currentUser.organId
          : -1,
      };

      const [
        deviceTypeList,
        deviceModelList,
        onlineStateList,
        offlineList,
        settleList,
        simRenewList,
        top10List,
      ] = await Promise.all([
        deviceReportBusiness.getDeviceTypeReportListLogic(queryParams),
        deviceReportBusiness.getDeviceModelReportListLogic(queryParams),
        deviceReportBusiness.getOnlineStateReportListLogic(queryParams),
        deviceReportBusiness.getDeviceOfflineReportListLogic(queryParams),
        deviceReportBusiness.getDeviceSettleReportListLogic(queryParams),
        deviceReportBusiness.getDeviceSimRenewReportListLogic(queryParams),
        deviceReportBusiness.getProvinceTop10ReportListLogic(queryParams),
      ]);

      const chart1Data = deviceTypeList.map((m) => ({
        name: `${m.deviceTypeName}（${m.deviceCount}）`,
        value: m.deviceCount,
      }));

      const chart2Data = deviceModelList.map((m) => ({
        name: `${m.deviceModelName}(${m.deviceCount})`,
        value: m.deviceCount,
      }));

      const chart3Data = onlineStateList.map((m) => ({
        name: `${m.onlineStateName}(${m.deviceCount})`,
        value: m.deviceCount,
      }));

      const chart4Data = offlineList.map((m) => ({
        name: `${m.offlineIntervalName}(${m.deviceCount})`,
        value: m.deviceCount,
      }));

      const chart5Data = settleList.map((m) => ({
        name: `${m.isSettleName}(${m.deviceCount})`,
        value: m.deviceCount,
      }));

      const chart6Data = simRenewList.map((m) => ({
        name: `${m.expireIntervalName}(${m.deviceCount})`,
        value: m.deviceCount,
      }));

      const chart7Data = top10List.map((m) => ({
        name: m.provinceName,
        value: m.deviceCount,
      }));

      const data = {
        chart1Data,
        chart2Data,
        chart3Data,
        chart4Data,
        chart5Data,
        chart6Data,
        chart7Data,
      };

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        data
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getEchartStatisticsReport",
          "获取图形统计报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getEchartStatisticsReport",
        "获取图形统计报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /home/<USER>
 * @apiName gethomealarmcountreport
 * @apiGroup Home
 * @apiVersion  1.0.0
 * @apiPermission HM-11
 *
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
    }
  }
 */
exports.getHomeAlarmCountReport = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "首页-获取首页报警数量报表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(currentUser.organId)
          ? currentUser.organId
          : -1,
        endSecond: validator.isIntFormat(reqQueryParams.endSecond, { min: 1 })
          ? Number.parseInt(reqQueryParams.endSecond, 10)
          : 604800,
        startSecond: validator.isIntFormat(reqQueryParams.startSecond, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.startSecond, 10)
          : 259200,
      };

      const homeAlarmData = await deviceReportBusiness.getHomeAlarmCountReportLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        homeAlarmData
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getHomeAlarmCountReport",
          "获取首页报警数量报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getHomeAlarmCountReport",
        "获取首页报警数量报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /home/<USER>
 * @apiName getmultideviceofflinelist
 * @apiGroup Home
 * @apiVersion  1.0.0
 * @apiPermission HM-12
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
    }
  }
 */
exports.getMultiDeviceOfflineList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "首页-获取多设备离线报表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        organId: validator.isIntFormat(currentUser.organId)
          ? currentUser.organId
          : -1,
      };

      const multiDeviceOfflineData = await deviceReportBusiness.getMultiDeviceOfflineListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        multiDeviceOfflineData
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getMultiDeviceOfflineList",
          "获取多设备离线报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getMultiDeviceOfflineList",
        "获取多设备离线报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /home/<USER>
 * @apiName gethomestopreportlist
 * @apiGroup Home
 * @apiVersion  1.0.0
 * @apiPermission HM-13
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} startSecond 大于停留秒数
 * @apiParam  {Number} endSecond 小于停留秒数
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getHomeStopReportList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "首页-获取超长停车报表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: currentUser.organId,
        endSecond: validator.isIntFormat(reqQueryParams.endSecond, { min: 1 })
          ? Number.parseInt(reqQueryParams.endSecond, 10)
          : 604800,
        startSecond: validator.isIntFormat(reqQueryParams.startSecond, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.startSecond, 10)
          : 259200,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const stopList = await stopBusiness.getHomeStopListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        stopList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getHomeStopReportList",
          "获取超长停车报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getHomeStopReportList",
        "获取超长停车报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /home/<USER>
 * @apiName exporthomestopreportlist
 * @apiGroup Home
 * @apiVersion  1.0.0
 * @apiPermission HM-14
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} startSecond 大于停留秒数
 * @apiParam  {Number} endSecond 小于停留秒数
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportHomeStopReportList = {
  method: "POST",
  middlewares: ["loginRequired"],
  routeDesc: "首页-导出超长停车报表",
  buttonPerm: "HM-14",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: currentUser.organId,
        endSecond: validator.isIntFormat(requestBody.endSecond, { min: 1 })
          ? Number.parseInt(requestBody.endSecond, 10)
          : 0,
        startSecond: validator.isIntFormat(requestBody.startSecond, { min: 1 })
          ? Number.parseInt(requestBody.startSecond, 10)
          : 0,
        pagination: false,
      };

      const stopList = await stopBusiness.getHomeStopListLogic(queryParams);

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "车架号",
        "车主",
        "车辆备注",
        "设备号",
        "设备类型",
        "设备型号",
        "关注度",
        "关注度备注",
        "停车开始时间",
        "停留时长",
      ]);

      stopList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.vinNo,
          e.carOwner,
          e.carRemark,
          e.imei,
          e.deviceTypeName,
          e.deviceModelName,
          e.attentionName,
          e.attentionRemark,
          e.stopStartTime,
          e.stopSecond,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `超长停车 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportHomeStopReportList",
          "导出超长停车报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportHomeStopReportList",
        "导出超长停车报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
