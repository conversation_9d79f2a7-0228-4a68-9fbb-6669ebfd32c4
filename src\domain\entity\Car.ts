import { Entity, PrimaryColumn, Column } from "typeorm";

@Entity({ name: "gps_car" })
export class Car {
  @PrimaryColumn()
  id: number;

  @Column({
    name: "organ_id",
    comment: "所属机构",
  })
  organId: number;

  @Column({
    name: "plate_no",
    comment: "车牌号",
  })
  plateNo: string;

  @Column({
    name: "vin_no",
    comment: "车架号",
  })
  vinNo: string;

  @Column({
    name: "engine_no",
    comment: "发动机号",
  })
  engineNo: string;

  @Column({
    name: "policy_no",
    comment: "保险单号",
  })
  policyNo: string;

  @Column({
    name: "policy_exp_date",
    comment: "保单到期时间",
  })
  policyExpiredAt: number;

  @Column({
    name: "car_brand",
    comment: "品牌",
  })
  brand: string;

  @Column({
    name: "car_model",
    comment: "车型号",
  })
  model: string;

  @Column({
    name: "car_owner",
    comment: "车主姓名",
  })
  ownerName: string;

  @Column({
    name: "owner_phone",
    comment: "车主手机",
  })
  ownerPhone: string;

  @Column({
    name: "loan_amount",
    comment: "贷款金额",
  })
  loanAmount: string;

  @Column({
    name: "is_delete",
    comment: "是否删除，0 否，1 是",
  })
  isDelete: boolean;

  @Column({
    name: "is_settle",
    comment: "设备结清，0 否，1 是，与设备结清状态关联",
  })
  isSettle: boolean;

  @Column({
    name: "settle_remark",
    comment: "设备结清备注",
  })
  settleRemark: string;

  @Column({
    name: "settle_cancel_remark",
    comment: "撤消设备结清备注",
  })
  settleCancelRemark: string;

  @Column({
    name: "settle_time",
    comment: "结清时间",
  })
  settledAt: number;

  @Column({
    name: "remark",
    comment: "备注",
  })
  remark: string;

  @Column({
    name: "create_user",
    comment: "创建人账号",
  })
  createUser: string;

  @Column({
    name: "create_name",
    comment: "创建人姓名",
  })
  createName: string;

  @Column({
    name: "create_time",
    comment: "创建时间",
  })
  createdAt: Date;

  @Column({
    name: "update_user",
    comment: "更新人",
  })
  updateUser: string;

  @Column({
    name: "update_time",
    comment: "更新时间",
  })
  updatedAt: Date;
}
