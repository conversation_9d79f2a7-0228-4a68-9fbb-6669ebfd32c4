import { env } from "process";
import { Config } from "../config";

export default (): Config => ({
  security: {
    secret: env.JWT_SECRET || "123456",
  },
  database: {
    host: env.MYSQL_HOST || "mysql",
    username: env.MYSQL_USER || "root",
    password: env.MYSQL_PASSWORD || "123456",
    database: env.MYSQL_DATABASE || "gps_monitor",
    port: Number(env.MYSQL_PORT || 3306),
    synchronize: false,
    logging: false,
  },
  mongo: {
    host: env.MONGO_HOST || "mongo",
    username: env.MONGO_USER || "root",
    password: env.MONGO_PASSWORD || "123456",
    database: env.MONGO_DATABASE || "gps_monitor",
    port: Number(env.MONGO_PORT || 27017),
  },
  redis: {
    port: Number(env.REDIS_PORT || 6379),
    host: env.REDIS_HOST || "redis",
    password: env.REDIS_PASS || "",
    db: Number(env.REDIS_DB || 0),
  },
});
