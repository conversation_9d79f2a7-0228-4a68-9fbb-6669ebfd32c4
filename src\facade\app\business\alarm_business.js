const moment = require("moment");

const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");

const alarmDao = require("../lib/dao/alarms_dao");
const deviceDao = require("../lib/dao/device_dao");

/**
 * 获取报警列表
 * 请求参数:
 "organId": "机构Id",
 "alarmTypes": "报警类型",
 "alarmClass": "报警类别",
 "alarmClassIden": "报警类别标识",
 "searchKey": "搜索关键字"
 *
 */
async function getAlarmListLogic(queryParams) {
  if (
    queryParams.alarmClass === -1 &&
    !validator.isCommaSeparated(queryParams.alarmTypes)
  ) {
    return {total: 0, rows: []};
  }

  const [alarmList, keyValConfig] = await Promise.all([
    alarmDao.getAlarmSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "ALARM_TYPE",
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "ATTENTION",
    ]),
  ]);

  const imeis = alarmList.rows.map((item) => {
    return item.imei;
  })
  const deviceList = imeis ? await deviceDao.getDeviceAllSqlData({imeis: imeis.join(",")}) : [];

  alarmList.rows.forEach((e, i) => {
    const device = deviceList.filter((item) => item.imei == e.imei)[0];
    e.organName = device.organName;
    e.deviceName = device.deviceName;
    e.imei = device.imei;
    e.deviceType = device.deviceType;
    e.deviceModel = device.deviceModel;
    e.attention = device.attention;
    e.attentionRemark = device.attentionRemark;
    e.plateNo = device.plateNo;
    e.vinNo = device.vinNo;
    e.carOwner = device.carOwner;
    e.carRemark = device.carRemark;
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.attentionName = keyValConfig.ATTENTION[e.attention] || "";
    e.alarmTypeName = keyValConfig.ALARM_TYPE[e.alarmType] || "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.alarmTime = e.alarmTime
      ? moment.unix(e.alarmTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.currentAddress = "";
  });
  /*  for (const e of alarmList.rows) {
      const deviceCache = await global.context.redisClient.hgetall(`device:${e.imei}`);
      if (deviceCache) {
        e.lon = Number.parseFloat(deviceCache.lon || '0');
        e.lat = Number.parseFloat(deviceCache.lat || '0');
      }
    }*/

  return alarmList;
}

/**
 * 更新报警
 * 请求参数:
 "alarmIds": "报警Id列表",
 "handleStatus": "处理状态",
 "handleUser": "处理人",
 "handleMobile": "处理人手机号",
 "problemType": "问题类型",
 "handleRemark": "处理备注",
 *
 */
async function updateAlarmLogic(updateData) {
  const rules = [
    {
      field: "alarmIds",
      title: "报警Id列表",
      rule: "isCommaSeparated",
      msg: "",
      required: true,
    },
    {
      field: "handleStatus",
      title: "处理状态",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err, model] = commFunc.checkRequestData(updateData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  commFunc.formatNullToString(model);

  model.problemType = model.problemType || 0;
  model.handleMobile = model.handleMobile || "";
  model.handleTime = moment().format("X");

  let updateCount = await alarmDao.updateAlarmSql(model);
  // 更新成功后, 查询剩余未处理的报警, 为0时:更新设备is_alarm为0
  let alarms = await alarmDao.getAlarmSqlList(model);
  for (let i = 0; i < alarms.length; i++) {
    let alarm = alarms[i];
    let undisposedAlarmCount = await alarmDao.getAlarmCountSql({
      imei: alarm.imei,
      handleStatus: 0
    });
    if (undisposedAlarmCount.count === 0) {
      await deviceDao.updateDeviceIsAlarmSql({
        imei: alarm.imei,
        isAlarm: 0
      });
    }
  }
  return updateCount;
}

module.exports = {
  getAlarmListLogic,
  updateAlarmLogic,
};
