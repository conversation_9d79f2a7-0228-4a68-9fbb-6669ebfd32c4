const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const siteConfig = require("../config/config_site");
const validator = require("../lib/common/validator_extend");

const commandDao = require("../lib/dao/report_command_dao");

/**
 * 获取命令记录
 * 请求参数:
 "organId": "机构Id",
 "imei": "IMEI号",
 "startTime": "开始时间戳",
 "endTime": "结束时间戳",
 "pagination": true,
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getCommandReportListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: -1 } }],
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "" },
    {
      field: "startTime",
      title: "开始时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
    },
    {
      field: "endTime",
      title: "结束时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.startTime = queryParams.startTime
    ? moment(`${queryParams.startTime}:00`).format("X")
    : "";
  queryParams.endTime = queryParams.endTime
    ? moment(`${queryParams.endTime}:00`).format("X")
    : "";

  const [commandList, keyValConfig] = await Promise.all([
    commandDao.getCommandReportSqlList(queryParams, currentUser),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "CMD_CODE",
      "CMD_STATUS",
    ]),
  ]);

  commandList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.cmdCodeName = keyValConfig.CMD_CODE[e.cmdCode] || "";
    e.cmdStatusName = keyValConfig.CMD_STATUS[e.cmdStatus] || "";
    e.sendTime = e.sendTime
      ? moment.unix(e.sendTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.returnTime = e.returnTime
      ? moment.unix(e.returnTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return commandList;
}

/**
 * 发送命令
 * 请求参数:
 "imei": "IMEI号",
 "cmdCode": "",
 "sendData": "",
 "createUser": ""
 *
 */
async function sendCommandLogic(insertData) {
  const rules = [
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "", required: true },
    {
      field: "deviceModel",
      title: "设备型号",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(insertData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [keyValConfig, nameKeyConfig] = await Promise.all([
    context.getPropertymapKeyVal(["MODEL_COMMAND", "CMD_CODE"]),
    context.getPropertymapNameKey(["MODEL_COMMAND", "CMD_CODE", "CMD_STATUS"]),
  ]);

  if (!keyValConfig.MODEL_COMMAND.hasOwnProperty(`${insertData.deviceModel}`)) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.IMEI_NOT_COMMAND);
  }

  const commands = keyValConfig.MODEL_COMMAND[`${insertData.deviceModel}`];
  const cmdCodes = Object.keys(keyValConfig.CMD_CODE).filter((f) =>
    commands.split(",").includes(`${f}`)
  );
  const commandData = cmdCodes.filter(
    (f) => `${f}` === `${insertData.cmdCode}`
  );
  if (!commandData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.IMEI_NOT_COMMAND);
  }

  let sendData = "";
  if (
    [
      nameKeyConfig.CMD_CODE.CC_2,
      nameKeyConfig.CMD_CODE.CC_3,
      nameKeyConfig.CMD_CODE.CC_88,
      nameKeyConfig.CMD_CODE.CC_98,
      nameKeyConfig.CMD_CODE.CC_99
    ].includes(
      `${insertData.cmdCode}`
    )
    // && validator.isIntFormat(insertData.control, { min: 1, max: 2 })
  ) {
    sendData = insertData.control;
  }

  if (`${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_7) {
    sendData = 1;
  }

  if (
    `${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_10 &&
    validator.isIntFormat(insertData.interval, { min: 0, max: 999 })
  ) {
    sendData = insertData.interval;
  }

  if (`${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_14) {
    sendData += validator.isDateHHmm(insertData.t1) ? `${insertData.t1}` : "";
    sendData += validator.isDateHHmm(insertData.t2) ? `,${insertData.t2}` : "";
    sendData += validator.isDateHHmm(insertData.t3) ? `,${insertData.t3}` : "";
    sendData += validator.isDateHHmm(insertData.t4) ? `,${insertData.t4}` : "";
  }

  if (
    `${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_16 &&
    validator.isIntFormat(insertData.weekEnable, { min: 0, max: 1 }) &&
    validator.isWeekDay(insertData.weekDays, { min: 0, max: 1234567 }) &&
    validator.isDateHHmm(insertData.weekTime)
  ) {
    sendData += `${insertData.weekEnable},${insertData.weekDays},${insertData.weekTime}`;
  }

  if (
    `${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_17 &&
    validator.isDateHHmm(insertData.awakenTime) &&
    validator.isIntFormat(insertData.aInterval, { min: 1, max: 360 })
  ) {
    sendData += `${insertData.awakenTime},${insertData.aInterval}`;
  }

  if (
    `${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_18 &&
    validator.isIntFormat(insertData.trackInterval, { min: 10, max: 1800 }) &&
    validator.isIntFormat(insertData.trackDuration, { min: 5, max: 43200 })
  ) {
    sendData += `${insertData.trackInterval},${insertData.trackDuration}`;
  }

  insertData.custom = (insertData.custom || "").trim();
  if (
    `${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_100 &&
    insertData.custom.length > 2
  ) {
    sendData = insertData.custom;
  }

  if (!sendData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.COMMAND_FORMAT_ERROR);
  }

  const insertModel = {
    imei: insertData.imei,
    cmdCode: insertData.cmdCode,
    cmdStatus: nameKeyConfig.CMD_STATUS.NOSEND,
    sendData,
    remark: "",
    userName: insertData.userName,
  };

  const result = await commandDao.insertCommandReportSql(insertModel);
  const updateData = {
    imei: insertData.imei,
    cmdCode: insertData.cmdCode,
    id: result,
  };
  if (`${insertData.cmdCode}` === nameKeyConfig.CMD_CODE.CC_100) {
    updateData.sendData = sendData.substr(0, 2);
  }
  await commandDao.updateCommandReportSql(updateData);
  await context.redisClient.publish(
    siteConfig.redisChannel.command,
    `${result}`
  );
  return result;
}

module.exports = {
  getCommandReportListLogic,
  sendCommandLogic,
};
