const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const propertymapBusiness = require("../business/propertymap_business");

/**
 * @api {GET} /propertymap/getpropertymapdropdownlist 属性管理-获取属性下拉列表
 * @apiName getpropertymapdropdownlist
 * @apiGroup Propertymap
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} codes 属性编码集合，用,隔开
 *
 * @apiParamExample  {Object} 请求示例:
  {
    codes: 'KITCHENCLASSA,KITCHENCLASSB'
  }
 *
 * @apiSuccessExample {Object} 响应示例:
  HTTP/1.1 200 OK
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": {
      "SERVICECLASS": [
        {
          "key": "1",
          "val": "检测类",
          "sort": 0
        }
      ]
    }
  }
 *
 */
exports.getPropertymapDropDownList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "属性管理-获取属性下拉列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      let codes = ctx.request.query.codes;
      if (validator.isNullOrEmpty(codes)) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_FIELD_ERROR
        );
        return;
      }
      codes = `${codes}`.split(",");

      const propertymapList = await propertymapBusiness.getPropertymapDropDownListLogic(
        codes
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        propertymapList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getPropertymapDropDownList",
        "获取属性映射下拉列表出错:",
        err
      );

      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /propertymap/getpropertymapnamekeydata 属性管理-获取名键对对象
 * @apiName getpropertymapnamekeydata
 * @apiGroup Propertymap
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} codes 属性编码集合，用,隔开
 *
 * @apiParamExample  {Object} 请求示例:
  {
    codes: 'SERVICECLASS,KITCHENINVEST'
  }
 *
 * @apiSuccessExample {Object} 响应示例:
  HTTP/1.1 200 OK
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      UNIT: { },
      CUSTOMERTYPE: {
        GENERAL: "1",
        CONTRACT: "2",
        FACTORY: "3"
      }
    }
  }
 *
 */
exports.getPropertymapNameKeyData = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "属性管理-获取取名键对对象",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      let codes = ctx.request.query.codes;
      if (validator.isNullOrEmpty(codes)) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_FIELD_ERROR
        );
        return;
      }
      codes = `${codes}`.split(",");

      const propertymapData = await propertymapBusiness.getPropertymapNameKeyLogic(
        codes
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        propertymapData
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getPropertymapNameKeyData",
        "获取取名键对对象出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /propertymap/getpropertymaplist 属性管理-获取属性列表
 * @apiName getpropertymaplist
 * @apiGroup Propertymap
 * @apiVersion  1.0.0
 * @apiPermission PM-10
 *
 * @apiParam  {String} code 模糊搜索关键字 编码
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 页码
 *
 * @apiParamExample  {json} 请求示例:
  Content-Type:application/json;charset=utf-8
  {
     code : 'UNIT',
     status : 1
  }
 * @apiSuccessExample {Object} 响应示例:
  HTTP/1.1 200 OK
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": {
      "total": 309,
      "rows": [
        {
          "id": 220,
          "name": "",
          "parentId": 0,
          "code": "",
          "key": "1",
          "val": "",
          "sort": 0,
          "status": 1,
          "remark": "",
          "createUser": "",
          "createTime": "",
          "updateUser": "",
          "updateTime": "",
          "index": 1
        }
      ]
    }
  }
 */
exports.getPropertymapList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "属性管理-获取属性分页列表",
  buttonPerm: "PM-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        code: (reqQueryParams.code || "").trim(),
        status: validator.isInt01(reqQueryParams.status)
          ? reqQueryParams.status
          : "",
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const propertymapList = await propertymapBusiness.getPropertymapListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        propertymapList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getPropertymapList",
        "获取属性列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};

/**
 * @api {POST} /propertymap/insertpropertymap 属性管理-新增属性
 * @apiName insertpropertymap
 * @apiGroup Propertymap
 * @apiVersion  1.0.0
 * @apiPermission PM-11
 *
 * @apiParam  {String} name 属性中文说明
 * @apiParam  {Number} parentId 父编码
 * @apiParam  {String} code 属性编码 必需值
 * @apiParam  {String} key 属性键，（与属性编码联合唯一） 必需值
 * @apiParam  {String} val 属性值 必需值
 * @apiParam  {Number} sort 排序
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 * @apiParam  {String} remark 备注
 *
 * @apiParamExample  {json} 请求示例:
  Content-Type:application/json;charset=utf-8
  {
    name: "",
    parentId: "",
    code: "",
    key: "11",
    val: "",
    sort: 20,
    status: 1,
    remark: ""
  }
 *
 * @apiSuccessExample {Object} 响应示例:
  HTTP/1.1 200 OK
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: ""
  }
 */
exports.insertPropertymap = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "属性管理-新增属性",
  buttonPerm: "PM-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      await propertymapBusiness.insertPropertymapLogic(
        requestData,
        currentUser.loginName
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertPropertymap",
        "新增属性映射成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.hasOwnProperty("errcode")) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertPropertymap",
          "新增属性映射失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertPropertymap",
        "新增属性映射错误:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /propertymap/updatepropertymap 属性管理-更新属性
 * @apiName updatepropertymap
 * @apiGroup Propertymap
 * @apiVersion  1.0.0
 * @apiPermission PM-12
 *
 * @apiParam  {Number} id 属性Id 必需值
 * @apiParam  {String} name 属性中文说明
 * @apiParam  {Number} parentId 父编码
 * @apiParam  {String} code 属性编码 必需值
 * @apiParam  {String} key 属性键，（与属性编码联合唯一） 必需值
 * @apiParam  {String} val 属性值 必需值
 * @apiParam  {Number} sort 排序
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 * @apiParam  {String} remark 备注
 *
 * @apiParamExample  {json} 请求示例:
  Content-Type:application/json;charset=utf-8
  {
    id: 2,
    name : '',
    parentId : '',
    code: '',
    key: '11',
    val: '',
    sort: 10,
    status: 0,
    remark: ''
  }
 *
 * @apiSuccessExample {Object} 响应示例:
  HTTP/1.1 200 OK
  {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
  }
 */
exports.updatePropertymap = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "属性管理-更新属性",
  buttonPerm: "PM-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      await propertymapBusiness.updatePropertymapLogic(
        requestData,
        currentUser.loginName
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updatePropertymap",
        "更新属性映射成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.hasOwnProperty("errcode")) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updatePropertymap",
          "更新属性映射失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updatePropertymap",
        "更新属性映射错误:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /propertymap/refreshpropertymap 属性管理-刷新属性缓存
 * @apiName refreshpropertymap
 * @apiGroup Propertymap
 * @apiVersion  1.0.0
 * @apiPermission PM-13
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
    errcode : 0,
    errmsg : '操作成功',
    retobj : ''
 }
 */
exports.refreshPropertymap = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "属性管理-刷新属性",
  buttonPerm: "PM-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      await propertymapBusiness.refreshPropertymapLogic();

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "refreshPropertymap",
        "刷新属性缓存成功"
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "REDIS",
        "refreshPropertymap",
        "刷新属性缓存错误:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
