const mysqlDB = require("../common/mysql_pool");

function getFenceSqlList(queryParams) {
  const sql = `select gf.id fence_id,gf.organ_id,gf.fence_name,gf.shape_type,gf.shape_data,gf.shape_scope,
  gf.stop_minutes,gf.data_source,gf.is_share,gf.in_out,gf.status,gf.remark,gf.create_user,gf.create_time,uo.organ_name
  from gps_fence gf
  join uc_organ uo on gf.organ_id=uo.id
  where (:search_key='' or gf.fence_name like concat('%',:search_key,'%')) 
  and gf.organ_id=:organ_id and (:is_share=-1 or gf.is_share=:is_share) and gf.status=:status`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertFenceSql(insertData) {
  const sql = `insert into gps_fence(organ_id,fence_name,shape_type,shape_data,shape_scope,stop_minutes,
  data_source,is_share,in_out,status,remark,create_user,create_time)
  values(:organ_id,:fence_name,:shape_type,:shape_data,:shape_scope,:stop_minutes,
  :data_source,:is_share,:in_out,1,:remark,:user_name,now())`;

  return mysqlDB.insert(sql, insertData);
}

function updateFenceSql(updateData) {
  const sql = `update gps_fence 
  set fence_name=:fence_name,shape_type=:shape_type,shape_data=:shape_data,shape_scope=:shape_scope,
  stop_minutes=:stop_minutes,data_source=:data_source,is_share=:is_share,in_out=:in_out,
  remark=:remark,update_user=:user_name,update_time=now()
  where id=:fence_id`;

  return mysqlDB.update(sql, updateData);
}

function deleteFenceSql(deleteData) {
  const sql = `delete from gps_fence where id in(${deleteData.fenceIds.join(
    ","
  )})`;

  return mysqlDB.del(sql, deleteData);
}

// 围栏绑定设备
function getFenceDeviceSqlList(queryParams) {
  const sql = `select gfd.id fence_device_id,gfd.fence_id,gfd.data_class,gfd.organ_id,gfd.imei,uo.organ_name
  from gps_fence_device gfd
  left join uc_organ uo on gfd.organ_id=uo.id
  where gfd.fence_id=:fence_id`;

  return mysqlDB.all(sql, queryParams);
}

function insertFenceDeviceSql(insertList) {
  const sqlTasks = [];

  const sql = `insert into gps_fence_device(fence_id,data_class,organ_id,imei,create_user,create_time)
  values(:fence_id,:data_class,:organ_id,:imei,:user_name,now())`;

  for (const insertData of insertList) {
    sqlTasks.push({ sql: sql, params: insertData });
  }

  return mysqlDB.executeTransaction(sqlTasks);
}

function deleteFenceDeviceSql(deleteData) {
  const sql = `delete from gps_fence_device where id in(${deleteData.fenceDeviceIds.join(
    ","
  )})`;

  return mysqlDB.del(sql, deleteData);
}

module.exports = {
  getFenceSqlList,
  insertFenceSql,
  updateFenceSql,
  deleteFenceSql,

  getFenceDeviceSqlList,
  insertFenceDeviceSql,
  deleteFenceDeviceSql,
};
