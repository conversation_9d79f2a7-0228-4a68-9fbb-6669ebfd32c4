const moment = require("moment");
const utility = require("utility");
const urlencode = require("urlencode");
const qs = require("querystring");

const RetJson = require("../../proxy/retjson");
const HttpSuperagent = require("../../proxy/supperagent");
const config = require("../../config/config_site");
const staticConfig = require("../../config/config_static");
const validator = require("../../lib/common/validator_extend");

class HttpHandler {
  _getHttpApiUrl(reqConfigKey, reqPath, reqParams) {
    const apiParams = config.apiReqConfig[reqConfigKey];
    reqParams[apiParams.apikey.key] = apiParams.apikey.val;
    reqParams[apiParams.timestamp.key] = moment().format(
      apiParams.timestamp.val
    );
    reqParams[apiParams.sign.key] = "";

    let gather = "";
    let keysTemp = Object.keys(reqParams);
    keysTemp = keysTemp.filter((k) => k !== apiParams.sign.key);
    keysTemp.sort();

    for (const key of keysTemp) {
      gather += `${key}${apiParams.gatherSign}${reqParams[key]}`;
    }

    if (apiParams.gatherMethod === "both") {
      gather = apiParams.secretkey.val + gather + apiParams.secretkey.val;
    }

    if (apiParams.gatherMethod === "right") {
      gather += apiParams.secretkey.val;
    }

    gather = apiParams.urlencode ? urlencode(gather) : gather;
    reqParams[apiParams.sign.key] = utility.md5(gather).toUpperCase();

    return `${
      apiParams.reqBaseUrl || staticConfig.monitorHost[reqConfigKey]
    }${reqPath}?${qs.stringify(reqParams)}`;
  }

  async httpApiGetInvoke(reqConfigKey, reqPath, reqParams) {
    const reqUrl = this._getHttpApiUrl(reqConfigKey, reqPath, reqParams);

    let httpResult = await HttpSuperagent.baseHttpGetCall(reqUrl);
    if (typeof httpResult === "string" && validator.isJSON(httpResult)) {
      httpResult = JSON.parse(httpResult);
    }

    return new RetJson(
      httpResult[config.apiReqConfig[reqConfigKey].returnFormat.errcode],
      httpResult[config.apiReqConfig[reqConfigKey].returnFormat.errmsg],
      httpResult[config.apiReqConfig[reqConfigKey].returnFormat.retobj]
    );
  }

  async httpApiPostInvoke(reqConfigKey, reqPath, reqParams, postParams) {
    const reqUrl = this._getHttpApiUrl(reqConfigKey, reqPath, reqParams);

    let httpResult = await HttpSuperagent.baseHttpPostCall(reqUrl, postParams);
    if (typeof httpResult === "string" && validator.isJSON(httpResult)) {
      httpResult = JSON.parse(httpResult);
    }

    return new RetJson(
      httpResult[config.apiReqConfig[reqConfigKey].returnFormat.errcode],
      httpResult[config.apiReqConfig[reqConfigKey].returnFormat.errmsg],
      httpResult[config.apiReqConfig[reqConfigKey].returnFormat.retobj]
    );
  }
}

module.exports = HttpHandler;
