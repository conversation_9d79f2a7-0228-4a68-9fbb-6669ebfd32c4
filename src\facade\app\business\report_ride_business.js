const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");

const rideDao = require("../lib/dao/report_ride_dao");

/**
 * 获取报警报表
 * 请求参数:
 "organId": "机构Id",
 "imei": "IMEI号",
 "startTime": "开始时间",
 "endTime": "结束时间",
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getRideReportListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    { field: "organId", title: "机构Id", rule: "isIntFormat", msg: "" },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "startTime",
      title: "开始时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
    {
      field: "endTime",
      title: "结束时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.startTime = moment(`${queryParams.startTime}:00`).format("X");
  queryParams.endTime = moment(`${queryParams.endTime}:00`).format("X");

  const rideList = await rideDao.getRideReportSqlList(queryParams, currentUser);

  rideList.rows.forEach((e) => {
    commFunc.formatNullToString(e);
    e.startTime = e.startTime
      ? moment.unix(e.startTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    e.endTime = e.endTime
      ? moment.unix(e.endTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    e.totalTime = e.totalTime ? commFunc.secondsToHumanize(e.totalTime) : "";
  });

  return rideList;
}

async function getMileageReportListLogic(queryParams) {
  const rules = [
    { field: "organId", title: "机构Id", rule: "isIntFormat", msg: "" },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "startTime",
      title: "开始时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
    {
      field: "endTime",
      title: "结束时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.startTime = moment(`${queryParams.startTime}:00`).format("X");
  queryParams.endTime = moment(`${queryParams.endTime}:00`).format("X");

  const mileageList = await rideDao.getMileageReportSqlList(queryParams);

  mileageList.rows.forEach((e) => {
    commFunc.formatNullToString(e);
    e.dayTime = e.dayTime ? moment.unix(e.dayTime).format("YYYY-MM-DD") : "";
  });

  return mileageList;
}

module.exports = {
  getRideReportListLogic,
  getMileageReportListLogic,
};
