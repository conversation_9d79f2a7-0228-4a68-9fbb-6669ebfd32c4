-- GPS 设备管理系统安全优化脚本
-- 只包含不会影响现有接口的优化项
-- 可以在生产环境安全执行

-- ==================== 执行前检查 ====================
-- 检查当前数据库版本和表状态
SELECT VERSION() as mysql_version;
SELECT TABLE_NAME, ENGINE, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('gps_device', 'uc_organ', 'gps_car');

-- ==================== 安全索引优化 ====================
-- 这些索引只会提升查询性能，不会影响现有功能

-- 1. gps_device 表索引优化
-- 主查询索引 - 覆盖最常用的查询条件
CREATE INDEX IF NOT EXISTS idx_gps_device_query_main 
ON gps_device(organ_id, is_settle, is_delete, online_state, attention);

-- 搜索优化索引 - 覆盖搜索场景  
CREATE INDEX IF NOT EXISTS idx_gps_device_search_optimized 
ON gps_device(organ_id, is_settle, is_delete, imei, device_name, plate_no);

-- 告警和超速查询索引
CREATE INDEX IF NOT EXISTS idx_gps_device_alarm_speed 
ON gps_device(is_alarm, is_speed, online_state);

-- 车牌号查询索引
CREATE INDEX IF NOT EXISTS idx_gps_device_plate_lookup 
ON gps_device(plate_no, organ_id);

-- IMEI 查询索引
CREATE INDEX IF NOT EXISTS idx_gps_device_imei 
ON gps_device(imei);

-- 在线状态索引
CREATE INDEX IF NOT EXISTS idx_gps_device_online_state 
ON gps_device(online_state, last_online_time);

-- 2. uc_organ 表索引优化
-- 路径查询覆盖索引
CREATE INDEX IF NOT EXISTS idx_uc_organ_path_covering 
ON uc_organ(path, id, organ_name);

-- ID和路径复合索引
CREATE INDEX IF NOT EXISTS idx_uc_organ_id_path 
ON uc_organ(id, path);

-- 3. gps_car 表索引优化
-- 车牌号覆盖索引
CREATE INDEX IF NOT EXISTS idx_gps_car_plate_covering 
ON gps_car(plate_no, vin_no, engine_no, car_owner, car_brand, car_model);

-- 组织ID索引
CREATE INDEX IF NOT EXISTS idx_gps_car_organ_id 
ON gps_car(organ_id);

-- 车主查询索引
CREATE INDEX IF NOT EXISTS idx_gps_car_owner 
ON gps_car(car_owner, plate_no);

-- 4. 分片表索引优化（如果存在）
-- 为常见的分片表创建索引
-- 注意：需要根据实际的分片表数量调整

-- 检查分片表是否存在
SET @sql = '';
SELECT GROUP_CONCAT(
    CONCAT('CREATE INDEX IF NOT EXISTS idx_', TABLE_NAME, '_main ON ', TABLE_NAME, '(user_id, imei, organ_id);')
    SEPARATOR '\n'
) INTO @sql
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'uc_user_device_%'
AND TABLE_NAME REGEXP '^uc_user_device_[0-9]+$';

-- 如果有分片表，执行索引创建
-- 注意：这里只是生成SQL，需要手动执行
SELECT @sql as shard_table_indexes;

-- ==================== 性能优化建议 ====================

-- 1. 更新表统计信息
ANALYZE TABLE gps_device;
ANALYZE TABLE uc_organ;
ANALYZE TABLE gps_car;

-- 2. 检查索引使用情况（执行一段时间后查看）
-- SELECT * FROM sys.schema_unused_indexes WHERE object_schema = DATABASE();

-- 3. 慢查询监控建议
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 2;
-- SET GLOBAL log_queries_not_using_indexes = 'ON';

-- ==================== 执行后验证 ====================

-- 检查新创建的索引
SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME, SEQ_IN_INDEX, CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('gps_device', 'uc_organ', 'gps_car')
AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 检查索引大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Total Size (MB)',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index Size (MB)',
    ROUND((INDEX_LENGTH / (DATA_LENGTH + INDEX_LENGTH)) * 100, 2) AS 'Index Ratio (%)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('gps_device', 'uc_organ', 'gps_car');

-- ==================== 性能测试建议 ====================

-- 测试主要查询的执行计划
-- EXPLAIN SELECT ... FROM gps_device WHERE organ_id = ? AND is_settle = 0 AND is_delete = 0;

-- 测试搜索查询的执行计划  
-- EXPLAIN SELECT ... FROM gps_device WHERE imei LIKE '%?%' AND is_settle = 0 AND is_delete = 0;

-- 测试关联查询的执行计划
-- EXPLAIN SELECT ... FROM gps_device gd 
-- INNER JOIN uc_organ uo ON gd.organ_id = uo.id 
-- INNER JOIN gps_car gc ON gd.plate_no = gc.plate_no;

-- ==================== 回滚脚本（如果需要）====================
-- 如果发现索引影响性能，可以删除特定索引
-- DROP INDEX IF EXISTS idx_gps_device_query_main ON gps_device;
-- DROP INDEX IF EXISTS idx_gps_device_search_optimized ON gps_device;
-- DROP INDEX IF EXISTS idx_gps_device_alarm_speed ON gps_device;
-- DROP INDEX IF EXISTS idx_gps_device_plate_lookup ON gps_device;
-- DROP INDEX IF EXISTS idx_uc_organ_path_covering ON uc_organ;
-- DROP INDEX IF EXISTS idx_gps_car_plate_covering ON gps_car;

-- ==================== 监控建议 ====================
-- 1. 监控慢查询日志
-- 2. 监控索引使用率
-- 3. 监控表锁等待
-- 4. 监控磁盘空间使用（索引会增加存储空间）

-- 执行完成提示
SELECT 'GPS设备管理系统安全索引优化完成！' as status,
       '请监控系统性能，确认优化效果' as next_step;
