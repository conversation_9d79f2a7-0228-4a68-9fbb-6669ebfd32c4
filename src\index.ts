import "reflect-metadata";
import dotenv from "dotenv";
import { env } from "process";
import { join } from "path";
import Koa from "koa";
import cors from "koa2-cors";
import helmet from "koa-helmet";
import body from "koa-body";
import compress from "koa-compress";
import { Container } from "inversify";
import configBuilder from "./config/config.default";
import registerRedis from "./foundation/component/redis";
import registerOrm from "./foundation/component/orm";
import registerRouter, { Router } from "./foundation/component/router";
// import Scheduler from "./scheduler";
import jwt from "jsonwebtoken";
import {
  globalMiddleware,
  routerMiddleware,
  allowedMethodsMiddleware,
} from "./facade/app";
import { Context } from "./types";
import AlarmController from "./facade/device/Alarm";
import MessageController from "./facade/device/Message";

// 环境载入
if (env.NODE_ENV === "development") {
  dotenv.config({ path: join(__dirname, "../.env") });
}

// 启动应用
const start = async () => {
  // 依赖注入容器
  const container = new Container();
  container.bind("env").toConstantValue(env);

  // 生成配置
  const config = configBuilder();
  require(`./config/config.${env.NODE_ENV || "development"}`).default(config);
  container.bind("config").toConstantValue(config);

  // 注册组件
  await Promise.all([
    registerRedis(container),
    registerOrm(container),
    registerRouter(container),
  ]);

  // 旧接口服务
  const apiServer = new Koa();
  apiServer.use(cors());
  apiServer.use(helmet());
  apiServer.use(
    body({
      multipart: true,
      formidable: { maxFileSize: 200 * 1024 * 1024 },
    })
  );
  apiServer.use(
    compress({
      threshold: 2048,
      filter: (type) => /text/i.test(type),
    })
  );
  container.bind(AlarmController).toSelf();
  container.bind(MessageController).toSelf();
  apiServer.use((ctx: Context, next) => {
    if (ctx.method !== "POST") return next();
    switch (ctx.path) {
      case "/device/alarm": {
        return container.get(AlarmController).fire(ctx);
      }
      case "/device/message/tcp": {
        return container.get(MessageController).tcp(ctx);
      }
      case "/device/message/udp": {
        return container.get(MessageController).tcp(ctx);
      }
      default: {
        return next();
      }
    }
  });
  apiServer.use(globalMiddleware);
  apiServer.use(routerMiddleware);
  apiServer.use(allowedMethodsMiddleware);

  // 开放接口服务
  const openServer = new Koa();
  openServer.use(cors());
  openServer.use(helmet());
  openServer.use(
    body({
      multipart: true,
      formidable: { maxFileSize: 200 * 1024 * 1024 },
    })
  );
  openServer.use(
    compress({
      threshold: 2048,
      filter: (type) => /text/i.test(type),
    })
  );
  openServer.use(async (ctx, next) => {
    if (ctx.path === "/api/v1/token") return await next();
    const { authorization } = ctx.request.headers;

    // 授权为空
    if (!authorization) {
      ctx.body = { msg: "授权authorization不能为空" };
      ctx.status = 401;
      return;
    }

    try {
      ctx.token = <any>jwt.verify(authorization, config.security.secret);

      // 过期判定
      if (ctx.token.exp * 1000 < Date.now()) {
        ctx.body = { msg: "用户TOKEN已过期" };
        ctx.status = 401;
        return;
      }
    } catch (e) {
      ctx.body = { msg: `授权验证失败：${e}` };
      ctx.status = 401;
      return;
    }

    // 执行操作
    await next();
  });

  const router = container.get<Router>("router");
  openServer.use((ctx: any) => router.handle(ctx));

  // 启动服务
  apiServer.listen(7001, () => {
    console.log("web listen : 7001");
  });
  openServer.listen(7002, () => {
    console.log("web listen : 7002");
  });

  // // 定时任务
  // container.bind(Scheduler).toSelf();
  // if (env.NODE_ENV !== "development") {
  //   container.get(Scheduler).run();
  // }
};

// 启动程序
(async () => {
  try {
    await start();
  } catch (error) {
    console.log("[启动失败]", error);
  }
})();
