## 项目安装

1.安装开发环境

```
webstorm 2018.2.5
node > v8.9.3
mongodb
redis
mysql
```

2.安装全局模块

```
此项目必装
npm install -g cnpm
npm install -g gulp
npm install -g supervisor
npm install -g apidoc

建议安装
npm install -g babel-cli
npm install -g webpack
npm install -g eslint
npm install -g eslint-plugin-import
npm install -g eslint-config-airbnb
npm install -g eslint-plugin-jsx-a11y
npm install -g eslint-plugin-react

编译安装，无需
npm install -g node-gyp
npm install -g node-pre-gyp
npm install -g nw-gyp

生产环境必装
npm install -g pm2
```

3.安装依赖模块

```
开发环境
npm install
生产环境
npm install --production

单独模块安装
npm install --save-dev 模块名
npm install --save 模块名
```

4.项目说明

```
+ [business]                                  项目业务逻辑层
+ [config]                                    项目配置文件
+ [controllers]                               项目控制器
+ [dist]                                      项目发布临时文件夹
+ [lib]                                       公共模块，Dao
+ [middleware]                                项目中间件
+ [test]                                      项目测试
+ [proxy]                                     项目model
+ [.editorconfig]                             文件统一格式化，支持所有开发工具
+ [.eslintignore]                             项目代码检测忽略配置
+ [.eslint]                                   项目代码检测
+ [.gitignore]                                Git版本管理配置文件，用于配置不需要加入版本管理的文件
+ [app.js]                                    项目启动文件
+ [gulpfile.js]                               自动化打包，编译，压缩，部署服务器
+ [package.json]                              项目配置，自定义命令，启动程序，自动部署
+ [routes_api.json]                           项目api路由
+ [routes_web.json]                           项目路由
```

5.package.json 自定义命令说明

```
npm run env:dev       开发环境运行
npm run env:prod      生产环境运行
npm run code:eslint   项目代码检测
npm run doc:gen       文档生成
npm run bump:version  项目版本提升
npm run publish       代码发布
```

6.运行程序执行命令：npm run env:dev

## 其他

pm2 命令

```
1. 第一次启动执行 pm2 start app.js --name 'gps-back' -e logs/err.log -o logs/out.log
2. 结束程序后启动 pm2 start gps-back
3. 重启 pm2 restart gps-back
4. 结束程序 pm2 stop gps-back
5. 删除 pm2 delete gps-back
6. 杀死 pm2 kill
7. 查看进程列表 pm2 list
8. 查看进程日志 pm2 logs
```
