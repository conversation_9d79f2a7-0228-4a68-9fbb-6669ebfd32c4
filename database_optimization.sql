-- GPS 设备管理系统数据库优化 SQL (上亿级数据量优化版本)
-- 针对 /device/getorgandevicelist 接口的性能优化

-- ==================== 核心优化策略 ====================
-- 1. 表分区策略
-- 2. 高效索引设计
-- 3. 读写分离
-- 4. 数据归档策略
-- 5. 缓存层优化

-- ==================== 1. 安全索引优化（立即可执行，不影响现有接口）====================
-- 这些索引只会提升性能，不会影响现有功能

-- ==================== 2. 表分区优化（需要维护窗口，会影响所有接口）====================
-- ⚠️ 警告：表分区会影响所有使用 gps_device 表的接口
-- 包括：设备列表、设备详情、车辆管理、统计查询等所有相关功能
-- 建议：先在测试环境充分验证，然后安排维护窗口执行

-- 方案A：按组织ID范围分区（注释掉，需要维护窗口时执行）
-- ALTER TABLE gps_device PARTITION BY RANGE(organ_id) (
--     PARTITION p0 VALUES LESS THAN (10000),
--     PARTITION p1 VALUES LESS THAN (20000),
--     PARTITION p2 VALUES LESS THAN (30000),
--     PARTITION p3 VALUES LESS THAN (40000),
--     PARTITION p4 VALUES LESS THAN (50000),
--     PARTITION p5 VALUES LESS THAN MAXVALUE
-- );

-- 方案B：按时间分区（如果有时间字段）
-- ALTER TABLE gps_device PARTITION BY RANGE(YEAR(create_time)) (
--     PARTITION p2022 VALUES LESS THAN (2023),
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ==================== 3. 高效复合索引设计（安全，立即可执行）====================
-- 基于查询模式的精确索引设计，只会提升性能，不影响现有功能

-- 检查索引是否存在的查询（可选执行）：
-- SHOW INDEX FROM gps_device WHERE Key_name = 'idx_gps_device_query_main';

-- 主查询索引 - 覆盖最常用的查询条件
CREATE INDEX IF NOT EXISTS idx_gps_device_query_main ON gps_device(organ_id, is_settle, is_delete, online_state, attention);

-- 搜索优化索引 - 覆盖搜索场景
CREATE INDEX IF NOT EXISTS idx_gps_device_search_optimized ON gps_device(organ_id, is_settle, is_delete, imei, device_name, plate_no);

-- 告警和超速查询索引
CREATE INDEX IF NOT EXISTS idx_gps_device_alarm_speed ON gps_device(is_alarm, is_speed, online_state);

-- 车牌号查询索引
CREATE INDEX IF NOT EXISTS idx_gps_device_plate_lookup ON gps_device(plate_no, organ_id);

-- ==================== 3. 关联表索引优化 ====================
-- uc_organ 表优化 - 使用覆盖索引减少回表查询
CREATE INDEX idx_uc_organ_path_covering ON uc_organ(path, id, organ_name);
CREATE INDEX idx_uc_organ_id_path ON uc_organ(id, path);

-- gps_car 表优化 - 覆盖索引包含常用字段
CREATE INDEX idx_gps_car_plate_covering ON gps_car(plate_no, vin_no, engine_no, car_owner, car_brand, car_model);

-- ==================== 4. 分片表索引优化 ====================
-- uc_user_device 分片表索引（需要在每个分片上执行）
-- 为每个分片表创建高效索引
-- 示例：uc_user_device_0, uc_user_device_1, etc.
CREATE INDEX idx_uc_user_device_0_main ON uc_user_device_0(user_id, imei, organ_id);
CREATE INDEX idx_uc_user_device_1_main ON uc_user_device_1(user_id, imei, organ_id);
-- 继续为其他分片创建相同索引...

-- 5. 查询优化建议
-- 建议在应用层面进行以下优化：
-- - 使用 LIMIT 限制返回结果数量
-- - 考虑分页查询时使用游标分页而不是 OFFSET
-- - 对于统计查询，考虑使用缓存机制
-- - 定期分析表统计信息：ANALYZE TABLE gps_device, uc_organ, gps_car;

-- 6. 表结构优化建议
-- 如果 path 字段经常用于 LIKE 查询，考虑：
-- ALTER TABLE uc_organ ADD COLUMN path_hash VARCHAR(32) GENERATED ALWAYS AS (MD5(path)) STORED;
-- CREATE INDEX idx_uc_organ_path_hash ON uc_organ(path_hash);

-- 7. 分区表建议（如果数据量很大）
-- 可以考虑按时间或组织 ID 对 gps_device 表进行分区
-- ALTER TABLE gps_device PARTITION BY RANGE(organ_id) (
--     PARTITION p0 VALUES LESS THAN (1000),
--     PARTITION p1 VALUES LESS THAN (2000),
--     ...
-- );
