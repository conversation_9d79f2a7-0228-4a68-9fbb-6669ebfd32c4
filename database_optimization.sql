-- GPS 设备管理系统数据库优化 SQL
-- 针对 /device/getorgandevicelist 接口的性能优化

-- 1. gps_device 表索引优化
-- 主要查询字段的复合索引
CREATE INDEX idx_gps_device_organ_settle_delete ON gps_device(organ_id, is_settle, is_delete);
CREATE INDEX idx_gps_device_online_state ON gps_device(online_state);
CREATE INDEX idx_gps_device_attention ON gps_device(attention);
CREATE INDEX idx_gps_device_plate_imei ON gps_device(plate_no, imei);
CREATE INDEX idx_gps_device_search ON gps_device(imei, device_name, plate_no, sim_no);


CREATE INDEX idx_uc_organ_path ON uc_organ(path);
CREATE INDEX idx_uc_organ_id_path ON uc_organ(id, path);


CREATE INDEX idx_gps_car_plate_no ON gps_car(plate_no);

-- 4. uc_user_device 分片表索引优化（需要在每个分片表上执行）
-- 示例：uc_user_device_0, uc_user_device_1, etc.
-- CREATE INDEX idx_uc_user_device_imei_user ON uc_user_device_0(imei, user_id);
-- CREATE INDEX idx_uc_user_device_organ ON uc_user_device_0(organ_id);

-- 5. 查询优化建议
-- 建议在应用层面进行以下优化：
-- - 使用 LIMIT 限制返回结果数量
-- - 考虑分页查询时使用游标分页而不是 OFFSET
-- - 对于统计查询，考虑使用缓存机制
-- - 定期分析表统计信息：ANALYZE TABLE gps_device, uc_organ, gps_car;

-- 6. 表结构优化建议
-- 如果 path 字段经常用于 LIKE 查询，考虑：
-- ALTER TABLE uc_organ ADD COLUMN path_hash VARCHAR(32) GENERATED ALWAYS AS (MD5(path)) STORED;
-- CREATE INDEX idx_uc_organ_path_hash ON uc_organ(path_hash);

-- 7. 分区表建议（如果数据量很大）
-- 可以考虑按时间或组织 ID 对 gps_device 表进行分区
-- ALTER TABLE gps_device PARTITION BY RANGE(organ_id) (
--     PARTITION p0 VALUES LESS THAN (1000),
--     PARTITION p1 VALUES LESS THAN (2000),
--     ...
-- );
