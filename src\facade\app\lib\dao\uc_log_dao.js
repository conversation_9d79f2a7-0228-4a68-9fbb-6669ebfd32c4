const moment = require("moment");
const mongo = require("../common/mongo_pool");

async function getLoggerMongoList(condition, queryParams) {
  const collection = `log${queryParams.collection}`;

  const findParams = {
    skip: queryParams.offset,
    limit: queryParams.pageSize,
    sort: [["createTime", -1]],
  };

  const [logTotal, logRows] = await Promise.all([
    mongo.count(collection, condition),
    mongo.findItems(collection, condition, findParams),
  ]);

  const logResults = logRows || [];
  logResults.forEach((log) => {
    log.createTime = log.createTime
      ? moment(log.createTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
  });

  return { total: logTotal || 0, rows: logResults };
}

module.exports = {
  getLoggerMongoList,
};
