const {getDaoShard} = require("../utils/sharding");
const mysqlDB = require("../common/mysql_pool");

function getAlarmReportSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  let alarmSql = "";
  if (queryParams.alarmTypes.length > 0) {
    alarmSql += ` and ga.alarm_type in(${queryParams.alarmTypes.join(",")})`;
  }
  // 筛选报警信息, 手机号或者微信号
  let alarmGroupSql = "";
  if (queryParams.isAlarm === 1) {
    alarmSql += ` and ( (gd.is_alarm_phone=1 and gd.alarm_phone>0) or (gd.is_alarm_wechat=1 and gd.alarm_wechat_msg_id>0) )
    and ga.lon>0 and ga.lat>0 and ga.alarm_type>0`;
    alarmGroupSql = " group by ga.imei,ga.alarm_type"; // 分组去重
  }

  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.id,gd.plate_no,gd.imei,gd.device_name,
  ga.id as alarm_id,ga.alarm_type,ga.alarm_time,ga.lat,ga.lon,ga.alarm_address, ga.start_time, ga.end_time,
  ga.location_type,ga.handle_status,ga.handle_user,ga.handle_time,ga.alarm_content,
  gd.is_alarm_phone,gd.is_alarm_sms,gd.is_alarm_wechat,
  gd.alarm_phone,gd.alarm_sms,gd.alarm_wechat_name,gd.alarm_wechat_msg_id,
  gd.alarm_phone_types,gd.alarm_sms_types,gd.alarm_wechat_types
  from gps_alarm_cycle ga
  join gps_device gd on ga.imei=gd.imei ${
    queryParams.imei === "" ? "" : "and gd.imei=:imei"
  }
  and gd.is_settle=0 and gd.is_delete=0
  join uc_organ uo on gd.organ_id=uo.id ${
    queryParams.organId === -1 ? "" : organSql
  }
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on ga.imei=uud.imei` : ""
  }
  where ga.alarm_time between :start_time and :end_time ${alarmSql}
  ${alarmGroupSql}
  ${queryParams.handleStatus === 0 ? " and ga.handle_status=0" : ""}
  ${queryParams.handleStatus === 1 ? " and ga.handle_status=1" : ""}
  order by ga.id desc`;
  console.log(sql);

  return mysqlDB.queryForPagination(sql, queryParams);
}

module.exports = {
  getAlarmReportSqlList,
};
