const logDao = require("../lib/dao/uc_log_dao");

async function getLoggerListLogic(queryParams) {
  let condition = { $and: [] };
  if (queryParams.logLevel !== "ALL") {
    condition.$and.push({ logLevel: queryParams.logLevel });
  }

  if (queryParams.source !== "ALL") {
    condition.$and.push({ source: queryParams.source });
  }

  if (queryParams.timeEnable) {
    condition.$and.push({
      createTime: {
        $gte: new Date(queryParams.startDate),
        $lt: new Date(queryParams.endDate),
      },
    });
  }

  if (queryParams.clientKey && queryParams.clientKey.length > 0) {
    const keyCon = { $or: [] };
    condition.$and.push(keyCon);
    queryParams.clientKey.forEach((key) => {
      keyCon.$or.push({ key: new RegExp(`^${key.trim()}`) });
    });
  }

  if (queryParams.logModule && queryParams.logModule.length > 0) {
    condition.$and.push({
      module: new RegExp(`^${queryParams.logModule.trim()}`),
    });
  }

  if (queryParams.logContent && queryParams.logContent.length > 0) {
    condition.$and.push({
      logContent: new RegExp(queryParams.logContent.trim()),
    });
  }

  if (queryParams.logType) {
    condition.$and.push({ logType: queryParams.logType });
  }

  if (condition.$and.length === 0) {
    condition = {};
  }

  return await logDao.getLoggerMongoList(condition, queryParams);
}

module.exports = {
  getLoggerListLogic,
};
