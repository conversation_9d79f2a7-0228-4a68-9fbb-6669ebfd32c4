const os = require("os");
const glob = require("glob");
const compose = require("koa-compose");
const auth = require("./middleware/auth");
const commFunc = require("./lib/common/comm_func");
const validator = require("./lib/common/validator_extend");

const METHOD_ENUM = ["get", "post", "put", "delete", "patch"];
const routeData = {
  systemType: 2,
  routes: [],
  repeatPerCollect: [],
};

// 直接执行next
async function routeNext(ctx, next) {
  await next();
}

// 转换每个接口多个权限中间件，返回一个方法数组
function composeM(middlewares) {
  if (!validator.isArray(middlewares)) {
    throw new Error(
      `[mountRoute]: middlewares ${JSON.stringify(
        middlewares
      )} Should be an Array of statement.`
    );
  }

  if (middlewares.filter((f) => !Object.keys(auth).includes(f)).length) {
    throw new Error(
      `[mountRoute]: middlewares ${JSON.stringify(
        middlewares
      )} Invalid middleware name.`
    );
  }

  if (middlewares.length < 1) {
    return [routeNext];
  }

  return middlewares.map((m) => auth[m]);
}

// 将单个路由导入 koa-router 中
function generateRoute(router, urlPrefix, excludeRules, rewriteAll, key, val) {
  const model = {
    method: "get",
    middlewares: [],
    routeDesc: "",
    buttonPerm: null,
    handler: "",
  };

  // let modifiedUrl = `${urlPrefix}${key === 'indexView' ? '/' : `/${key.toLowerCase()}`}`;
  let modifiedUrl = `${urlPrefix}${`/${key.toLowerCase()}`}`;
  if (excludeRules.includes(modifiedUrl)) {
    return;
  }

  switch (commFunc.getObjectType(val)) {
    case "object":
      model.method = (val.method || "get").toLowerCase();
      model.middlewares = val.middlewares || [];
      model.routeDesc = val.routeDesc || "";
      model.buttonPerm = val.buttonPerm || null;
      model.handler = val.handler || "";
      break;
    case "function":
      model.handler = val;
      break;
    case "asyncfunction":
      model.handler = val;
      break;
    default:
      return;
  }

  if (
    !METHOD_ENUM.includes(model.method) ||
    (commFunc.getObjectType(model.handler) !== "asyncfunction" &&
      commFunc.getObjectType(model.handler) !== "function")
  ) {
    throw new Error(
      `[mountRoute] -- invalid statement: ${JSON.stringify(model)}`
    );
  }

  modifiedUrl = rewriteAll.has(modifiedUrl)
    ? rewriteAll.get(modifiedUrl)
    : modifiedUrl;
  // modifiedUrl = modifiedUrl === '/basic/' ? ['/'] : modifiedUrl;
  router[model.method](
    modifiedUrl,
    compose(composeM(model.middlewares)),
    model.handler
  );

  routeData.routes.push({
    systemType: routeData.systemType,
    httpMethod: model.method.toUpperCase(),
    routeType: !modifiedUrl.includes("/v1/") ? 1 : 2,
    buttonPerm: model.buttonPerm,
    routePath: modifiedUrl,
    middlewares: model.middlewares,
    methodName: key,
    methodDesc: model.routeDesc,
  });

  const repeatPerList = routeData.routes.filter(
    (f) => f.buttonPerm === model.buttonPerm
  );
  if (model.buttonPerm !== null && repeatPerList.length > 1) {
    routeData.repeatPerCollect.push(repeatPerList);
  }
}

// 遍历 controller 所有接口挂载到 koa-router 上
function mountRoute(router, root, ctrls, options = {}) {
  const absoluteRoot =
    os.platform() === "win32" ? root.replace(/\\/gi, "/") : root;

  ctrls.forEach((f) => {
    if (!/.*(\.js)$/.test(f)) return;

    const ctrlPath = f.replace(/\.[^.]*$/, "");
    const ctrlClass = require(ctrlPath);

    const excludeRules = options.excludeRules || [];
    const rewritePrefix = options.rewritePrefix || new Map();
    const rewriteAll = options.rewriteAll || new Map();

    let urlPrefix = ctrlPath
      .replace(absoluteRoot, "")
      .replace(/\_|controller/g, "");
    urlPrefix = rewritePrefix.has(urlPrefix)
      ? rewritePrefix.get(urlPrefix)
      : urlPrefix;

    Object.keys(ctrlClass).forEach((key) => {
      generateRoute(
        router,
        `${urlPrefix}`,
        excludeRules,
        rewriteAll,
        key,
        ctrlClass[key]
      );
    });
  });
}

// 对外 API 接口挂载
function mountOutsideRoute(app, root, options) {
  mountRoute(app, root, glob.sync(`${root}/**/api_*_controller.js`), options);
}

// 使用 TOKEN 对内接口挂载
function mountInsideRoute(router, root, options) {
  mountRoute(
    router,
    root,
    glob.sync(`${root}/**/!(api_*_controller.js)`),
    options
  );

  const repeatPers = routeData.repeatPerCollect
    .reduce((value, key) => value.concat(key), [])
    .map((m) => m.buttonPerm);

  async function getRoutes(ctx, next) {
    ctx.request.body.routeData = routeData;
    ctx.request.body.repeatPers = [...new Set(repeatPers)];
    await next();
  }

  router.post(
    "/ucroute/initroute",
    auth.loginRequired,
    getRoutes,
    require("./controllers/uc_route_controller").insertRoute
  );
}

module.exports = {
  mountOutsideRoute,
  mountInsideRoute,
};
