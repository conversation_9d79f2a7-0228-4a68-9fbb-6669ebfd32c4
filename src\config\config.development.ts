import { env } from "process";
import { Config } from "../config";

export default (config: Config) => {
  config.redis.host = env.REDIS_HOST || "127.0.0.1";
  config.redis.password = env.REDIS_PASS || "";

  config.database.host = env.MYSQL_HOST || "127.0.0.1";
  config.database.username = env.MYSQL_USER || "root";
  config.database.password = env.MYSQL_PASSWORD || "123456";

  config.mongo.host = env.MONGO_HOST || "**************";
  config.mongo.username = env.MONGO_USER || "monitor";
  config.mongo.password = env.MONGO_PASSWORD || "Monitor_1109";
  config.mongo.database = env.MONGO_DATABASE || "gps_monitor";
  config.mongo.port = Number(env.MONGO_PORT || 27017);
};
