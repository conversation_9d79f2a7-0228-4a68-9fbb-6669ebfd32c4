const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const geography = require("../proxy/geography_js");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");

const secondBetDao = require("../lib/dao/second_bet_dao");

/**
 * 获取二押点列表
 * 请求参数:
 "organId": "机构Id",
 "searchKey": "搜索信息",
 "isShare": "是否分享",
 "secondBetType": "二押点类型",
 "dangerLevel": "危险级别",
 "shapeType": "形状类型"
 *
 */
async function getSecondBetListLogic(queryParams) {
  queryParams.status = 1;
  const [secondBetList, keyValConfig, keyIdenConfig] = await Promise.all([
    secondBetDao.getSecondBetSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "SHAPE_TYPE",
      "SECOND_BET_TYPE",
      "DANGER_LEVEL",
      "DATA_SOURCE",
    ]),
    global.context.getPropertymapKeyIden(["SHAPE_TYPE"]),
  ]);

  secondBetList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.secondBetTypeName = keyValConfig.SECOND_BET_TYPE[e.secondBetType] || "";
    e.dangerLevelName = keyValConfig.DANGER_LEVEL[e.dangerLevel] || "";
    e.shapeTypeName = keyValConfig.SHAPE_TYPE[e.shapeType] || "";
    e.shapeTypeIden = keyIdenConfig.SHAPE_TYPE[e.shapeType] || "";
    e.dataSourceName = keyValConfig.DATA_SOURCE[e.dataSource] || "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return secondBetList;
}

/**
 * 二押点数据验证
 *
 */
async function _validateSecondBetRequest(requestData) {
  const rules = [
    {
      field: "secondBetId",
      title: "二押点Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    },
    {
      field: "organId",
      title: "所属车组",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "i",
    },
    {
      field: "dangerLevel",
      title: "危险等级",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    {
      field: "shapeType",
      title: "形状类型",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "stopMinutes",
      title: "停留分钟数",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    {
      field: "isShare",
      title: "是否分享",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  let [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  model.address = model.address || "";
  commFunc.formatNullToString(model);
  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "SHAPE_TYPE",
    "SECOND_BET_TYPE",
    "DATA_SOURCE",
  ]);
  model.secondBetType = nameKeyConfig.SECOND_BET_TYPE.ZDY;
  model.dataSource = nameKeyConfig.DATA_SOURCE.HT;

  // 百度地图时，转化坐标
  if (`${model.shapeType}` === nameKeyConfig.SHAPE_TYPE.YX && model.isBaidu) {
    const lngLat = JSON.parse(model.shapeData);
    const [lng, lat] = geography.convertBaiduToGps(lngLat.lng, lngLat.lat);
    model.shapeData = JSON.stringify({ lng, lat });
  }

  if (`${model.shapeType}` === nameKeyConfig.SHAPE_TYPE.JX && model.isBaidu) {
    const lngLats = JSON.parse(model.shapeData);
    lngLats.forEach((f) => {
      const [lng, lat] = geography.convertBaiduToGps(f.lng, f.lat);
      f.lng = lng;
      f.lat = lat;
    });
    model.shapeData = JSON.stringify(lngLats);
  }

  return [undefined, model];
}

/**
 * 新增二押点
 * 请求参数:
 "organId": "机构Id",
 "secondBetName": "二押点名称",
 "dangerLevel": "危险等级",
 "shapeType": "形状类型",
 "shapeData": "二押点数据",
 "stopMinutes": "停留分钟数",
 "isShare": "是否分享",
 "remark": "备注",
 "userName": "操作人"
 *
 */
async function insertSecondBetLogic(insertData) {
  insertData.opt = "i";
  const [err, model] = await _validateSecondBetRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await secondBetDao.insertSecondBetSql(model);
}

/**
 * 更新二押点
 * 请求参数:
 "secondBetId": "二押点Id",
 "organId": "机构Id",
 "secondBetName": "二押点名称",
 "dangerLevel": "危险等级",
 "shapeType": "形状类型",
 "shapeData": "二押点数据",
 "stopMinutes": "停留分钟数",
 "isShare": "是否分享",
 "remark": "备注",
 "userName": "操作人"
 *
 */
async function updateSecondBetLogic(updateData) {
  updateData.opt = "u";
  const [err, model] = await _validateSecondBetRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await secondBetDao.updateSecondBetSql(model);
}

/**
 * 删除二押点
 * 请求参数:
 "secondBetIds": "二押点Ids"
 *
 */
async function deleteSecondBetLogic(deleteData) {
  if (
    !validator.isArray(deleteData.secondBetIds, { min: 1 }, [
      { rule: "isIntFormat", opt: { min: 1 }, required: true },
    ])
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  return await secondBetDao.deleteSecondBetSql(deleteData);
}

/**
 * 获取机构设备列表
 * 请求参数:
 "organIds": "机构Ids",
 "organId": "机构Id"
 *
 */
async function getOrganBetDeviceListLogic(queryParams) {
  const secondBetList = await secondBetDao.getOrganBetDeviceSqlList(
    queryParams
  );
  secondBetList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
  });
  return secondBetList;
}

/**
 * 获取二押点设备列表
 * 请求参数:
 "organId": "机构Id"
 *
 */
async function getSecondBetDeviceListLogic(queryParams) {
  const secondBetList = await secondBetDao.getSecondBetDeviceSqlList(
    queryParams
  );
  secondBetList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
  });
  return secondBetList;
}

/**
 * 新增二押点设备
 * 请求参数:
 "imei": "设备号",
 "userName": "操作人"
 *
 */
async function insertSecondBetDeviceLogic(insertList) {
  const rules = [
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "", required: true },
  ];

  if (!validator.isArray(insertList, { min: 1 }, rules)) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_FIELD_ERROR);
  }

  return await secondBetDao.insertSecondBetDeviceSql(insertList);
}

/**
 * 删除二押点设备
 * 请求参数:
 "betDeviceIds": "二押点设备Ids"
 *
 */
async function deleteSecondBetDeviceLogic(deleteData) {
  if (!validator.isIntArray(deleteData.betDeviceIds, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  return await secondBetDao.deleteSecondBetDeviceSql(deleteData);
}

module.exports = {
  getSecondBetListLogic,
  insertSecondBetLogic,
  updateSecondBetLogic,
  deleteSecondBetLogic,

  getOrganBetDeviceListLogic,
  getSecondBetDeviceListLogic,
  insertSecondBetDeviceLogic,
  deleteSecondBetDeviceLogic,
};
