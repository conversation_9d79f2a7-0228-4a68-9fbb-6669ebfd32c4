const moment = require("moment");
const trackDao = require("../lib/dao/history_track_dao");

async function getHistoryTrackListLogic(queryParams) {
  let condition = { $and: [] };

  const keyValConfig = await global.context.getPropertymapKeyVal([
    "LOCATION_TYPE",
  ]);

  condition.$and.push({ imei: queryParams.imei });
  const startTime = Number.parseInt(
    moment(queryParams.startTime).format("X"),
    10
  );
  const endTime = Number.parseInt(moment(queryParams.endTime).format("X"), 10);
  condition.$and.push({ locTime: { $lt: endTime, $gt: startTime } });

  if (queryParams.locType) {
    condition.$and.push({ locType: queryParams.locType });
  }

  return await trackDao.getHistoryTrackMongoList(condition, keyValConfig);
}

module.exports = {
  getHistoryTrackListLogic,
};
