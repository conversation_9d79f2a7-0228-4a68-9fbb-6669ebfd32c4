const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");

const alarmDao = require("../lib/dao/report_alarm_dao");

/**
 * 获取报警报表
 * 请求参数:
 "organId": "机构Id",
 "imei": "IMEI号",
 "startTime": "开始时间",
 "endTime": "结束时间",
 "alarmTypes": "报警类型数组",
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getAlarmReportListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    { field: "organId", title: "机构Id", rule: "isIntFormat", msg: "" },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "startTime",
      title: "开始时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
    {
      field: "endTime",
      title: "结束时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
    { field: "alarmTypes", title: "报警类型", rule: "isArray", msg: "" },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.startTime = moment(`${queryParams.startTime}:00`).format("X");
  queryParams.endTime = moment(`${queryParams.endTime}:00`).format("X");

  const [alarmList, keyValConfig] = await Promise.all([
    alarmDao.getAlarmReportSqlList(queryParams, currentUser),
    global.context.getPropertymapKeyVal(["ALARM_TYPE", "LOCATION_TYPE"]),
  ]);

  alarmList.rows.forEach((e) => {
    commFunc.formatNullToString(e);
    e.alarmTypeName = keyValConfig.ALARM_TYPE[e.alarmType] || "";
    e.locationTypeName = keyValConfig.LOCATION_TYPE[e.locationType] || "";
    e.handleStatusName = e.handleStatus === 1 ? "已处理" : "未处理";
    e.alarmTime = e.alarmTime
      ? moment.unix(e.alarmTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.handleTime = e.handleTime
      ? moment.unix(e.handleTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.startTime = e.startTime
      ? moment(e.startTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    e.endTime = e.endTime
      ? moment(e.endTime).format("YYYY-MM-DD HH:mm:ss")
      : "";
    e.currentAddress = "";
    e.alarmTypes = e.alarmTypes ? e.alarmTypes.toString().split(",") : [];
  });

  return alarmList;
}

module.exports = {
  getAlarmReportListLogic,
};
