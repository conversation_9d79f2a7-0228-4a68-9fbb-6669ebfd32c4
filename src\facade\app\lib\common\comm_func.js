/**
 * Created by Administrator on 2016/3/3.
 */
const _ = require("lodash");
const uuidv4 = require("uuid/v4");
const i18n = require("../../config/i18n_cn");
const validator = require("../../lib/common/validator_extend");

function calculationTreeCount(parentNode) {
  parentNode.totalCount = parentNode.count;
  for (const childNode of parentNode.children || []) {
    calculationTreeCount(childNode);
    parentNode.totalCount += childNode.totalCount;
  }
  return parentNode;
}
exports.calculationTreeCount = calculationTreeCount;

exports.randomPassword = function () {
  const text = "abcdefghijklmnopqrstuvwxyz1234567890";
  let password = "";
  const rand = function (min, max) {
    return Math.floor(Math.max(min, Math.random() * (max + 1)));
  };
  for (let i = 0; i < 6; i++) {
    password += text.charAt(rand(0, text.length));
  }
  return password;
};

exports.getUserClientIP = function (ctx) {
  return (
    ctx.request.headers["x-forwarded-for"] ||
    ctx.request.ip ||
    ctx.request.socket.remoteAddress
  );
};

// 检测数据准确性
exports._ruleTranFunc = function (rule, model) {
  const cloneModel = JSON.parse(JSON.stringify(model));
  if (cloneModel.hasOwnProperty("rule") || cloneModel.hasOwnProperty("opt")) {
    delete cloneModel.rule;
    delete cloneModel.opt; // 命名和rule对象的opt冲突
  }
  if (rule instanceof Array) {
    return rule.map((m) => Object.assign(m || {}, cloneModel));
  }
  if (typeof rule === "string") {
    return rule.split(",").map((m) => Object.assign({ name: m }, cloneModel));
  }
  return [];
};

exports.checkRequestData = function (requestData, models) {
  try {
    models = models.filter(
      (f) => `${requestData.opt || "i,u"},*`.indexOf(f.opt || "*") !== -1
    );

    const propertys = Object.getOwnPropertyNames(requestData);

    // 检测前端必传项
    const transList = models.filter(
      (f) => f.required && !propertys.includes(f.field)
    );
    if (transList.length > 0) {
      return [`${transList[0].title}为${i18n.REQUEST_INPUT_FIELD_MUST}`];
    }

    const modelList = models.filter((f) => propertys.includes(f.field));

    // 检测必填项
    const requireList = modelList.filter(
      (f) => f.required && validator.isNullOrEmpty(requestData[f.field])
    );
    if (requireList.length > 0) {
      return [
        `${requireList[0].title}${
          i18n.REQUEST_INPUT_DATA_EMPTY +
          (requireList[0].msg ? `，${requireList[0].msg}` : "")
        }`,
      ];
    }

    let accuracyList = modelList.filter(
      (f) =>
        !validator.isNullOrEmpty(requestData[f.field]) &&
        (f.rule || []).length > 0
    );
    accuracyList = accuracyList.map((m) => ({
      rules: this._ruleTranFunc(m.rule, m),
    }));

    if (accuracyList.every((f) => f.rules.length === 0)) {
      return [`${i18n.MODELS_UNDEFIND_FIELD}`];
    }

    accuracyList = _.flattenDeep(accuracyList.map((m) => m.rules));
    accuracyList = accuracyList.filter(
      (f) =>
        !(f.opt
          ? validator[f.name](requestData[f.field], f.opt)
          : validator[f.name](requestData[f.field]))
    );

    if (accuracyList.length > 0) {
      return [
        `${accuracyList[0].title}${
          i18n.REQUEST_INPUT_DATA_ERROR +
          (accuracyList[0].msg ? `，${accuracyList[0].msg}` : "")
        }`,
      ];
    }
  } catch (err) {
    return [err ? err.message || err : undefined];
  }
  return [undefined, requestData];
};

exports.checkRequestDataBool = function (requestData, models) {
  try {
    models = models.filter(
      (f) => `${requestData.opt || "i,u"},*`.indexOf(f.opt || "*") !== -1
    );

    const propertys = Object.getOwnPropertyNames(requestData);

    // 检测前端必传项
    const transList = models.filter(
      (f) => f.required && !propertys.includes(f.field)
    );
    if (transList.length > 0) {
      return false;
    }

    const modelList = models.filter((f) => propertys.includes(f.field));

    // 检测必填项
    const requireList = modelList.filter(
      (f) => f.required && validator.isNullOrEmpty(requestData[f.field])
    );
    if (requireList.length > 0) {
      return false;
    }

    let accuracyList = modelList.filter(
      (f) =>
        !validator.isNullOrEmpty(requestData[f.field]) &&
        (f.rule || []).length > 0
    );
    accuracyList = accuracyList.map((m) => ({
      rules: this._ruleTranFunc(m.rule, m),
    }));

    if (accuracyList.every((f) => f.rules.length === 0)) {
      return false;
    }

    accuracyList = _.flattenDeep(accuracyList.map((m) => m.rules));
    accuracyList = accuracyList.filter(
      (f) =>
        !(f.opt
          ? validator[f.name](requestData[f.field], f.opt)
          : validator[f.name](requestData[f.field]))
    );

    if (accuracyList.length > 0) {
      return false;
    }
  } catch (err) {
    return false;
  }
  return true;
};

function mathSum(arr) {
  return arr.reduce((prev, current) => prev + Number.parseInt(current, 10), 0);
}
exports.mathSum = mathSum;

exports.getObjectType = function (o) {
  const s = Object.prototype.toString.call(o);
  return s.slice(s.indexOf(" ") + 1, s.length - 1).toLowerCase();
};

// 生成6位数的uuid
exports.randomShortUUID = function () {
  const chars = [
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "m",
    "n",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
  ];

  const shortBuffer = [];
  const uuid = uuidv4().replace(/\-/gi, "");

  for (let i = 0; i < 6; i++) {
    const str = uuid.substring(i * 4, i * 4 + 4);

    const x = Number.parseInt(str, 16);
    shortBuffer.push(chars[x % 0x39]);
  }
  return shortBuffer.join("");
};

exports.formatNullToString = function (data) {
  const keys = Object.keys(data);
  if (
    this.getObjectType(keys) !== "array" ||
    this.getObjectType(data) !== "object"
  ) {
    return data;
  }
  keys.forEach((e) => {
    if (data[e] === null) {
      data[e] = "";
    }
  });
  return data;
};

exports.compact = function (arr) {
  return arr.filter((val) => !(!val || val === ""));
};

// 手机或银行卡星号占位
exports.formatAsterisk = function (number) {
  if (validator.isLengthCorrect(number, { min: 16, max: 19 })) {
    return `${number.slice(0, 4)} **** **** ${number.slice(-4)}`;
  }

  if (validator.isInt11(number)) {
    return `${number.slice(0, 3)} **** ${number.slice(-4)}`;
  }

  return number;
};

// 取优先级较高，并且不为空的整数值
// 规则：arg1 > arg2 > arg3 ...
exports.getPriorityValue = function (...args) {
  return [...args].filter((f) =>
    validator.isFloatFormat(f, { min: 0, digit: 2 })
  )[0];
};

// 格式化千分位
exports.thousandsFormat = function (num) {
  num = typeof num === "number" ? `${num}` : "";
  return num ? num.replace(/(?=(?!(\b))(\d{3})+$)/g, ",") : num;
};

exports.buildTreeData = function (originalData, parentNode) {
  const groupData = _.groupBy(originalData, (g) => g.parentId);
  for (const key of Object.keys(groupData)) {
    let childNode = parentNode;
    if (`${parentNode.id}` !== `${key}`) {
      childNode = originalData.find((p) => `${p.id}` === `${key}`);
    }
    if (childNode) {
      childNode.children = groupData[key];
      childNode.totalCount =
        mathSum(groupData[key].map((m) => m.count)) + childNode.count;
    }
  }
  return parentNode;
};

// 下划线转换驼峰
function toCamelCase(str) {
  return str.replace(/_(\w)/g, (all, letter) => {
    return letter.toUpperCase();
  });
}
exports.toCamelCase = toCamelCase;

// 驼峰转换下划线
function toUnderLine(name) {
  return name.replace(/([A-Z])/g, "_$1").toLowerCase();
}
exports.toUnderLine = toUnderLine;

exports.camelCaseObjectKeys = function (data) {
  if (!data && typeof data !== "object") {
    return data;
  }

  const res = {};
  for (const k in data) {
    res[toCamelCase(k)] = data[k];
  }

  return res;
};

exports.secondsToHumanize = function (seconds) {
  const mi = 60;
  const hh = mi * 60;
  const dd = hh * 24;

  const day = Number.parseInt(seconds / dd, 10);
  const hour = Number.parseInt((seconds - day * dd) / hh, 10);
  const minute = Number.parseInt((seconds - day * dd - hour * hh) / mi, 10);
  const second = seconds - day * dd - hour * hh - minute * mi;
  let str = "";
  str += day > 0 ? `${day}天` : "";
  str += hour > 0 ? `${hour}小时` : "";
  str += minute > 0 ? `${minute}分钟` : "";
  str += second > 0 ? `${second}秒` : "";
  return str;
};

exports.secondsToShortHumanize = function (seconds) {
  const mi = 60;
  const hh = mi * 60;
  const dd = hh * 24;

  const day = Number.parseInt(seconds / dd, 10);
  const hour = Number.parseInt((seconds - day * dd) / hh, 10);
  const minute = Number.parseInt((seconds - day * dd - hour * hh) / mi, 10);

  let str = "";
  str += day > 0 ? `${day}天` : "";
  if (str) {
    return str;
  }
  str += hour > 0 ? `${hour}小时` : "";
  if (str) {
    return str;
  }
  str += minute > 0 ? `${minute}分钟` : "";
  return str;
};

exports.excelArrayToObject = function (keys, array) {
  const data = {};
  const keySplits = keys.split(",");
  keySplits.forEach((key, i) => {
    key = key.replace("\n      ", "");
    data[key] = array[i] || "";
  });
  return data;
};

exports.encodeImei = function (imei) {
  const sTemp = [];
  const sIp = Buffer.alloc(4);
  let iHigt = 0;

  if (imei.length === 11) {
    sTemp.push(Number.parseInt(imei.substr(3, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(5, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(7, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(9, 2), 10));
    iHigt = Number.parseInt(imei.substr(1, 2), 10) - 30;
  } else if (imei.length === 10) {
    sTemp.push(Number.parseInt(imei.substr(2, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(4, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(6, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(8, 2), 10));
    iHigt = Number.parseInt(imei.substr(1, 2), 10) - 30;
  } else if (imei.length === 9) {
    sTemp.push(Number.parseInt(imei.substr(1, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(3, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(5, 2), 10));
    sTemp.push(Number.parseInt(imei.substr(7, 2), 10));
    iHigt = Number.parseInt(imei.substr(0, 2), 10);
  } else {
    return imei;
  }

  if ((iHigt & 0x8) !== 0) {
    sIp[0] = sTemp[0] | 128;
  } else {
    sIp[0] = sTemp[0];
  }

  if ((iHigt & 0x4) !== 0) {
    sIp[1] = sTemp[1] | 128;
  } else {
    sIp[1] = sTemp[1];
  }

  if ((iHigt & 0x2) !== 0) {
    sIp[2] = sTemp[2] | 128;
  } else {
    sIp[2] = sTemp[2];
  }

  if ((iHigt & 0x1) !== 0) {
    sIp[3] = sTemp[3] | 128;
  } else {
    sIp[3] = sTemp[3];
  }
  return sIp.toString("hex");
};
