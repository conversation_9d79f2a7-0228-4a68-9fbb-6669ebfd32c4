/**
 * 上亿级数据量优化版本的设备 DAO
 * 针对大数据量场景的数据库查询优化
 */

const mysqlDB = require("../lib/common/mysql_pool");
const { getDaoShard } = require("../lib/utils/sharding");

/**
 * 优化版本的设备列表查询
 * 针对上亿级数据量的查询优化
 */
function getOrganDeviceSqlListOptimized(queryParams, currentUser, dbShard = 0) {
  currentUser = currentUser || {};
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  const ucUserDeviceWhiteTable = getDaoShard('uc_user_device', queryParams.whiteUserId);
  
  // 构建分区查询条件
  let organSql = "inner join uc_organ uo1 on gd.organ_id = uo1.id ";
  organSql = queryParams.hasSubOrgan === 1
    ? `${organSql} and uo1.path like concat('%,',:organ_id,',%')`
    : `${organSql} and gd.organ_id = :organ_id`;

  // 优化的 SQL 查询 - 使用覆盖索引和分区查询
  const sql = `
    select /*+ USE_INDEX(gd, idx_gps_device_query_main) */
      gd.organ_id, uo.organ_name, gd.plate_no, gd.is_speed, gd.is_alarm,
      gc.vin_no, gc.engine_no, gc.car_owner, gc.car_brand, gc.car_model, 
      gc.owner_phone, gc.loan_amount, gc.policy_no, gc.policy_exp_date, gc.remark as car_remark,
      gd.id as device_id, gd.imei, gd.device_name, gd.device_type, gd.device_model, 
      gd.install_location, gd.installer, gd.install_date, gd.sim_no, gd.iccid, 
      gd.service_pswd, gd.sim_expire_time, gd.sim_state, gd.attention, gd.alarm_type, 
      gd.last_signal_time, gd.last_location_time, gd.online_state, gd.last_online_time, 
      gd.last_offline_time, gd.expire_time, gd.active_time, gd.remark as device_remark, 
      gd.create_time, gd.is_alarm_phone, gd.is_alarm_sms, gd.is_alarm_wechat, 
      gd.alarm_phone, gd.alarm_sms, gd.alarm_wechat_name, gd.alarm_wechat_msg_id,
      gd.alarm_phone_types, gd.alarm_sms_types, gd.alarm_wechat_types
      ${queryParams.whiteUserId ? `, !ISNULL(uudw.imei) as is_white` : ", 0 as is_white"}
      ${queryParams.noticeUserId && queryParams.noticeType ? `, !ISNULL(uund.imei) as is_notice_imei` : ", 0 as is_notice_imei"}
    from gps_device gd
    inner join uc_organ uo on gd.organ_id = uo.id 
      and uo.path like concat('%,',:current_organ_id,',%')
    inner join gps_car gc on gd.plate_no = gc.plate_no
    ${queryParams.isAttention === 0 ? organSql : ""}
    ${currentUser.type === 0 ? `inner join ${ucUserDeviceTable} uud on gd.imei = uud.imei and uud.user_id = ${currentUser.userId}` : ""}
    ${queryParams.whiteUserId ? `left join ${ucUserDeviceWhiteTable} uudw on gd.imei = uudw.imei and uudw.user_id = ${queryParams.whiteUserId}` : ""}
    ${queryParams.noticeUserId && queryParams.noticeType ? `left join uc_user_notice_device uund on gd.imei = uund.imei and uund.user_id = ${queryParams.noticeUserId} and uund.type = '${queryParams.noticeType}'` : ""}
    where gd.is_settle = 0 and gd.is_delete = 0 
      ${queryParams.isAttention === 1 ? " and gd.attention > 0" : ""} 
      and (:online_state = -1 or gd.online_state = :online_state)
      ${buildSearchCondition(queryParams)}
      ${queryParams.imeis ? " and gd.imei in (" + queryParams.imeis + ")" : ""}
    order by gd.is_alarm desc, gd.is_speed desc, FIELD(gd.online_state,'1','2','0') asc
  `;

  return mysqlDB.queryForPaginationOptimized(sql, queryParams);
}

/**
 * 优化版本的设备统计查询
 */
function getOrganDeviceStaSqlListOptimized(queryParams, currentUser, dbShard = 0) {
  currentUser = currentUser || {};
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  const ucUserDeviceWhiteTable = getDaoShard('uc_user_device', queryParams.whiteUserId);
  
  let organSql = "inner join uc_organ uo1 on gd.organ_id = uo1.id ";
  organSql = queryParams.hasSubOrgan === 1
    ? `${organSql} and uo1.path like concat('%,',:organ_id,',%')`
    : `${organSql} and gd.organ_id = :organ_id`;

  // 使用覆盖索引优化统计查询
  const sql = `
    select /*+ USE_INDEX(gd, idx_gps_device_query_main) */
      count(gd.id) as total,
      sum(case when gd.online_state = :online then 1 else 0 end) as online,
      sum(case when gd.online_state = :offline then 1 else 0 end) as offline,
      sum(case when gd.online_state = :notuse then 1 else 0 end) as notuse,
      sum(case when gd.attention > 0 then 1 else 0 end) as attention_count
    from gps_device gd
    inner join uc_organ uo on gd.organ_id = uo.id 
      and uo.path like concat('%,',:current_organ_id,',%')
    ${organSql}
    ${currentUser.type === 0 ? `inner join ${ucUserDeviceTable} uud on gd.imei = uud.imei and uud.user_id = ${currentUser.userId}` : ""}
    ${queryParams.whiteUserId ? `inner join ${ucUserDeviceWhiteTable} uudw on gd.imei = uudw.imei and uudw.user_id = ${queryParams.whiteUserId}` : ""}
    where gd.is_settle = 0 and gd.is_delete = 0
  `;

  return mysqlDB.one(sql, queryParams);
}

/**
 * 构建搜索条件 - 优化模糊查询
 */
function buildSearchCondition(queryParams) {
  if (!queryParams.searchKey) {
    return "";
  }

  // 优化搜索条件，避免使用 concat 进行模糊查询
  const searchKey = queryParams.searchKey.trim();
  
  // 如果是精确的 IMEI 查询（数字），使用精确匹配
  if (/^\d+$/.test(searchKey)) {
    return ` and (gd.imei = '${searchKey}' or gd.imei like '${searchKey}%')`;
  }
  
  // 如果是车牌号格式，优先匹配车牌号
  if (/^[A-Z\u4e00-\u9fa5]/.test(searchKey)) {
    return ` and (gd.plate_no like '${searchKey}%' or uo.organ_name like '%${searchKey}%' or gd.device_name like '%${searchKey}%')`;
  }
  
  // 通用模糊查询
  return ` and (uo.organ_name like '%${searchKey}%' or gd.imei like '%${searchKey}%' or gd.device_name like '%${searchKey}%' or gd.plate_no like '%${searchKey}%' or gd.sim_no like '%${searchKey}%')`;
}

/**
 * 游标分页查询 - 替代传统的 OFFSET 分页
 */
function getOrganDeviceSqlListCursor(queryParams, currentUser, lastId = 0) {
  currentUser = currentUser || {};
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  
  let organSql = "inner join uc_organ uo1 on gd.organ_id = uo1.id ";
  organSql = queryParams.hasSubOrgan === 1
    ? `${organSql} and uo1.path like concat('%,',:organ_id,',%')`
    : `${organSql} and gd.organ_id = :organ_id`;

  const sql = `
    select 
      gd.organ_id, uo.organ_name, gd.plate_no, gd.is_speed, gd.is_alarm,
      gd.id as device_id, gd.imei, gd.device_name, gd.device_type, gd.device_model,
      gd.online_state, gd.last_online_time, gd.attention
    from gps_device gd
    inner join uc_organ uo on gd.organ_id = uo.id 
      and uo.path like concat('%,',:current_organ_id,',%')
    ${queryParams.isAttention === 0 ? organSql : ""}
    ${currentUser.type === 0 ? `inner join ${ucUserDeviceTable} uud on gd.imei = uud.imei and uud.user_id = ${currentUser.userId}` : ""}
    where gd.is_settle = 0 and gd.is_delete = 0 
      and gd.id > :last_id
      ${queryParams.isAttention === 1 ? " and gd.attention > 0" : ""} 
      and (:online_state = -1 or gd.online_state = :online_state)
      ${buildSearchCondition(queryParams)}
    order by gd.id asc
    limit :page_size
  `;

  queryParams.lastId = lastId;
  queryParams.pageSize = Math.min(queryParams.pageSize || 20, 100); // 限制最大页面大小

  return mysqlDB.all(sql, queryParams);
}

/**
 * 分区表查询 - 针对已分区的表
 */
function getOrganDeviceSqlListPartitioned(queryParams, currentUser, partitionName) {
  const sql = `
    select 
      gd.organ_id, uo.organ_name, gd.plate_no, gd.is_speed, gd.is_alarm,
      gd.id as device_id, gd.imei, gd.device_name, gd.device_type, gd.device_model,
      gd.online_state, gd.last_online_time, gd.attention
    from gps_device partition(${partitionName}) gd
    inner join uc_organ uo on gd.organ_id = uo.id 
    where gd.is_settle = 0 and gd.is_delete = 0 
      and gd.organ_id = :organ_id
      and (:online_state = -1 or gd.online_state = :online_state)
      ${buildSearchCondition(queryParams)}
    order by gd.is_alarm desc, gd.is_speed desc, gd.id desc
    limit :offset, :page_size
  `;

  return mysqlDB.queryForPagination(sql, queryParams);
}

/**
 * 批量获取设备基础信息 - 用于缓存预热
 */
function getBatchDeviceBasicInfo(imeiList) {
  if (!imeiList || imeiList.length === 0) {
    return Promise.resolve([]);
  }

  const placeholders = imeiList.map(() => '?').join(',');
  const sql = `
    select imei, device_name, plate_no, online_state, organ_id
    from gps_device 
    where imei in (${placeholders}) and is_delete = 0
  `;

  return mysqlDB.all(sql, imeiList);
}

/**
 * 获取热点组织的设备数量 - 用于缓存预热
 */
function getHotOrganDeviceCount() {
  const sql = `
    select organ_id, count(*) as device_count
    from gps_device 
    where is_settle = 0 and is_delete = 0
    group by organ_id
    having device_count > 100
    order by device_count desc
    limit 50
  `;

  return mysqlDB.all(sql);
}

/**
 * 预聚合统计数据 - 定时任务使用
 */
function preAggregateOrganStats() {
  const sql = `
    insert into organ_device_stats_cache (organ_id, total_count, online_count, offline_count, attention_count, update_time)
    select 
      gd.organ_id,
      count(*) as total_count,
      sum(case when gd.online_state = 1 then 1 else 0 end) as online_count,
      sum(case when gd.online_state = 2 then 1 else 0 end) as offline_count,
      sum(case when gd.attention > 0 then 1 else 0 end) as attention_count,
      now() as update_time
    from gps_device gd
    where gd.is_settle = 0 and gd.is_delete = 0
    group by gd.organ_id
    on duplicate key update
      total_count = values(total_count),
      online_count = values(online_count),
      offline_count = values(offline_count),
      attention_count = values(attention_count),
      update_time = values(update_time)
  `;

  return mysqlDB.update(sql);
}

/**
 * 从预聚合表获取统计数据
 */
function getOrganStatsFromCache(organId, hasSubOrgan) {
  let sql;
  
  if (hasSubOrgan === 1) {
    sql = `
      select 
        sum(total_count) as total,
        sum(online_count) as online,
        sum(offline_count) as offline,
        sum(attention_count) as attention_count
      from organ_device_stats_cache odsc
      inner join uc_organ uo on odsc.organ_id = uo.id
      where uo.path like concat('%,',:organ_id,',%')
        and odsc.update_time > date_sub(now(), interval 10 minute)
    `;
  } else {
    sql = `
      select 
        total_count as total,
        online_count as online,
        offline_count as offline,
        attention_count as attention_count
      from organ_device_stats_cache
      where organ_id = :organ_id
        and update_time > date_sub(now(), interval 10 minute)
    `;
  }

  return mysqlDB.one(sql, { organId });
}

module.exports = {
  getOrganDeviceSqlListOptimized,
  getOrganDeviceStaSqlListOptimized,
  getOrganDeviceSqlListCursor,
  getOrganDeviceSqlListPartitioned,
  getBatchDeviceBasicInfo,
  getHotOrganDeviceCount,
  preAggregateOrganStats,
  getOrganStatsFromCache,
  buildSearchCondition
};
