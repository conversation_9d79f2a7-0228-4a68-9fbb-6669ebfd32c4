const mongo = require("mongoskin");
const staticConfig = require("../../config/config_static");

class MongoPool {
  constructor() {
    console.log("[connect mongo]", staticConfig.mongoConfig);
    this.db = mongo.db(
      staticConfig.mongoConfig.connstr,
      staticConfig.mongoConfig.options
    );
  }

  count(collection, condition) {
    return new Promise((resolve, reject) => {
      this.db.collection(collection).count(condition, (err, response) => {
        if (err) {
          console.log("[连接失败]", err);
          reject(err);
          return;
        }
        resolve(response);
      });
    });
  }

  findItems(collection, condition, findParams) {
    return new Promise((resolve, reject) => {
      this.db
        .collection(collection)
        .findItems(condition, findParams, (err, response) => {
          if (err) {
            reject(err);
            return;
          }
          resolve(response);
        });
    });
  }
}

module.exports = new MongoPool();
