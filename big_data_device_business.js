/**
 * 上亿级数据量优化版本的设备业务逻辑
 * 针对大数据量场景的深度优化
 */

const moment = require("moment");
const validator = require("../lib/common/validator");
const commFunc = require("../lib/common/comm_func");
const deviceDao = require("../lib/dao/device_dao");
const i18n = require("../config/i18n_cn");
const { RetJson } = require("../lib/common/ret_json");
const context = global.context;

/**
 * 大数据量优化版本的设备列表获取
 */
async function getOrganDeviceListLogicOptimized(queryParams, currentUser) {
  currentUser = currentUser || {};

  // 参数预处理
  queryParams.isAttention = queryParams.organId === "-10" ? 1 : 0;
  queryParams.hasSubOrgan = queryParams.hasSubOrgan === "true" ? 1 : 0;

  if (
    queryParams.isAttention !== 1 &&
    !validator.isIntFormat(queryParams.organId, { min: 1 })
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  // 1. 多级缓存策略
  const cacheKey = `device_list_${queryParams.organId}_${queryParams.hasSubOrgan}_${queryParams.onlineState}_${queryParams.currentPage}_${queryParams.pageSize}`;
  const cacheResult = await getCachedResult(cacheKey);
  if (cacheResult) {
    return cacheResult;
  }

  // 2. 配置数据缓存优化
  const configData = await getConfigDataCached();
  const { nameKeyConfig, keyValConfig, keyIdenConfig } = configData;

  // 处理在线状态参数
  const onlineState = nameKeyConfig.ONLINE_STATE[
    (queryParams.deviceState || "").replace("ONLINE_STATE.", "")
  ];
  queryParams.onlineState = onlineState === undefined ? -1 : onlineState;
  queryParams.online = nameKeyConfig.ONLINE_STATE.ONLINE;
  queryParams.offline = nameKeyConfig.ONLINE_STATE.OFFLINE;
  queryParams.notuse = nameKeyConfig.ONLINE_STATE.NOTUSE;

  if (!validator.isIntFormat(queryParams.onlineState, { min: -1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  // 3. 分库分表路由
  const dbShard = getDbShard(queryParams.organId);

  // 4. 并行查询优化 - 分离统计查询
  const deviceListPromise = deviceDao.getOrganDeviceSqlListOptimized(queryParams, currentUser, dbShard);
  const statsPromise = getDeviceStatsAsync(queryParams, currentUser, dbShard);

  const [deviceList] = await Promise.all([deviceListPromise]);

  // 5. 批量 Redis 查询优化
  const processedDeviceList = await processDeviceListOptimized(
    deviceList,
    keyValConfig,
    keyIdenConfig,
    nameKeyConfig
  );

  // 6. 异步获取统计数据
  const deviceStateCount = await statsPromise;

  const result = [processedDeviceList, deviceStateCount];

  // 7. 缓存结果（短时间缓存）
  await cacheResult(cacheKey, result, 30); // 30秒缓存

  return result;
}

/**
 * 多级缓存获取
 */
async function getCachedResult(cacheKey) {
  try {
    // L1 缓存 - 热点数据
    const l1Cache = await global.context.redisClient.get(`l1:${cacheKey}`);
    if (l1Cache) {
      return JSON.parse(l1Cache);
    }

    // L2 缓存 - 常用数据
    const l2Cache = await global.context.redisClient.get(`l2:${cacheKey}`);
    if (l2Cache) {
      // 提升到 L1 缓存
      await global.context.redisClient.setex(`l1:${cacheKey}`, 30, l2Cache);
      return JSON.parse(l2Cache);
    }

    return null;
  } catch (error) {
    global.context.logger.warn('缓存获取失败:', error);
    return null;
  }
}

/**
 * 缓存结果
 */
async function cacheResult(cacheKey, result, ttl) {
  try {
    const serialized = JSON.stringify(result);
    await Promise.all([
      global.context.redisClient.setex(`l1:${cacheKey}`, ttl, serialized),
      global.context.redisClient.setex(`l2:${cacheKey}`, ttl * 10, serialized)
    ]);
  } catch (error) {
    global.context.logger.warn('缓存设置失败:', error);
  }
}

/**
 * 配置数据缓存
 */
async function getConfigDataCached() {
  const configCacheKey = 'device_config_cache_v2';

  try {
    const cached = await global.context.redisClient.get(configCacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
  } catch (error) {
    global.context.logger.warn('配置缓存获取失败:', error);
  }

  // 缓存不存在，从数据库获取
  const [nameKeyConfig, keyValConfig, keyIdenConfig] = await Promise.all([
    context.getPropertymapNameKey(["ONLINE_STATE", "SIM_STATE"]),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE", "DEVICE_MODEL", "LOCATION_TYPE",
      "SIM_STATE", "ONLINE_STATE", "ALARM_TYPE",
    ]),
    global.context.getPropertymapKeyIden(["ONLINE_STATE", "DEVICE_TYPE"]),
  ]);

  const configData = { nameKeyConfig, keyValConfig, keyIdenConfig };

  // 缓存 30 分钟
  try {
    await global.context.redisClient.setex(configCacheKey, 1800, JSON.stringify(configData));
  } catch (error) {
    global.context.logger.warn('配置缓存设置失败:', error);
  }

  return configData;
}

/**
 * 分库分表路由
 */
function getDbShard(organId) {
  // 根据组织ID进行分库路由
  const shardCount = parseInt(process.env.DB_SHARD_COUNT) || 8;
  return organId % shardCount;
}

/**
 * 异步获取统计数据
 */
async function getDeviceStatsAsync(queryParams, currentUser, dbShard) {
  // 先尝试从缓存获取
  const statsCacheKey = `device_stats_${queryParams.organId}_${queryParams.hasSubOrgan}`;

  try {
    const cached = await global.context.redisClient.get(statsCacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
  } catch (error) {
    global.context.logger.warn('统计缓存获取失败:', error);
  }

  // 缓存不存在，异步计算
  const stats = await deviceDao.getOrganDeviceStaSqlListOptimized(queryParams, currentUser, dbShard);

  // 缓存统计结果 5 分钟
  try {
    await global.context.redisClient.setex(statsCacheKey, 300, JSON.stringify(stats));
  } catch (error) {
    global.context.logger.warn('统计缓存设置失败:', error);
  }

  return stats;
}

/**
 * 优化的设备列表处理
 */
async function processDeviceListOptimized(deviceList, keyValConfig, keyIdenConfig, nameKeyConfig) {
  if (!deviceList.rows || deviceList.rows.length === 0) {
    return deviceList;
  }

  // 批量获取设备缓存
  const deviceCacheMap = await getBatchDeviceCache(deviceList.rows.map(e => e.imei));

  // 预计算常用值
  const currentTimestamp = moment().format("X");
  const overtimeThreshold = moment().add(-5, "minutes").valueOf() / 1000;
  const deviceStateTimeOffset = parseInt(process.env.DEVICE_STATE_TIME) || 0;

  // 并行处理设备数据
  const processedRows = await Promise.all(
    deviceList.rows.map(device =>
      processDeviceData(device, deviceCacheMap, keyValConfig, keyIdenConfig, nameKeyConfig, {
        currentTimestamp,
        overtimeThreshold,
        deviceStateTimeOffset
      })
    )
  );

  deviceList.rows = processedRows;

  // 添加索引
  deviceList.rows.forEach((row, i) => {
    row.index = i + 1;
  });

  return deviceList;
}

/**
 * 批量获取设备缓存
 */
async function getBatchDeviceCache(imeiList) {
  const deviceCacheMap = new Map();

  if (imeiList.length === 0) {
    return deviceCacheMap;
  }

  try {
    // 使用 Redis Pipeline 批量获取
    const pipeline = global.context.redisClient.pipeline();
    imeiList.forEach(imei => {
      pipeline.hgetall(`device:${imei}`);
    });

    const cacheResults = await pipeline.exec();

    imeiList.forEach((imei, index) => {
      if (cacheResults[index] && cacheResults[index][1]) {
        deviceCacheMap.set(imei, cacheResults[index][1]);
      }
    });
  } catch (error) {
    global.context.logger.warn('批量获取设备缓存失败:', error);
  }

  return deviceCacheMap;
}

/**
 * 单个设备数据处理
 */
async function processDeviceData(device, deviceCacheMap, keyValConfig, keyIdenConfig, nameKeyConfig, constants) {
  // 基础字段映射
  Object.assign(device, {
    deviceTypeName: keyValConfig.DEVICE_TYPE[device.deviceType] || "",
    deviceTypeIden: keyIdenConfig.DEVICE_TYPE[device.deviceType] || "",
    deviceModelName: keyValConfig.DEVICE_MODEL[device.deviceModel] || "",
    simStateName: keyValConfig.SIM_STATE[device.simState] || "",
    alarmTypeName: keyValConfig.ALARM_TYPE[device.alarmType] || "",
    speed: "",
    electric: "",
    locationType: 0,
    lon: 0,
    lat: 0,
    currentAddress: ""
  });

  // 日期格式化
  device.policyExpDate = device.policyExpDate ? moment.unix(device.policyExpDate).format("YYYY-MM-DD") : "";
  device.installDate = device.installDate ? moment.unix(device.installDate).format("YYYY-MM-DD") : "";
  device.simExpireTime = device.simExpireTime ? moment.unix(device.simExpireTime).format("YYYY-MM-DD") : "";
  device.createTime = device.createTime ? moment(device.createTime).format("YYYY-MM-DD HH:mm") : "";

  // 处理过期时间
  if (device.expireTime) {
    device.expireTime = moment.unix(device.expireTime).format("YYYY-MM-DD HH:mm");
  } else if (device.activeTime) {
    device.expireTime = moment.unix(device.activeTime).add(1, "year").format("YYYY-MM-DD HH:mm");
  } else {
    device.expireTime = "";
  }

  device.activeTime = device.activeTime ? moment.unix(device.activeTime).format("YYYY-MM-DD HH:mm") : device.expireTime;

  commFunc.formatNullToString(device);

  // 处理设备缓存数据
  const deviceCache = deviceCacheMap.get(device.imei);
  let statusTime = 0;
  let statePeriod = 0;
  device.deviceState = device.onlineState;

  if (deviceCache) {
    device.deviceState = device.onlineState = deviceCache.onlineState || device.onlineState;
    device.lastLocationTime = deviceCache.locTime || device.lastLocationTime;
    statusTime = deviceCache.statusTime || 0;
    statePeriod = Number.parseInt(`${deviceCache.stopPeriod}` || "0", 10);

    // 设置其他缓存字段
    Object.assign(device, {
      speed: deviceCache.speed || "",
      electric: deviceCache.electricity || "",
      locationType: deviceCache.locType || 0,
      lastSignalTime: deviceCache.signalTime || "",
      direct: deviceCache.direct || 0,
      signals: deviceCache.signals || 0,
      outVoltage: deviceCache.outVoltage || "",
      lon: Number.parseFloat(deviceCache.lon || "0"),
      lat: Number.parseFloat(deviceCache.lat || "0")
    });

    if (deviceCache.hasOwnProperty("ACC")) {
      device.ACC = deviceCache.ACC;
    }

    // 处理状态信息
    for (let i = 1; i <= 12; i++) {
      device[`statusInfo_${i}`] = deviceCache[`statusInfo_${i}`] || "";
    }
  }

  // 优化在线状态处理
  const onlineStateStr = `${device.onlineState}`;
  if (onlineStateStr === nameKeyConfig.ONLINE_STATE.ONLINE) {
    device.lastOnlineTime = statusTime || "";
    if (deviceCache && deviceCache.stopFlag === "0") {
      statePeriod = device.lastOnlineTime ? constants.currentTimestamp - device.lastOnlineTime : 0;
    } else {
      device.deviceState = 10;
    }

    if (deviceCache && deviceCache.signalTime > 0 && deviceCache.signalTime < constants.overtimeThreshold) {
      device.onlineState = nameKeyConfig.ONLINE_STATE.OFFLINE;
      device.deviceState = nameKeyConfig.ONLINE_STATE.OFFLINE;
    }
  } else if (onlineStateStr === nameKeyConfig.ONLINE_STATE.OFFLINE) {
    device.lastOfflineTime = statusTime || "";
    statePeriod = device.lastOfflineTime ? constants.currentTimestamp - device.lastOfflineTime : 0;
  }

  if (device.deviceState === 2) {
    statePeriod += constants.deviceStateTimeOffset;
  }

  // 设置状态相关字段
  device.onlineStateName = keyValConfig.ONLINE_STATE[device.onlineState] || "";
  device.onlineStateIden = keyIdenConfig.ONLINE_STATE[device.onlineState] || "";
  device.shortStateName = `${commFunc.secondsToShortHumanize(statePeriod)}`;
  device.deviceStateName = `${commFunc.secondsToHumanize(statePeriod)}`;

  if (`${device.simState}` === nameKeyConfig.SIM_STATE.GQ) {
    device.deviceState = 100;
    device.shortStateName = "已到期";
  }

  device.locationTypeName = keyValConfig.LOCATION_TYPE[device.locationType] || "";
  device.lastLocationTime = device.lastLocationTime ? moment.unix(device.lastLocationTime).format("YYYY-MM-DD HH:mm") : "";
  device.lastOnlineTime = device.lastOnlineTime ? moment.unix(device.lastOnlineTime).format("YYYY-MM-DD HH:mm") : "";
  device.lastSignalTime = device.lastSignalTime ? moment.unix(device.lastSignalTime).format("YYYY-MM-DD HH:mm") : "";
  device.lastOfflineTime = device.lastOfflineTime ? moment.unix(device.lastOfflineTime).format("YYYY-MM-DD HH:mm") : "";

  return device;
}

module.exports = {
  getOrganDeviceListLogicOptimized,
  getCachedResult,
  cacheResult,
  getConfigDataCached,
  getBatchDeviceCache,
  processDeviceListOptimized,
  getDeviceStatsAsync
};
