const idgen = require("node-idgen");
const socketio = require("socket.io-client");

const logger = require("./logger");
const config = require("../../config/config_site");
const staticConfig = require("../../config/config_static");
const propertymapBusiness = require("../../business/propertymap_business");

exports.idGenerator = new idgen.IdGenerator();

const log = {};
// ...args: key, logType, module, logContent
["trace", "debug", "info", "warn", "error", "fatal"].forEach((method) => {
  log[method] = (...args) => {
    logger[method].apply(logger, ["%s#%s#%s#%s#%s", config.logSource, ...args]);
  };
});

exports.logger = log;

exports.redisClient = new (require("../../proxy/redis_client"))(
  staticConfig.redisConfig
);

exports.oldRedisClient = new (require("../../proxy/redis_client"))(
  staticConfig.oldRedisConfig
);

exports.getPropertymapKeyVal = (codes) =>
  propertymapBusiness.getPropertymapKeyValLogic(codes);
exports.getPropertymapNameKey = (codes) =>
  propertymapBusiness.getPropertymapNameKeyLogic(codes);
exports.getPropertymapKeyIden = (codes) =>
  propertymapBusiness.getPropertymapKeyIdenLogic(codes);

exports.apiTokenConfig = config.apiTokenConfig;

function sendWechatTemplate(sendTempFunc, sendOpenId, wechatTempData) {
  const message = `${sendTempFunc}#${sendOpenId}#${JSON.stringify(
    wechatTempData
  )}`;
  exports.redisClient.publish(config.redisChannel.template, message, () => {});
}

exports.sendWechatTemplate = sendWechatTemplate;

const socketClient = socketio.connect(staticConfig.socketConfig.wsHost);
exports.socketServer = new (require("../../proxy/socket_server"))(socketClient);
