const mysqlDB = require("../common/mysql_pool");

function getUserSql(queryParams) {
  const sql = `select
  uu.id user_id,uu.user_code,uu.user_name,uu.login_name,uu.realname,uu.sex,uu.role_ids,
  uu.email,uu.organ_id,uu.type,uu.last_login_time,uu.is_super,uu.status,uu.remark,uu.create_user,uu.create_time,
  uu.is_child_alarm,uu.is_alarm_phone,uu.is_alarm_sms,uu.is_alarm_wechat,uu.is_alarm_email,uu.is_alarm_official_account,
  uu.alarm_phone,uu.alarm_sms,uu.alarm_wechat_msg_id,uu.alarm_email,alarm_official_account_id,
  uu.alarm_phone_types,uu.alarm_sms_types,uu.alarm_email_types,uu.alarm_wechat_types,uu.alarm_official_accout_types
  from uc_user uu
  where ${queryParams.userId ? 'id=:user_id' : "1=1"} ${queryParams.organId ? 'and organ_id=:organ_id' : ""} ${queryParams.type ? 'and type=1' : ""}`;

  return mysqlDB.one(sql, queryParams);
}

function getUserSqlList(queryParams, currentUser) {
  const sql = `select uu.id user_id,uu.user_code,uu.user_name,uu.login_name,uu.realname,uu.sex,uu.role_ids,
  uu.email,uu.organ_id,uu.type,uo.organ_name,uo.level,
  uu.last_login_time,uu.is_super,uu.status,uu.remark,uu.create_user,uu.create_time,
  uu.is_child_alarm,uu.is_alarm_phone,uu.is_alarm_sms,uu.is_alarm_wechat,uu.is_alarm_email,uu.is_alarm_official_account,
  uu.alarm_phone,uu.alarm_sms,uu.alarm_wechat_msg_id,uu.alarm_email,alarm_official_account_id,
  uu.alarm_phone_types,uu.alarm_sms_types,uu.alarm_email_types,uu.alarm_wechat_types,uu.alarm_official_accout_types
  from uc_user uu
  join uc_organ uo on uu.organ_id=uo.id and (:current_organ_id=-1 or path like concat('%,',:current_organ_id,',%'))
  where (:status=-1 or uu.status=:status) and (:user_code='' or uu.user_code=:user_code)
  and (:roleId=-1 or uu.role_ids like concat('%,',:role_id,',%'))
  and (:search_organ_id=-1 or ifnull(uo.path, '') like concat('%',:search_organ_id,'%'))
  and (:search_key='' or concat(uu.user_name,uu.realname,uu.login_name) like concat('%',:search_key,'%'))
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `and uu.organ_id != ${currentUser.organId} or uu.id=${currentUser.userId}` : ""
  }
  order by uu.status desc,uo.path,uu.user_code desc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getUserDropDownSqlList(queryParams, queryRoles) {
  const sqlRoles = queryRoles.map(
    (m) => `uu.role_ids like concat('%,',${m},',%')`
  );
  const sql = `select uu.id user_id,uu.user_code,uu.user_name,uu.login_name,uu.realname,uu.role_ids
  from uc_user uu
  join uc_organ uo on uu.organ_id=uo.id and (:current_organ_id=-1 or path like concat('%,',:current_organ_id,',%'))
  where uu.status=1 and (${sqlRoles.length > 0 ? sqlRoles.join(" or ") : "1=1"})
  and (:search_key='' or concat(uu.user_code,uu.user_name,uu.realname,uu.login_name) like concat('%',:search_key,'%'))
  limit 0,15`;

  return mysqlDB.all(sql, queryParams);
}

function getUserRecordsSql(queryParams) {
  const sql = `select count(*) record,id user_id
  from uc_user
  where login_name=:login_name`;

  return mysqlDB.one(sql, queryParams);
}

function insertUserSql(insertData, operationModel) {
  const sqlTasks = [];

  const sql = `insert into uc_user
  (user_code,user_name,login_name,password,realname,sex,role_ids,email,organ_id,is_super,status,create_user,create_time,
  is_child_alarm,is_alarm_phone,is_alarm_sms,is_alarm_wechat,is_alarm_email,is_alarm_official_account,
  alarm_phone,alarm_sms,alarm_wechat_msg_id,alarm_email,alarm_official_account_id,
  alarm_phone_types,alarm_sms_types,alarm_email_types,alarm_wechat_types,alarm_official_accout_types)
  select ifnull(max(user_code),1000)+1,:user_name,:login_name,:password,:realname,:sex,:role_ids,
  :email,:organ_id,:is_super,:status,:create_user,now(),
  :is_child_alarm,:is_alarm_phone,:is_alarm_sms,:is_alarm_wechat,:is_alarm_email,:is_alarm_official_account,
  :alarm_phone,:alarm_sms,:alarm_wechat_msg_id,:alarm_email,:alarm_official_account_id,
  :alarm_phone_types,:alarm_sms_types,:alarm_email_types,:alarm_wechat_types,:alarm_official_accout_types
  from uc_user`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ field: "user", sql: sql, params: insertData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

function updateUserSql(updateData, operationModel) {
  const sqlTasks = [];

  let superSql = updateData.isSuper !== -1 ? "is_super=:is_super," : "";

  const sql = `update uc_user
  set user_name=:user_name,login_name=:login_name,realname=:realname,sex=:sex,role_ids=:role_ids,email=:email,
  organ_id=:organ_id,${superSql}status=:status,update_user=:update_user,update_time=now(),
  is_child_alarm=:is_child_alarm,is_alarm_phone=:is_alarm_phone,is_alarm_sms=:is_alarm_sms,is_alarm_wechat=:is_alarm_wechat,
  is_alarm_email=:is_alarm_email,is_alarm_official_account=:is_alarm_official_account,
  alarm_phone=:alarm_phone,alarm_sms=:alarm_sms,alarm_wechat_msg_id=:alarm_wechat_msg_id,
  alarm_email=:alarm_email,alarm_official_account_id=:alarm_official_account_id,
  alarm_phone_types=:alarm_phone_types,alarm_sms_types=:alarm_sms_types,alarm_email_types=:alarm_email_types,
  alarm_wechat_types=:alarm_wechat_types,alarm_official_accout_types=:alarm_official_accout_types
  where id=:user_id`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: sql, params: updateData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

function getUserByIdSqlData(queryParams) {
  const sql = `select id user_id,user_code,user_name,password,realname,login_name
  from uc_user
  where (id=:search_key or login_name=:search_key)`;

  return mysqlDB.one(sql, queryParams);
}

function updateUserPasswordSql(updateData) {
  const sql = "update uc_user set password=:password where id = :user_id";

  return mysqlDB.update(sql, updateData);
}

function batchInsertUserSql(insertList) {
  const sqlTasks = [];
  const sql = `insert into uc_user
  (user_code,user_name,login_name,password,realname,role_ids,organ_id,email,status,create_user,create_time)
  values
  (:user_code,:user_name,:login_name,:password,:realname,:role_ids,:organ_id,:email,1,:create_user,now())`;

  insertList.forEach((e) => {
    sqlTasks.push({ sql: sql, params: e });
  });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getUserSql,
  getUserSqlList,
  getUserDropDownSqlList,
  getUserRecordsSql,
  insertUserSql,
  updateUserSql,
  getUserByIdSqlData,
  updateUserPasswordSql,
  batchInsertUserSql,
};
