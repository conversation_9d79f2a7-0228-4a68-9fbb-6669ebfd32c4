const qiniu = require("qiniu");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const staticConfig = require("../config/config_static");
const basicBusiness = require("../business/basic_business");

/**
 * @api {GET} /basic/getwebsiteinfo 获取网站基本信息
 * @apiName getwebsiteinfo
 * @apiGroup Basic
 * @apiVersion  1.0.0
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
     errcode: 0,
     errmsg: '操作成功',
     retobj: {
      "menu":[{"id":11,"name":"系统设置"}],
      "button":["user"],
      "siteConfig":{"siteTitle":"用户中心"},
      "qiniu":{"qiniuDomain":"七牛域"}}
   }
 */
exports.getWebsiteInfo = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "基础信息-获取网站信息",
  handler: async (ctx) => {
    try {
      const currentUser = ctx.state.user;
      const permission = await basicBusiness.getCurrentUserPermissionDetail(
        currentUser,
        ["MENU", "BUTTON"]
      );
      const websiteData = {
        menu: permission.menu,
        button: permission.button,
        siteConfig: ctx.config || {},
        currentUser: {
          loginName: currentUser.loginName,
          userName: currentUser.userName,
          realname: currentUser.realname,
          isSuper: currentUser.isSuper,
        },
        qiniu: { qiniuDomain: staticConfig.qiniu.qiniuDomain },
      };

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        websiteData
      );
    } catch (err) {
      global.context.logger.error(
        "SYSTEM",
        "SYS",
        "getWebsiteInfo",
        "用户获取网站信息出错：",
        err
      );
      const websiteData = {
        menu: [],
        button: [],
        siteConfig: {},
      };
      ctx.body = new RetJson(
        i18n.SYS_ERROR_CODE,
        i18n.SYS_ERROR_MESSAGE,
        websiteData
      );
    }
  },
};

/**
 * @api {GET} /basic/qiniuuploadtoken 获取七牛上传token
 * @apiName qiniuuploadtoken
 * @apiGroup Basic
 * @apiVersion  1.0.0
 */
exports.qiniuUploadToken = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "基础信息-获取七牛上传token",
  handler: async (ctx) => {
    try {
      const mac = new qiniu.auth.digest.Mac(
        staticConfig.qiniu.accessKey,
        staticConfig.qiniu.secretKey
      );
      const options = { scope: staticConfig.qiniu.bucketName };
      const putPolicy = new qiniu.rs.PutPolicy(options);
      const uploadToken = putPolicy.uploadToken(mac);
      ctx.header("Cache-Control", "max-age=0, private, must-revalidate");
      ctx.header("Pragma", "no-cache");
      ctx.body = { uptoken: uploadToken };
    } catch (err) {
      global.context.logger.error(
        "SYSTEM",
        "SYS",
        "qiniuUploadToken",
        "获取七牛上传token出错：",
        err
      );
      ctx.body = { uptoken: "" };
    }
  },
};

/**
 * @api {POST} /basic/uploadsinglefile 基础信息-上传单个文件
 * @apiName uploadsinglefile
 * @apiGroup Basic
 * @apiVersion  1.0.0
 */
exports.uploadSingleFile = {
  method: "POST",
  middlewares: ["loginRequired"],
  routeDesc: "基础信息-上传单个文件",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const filename = await basicBusiness.uploadSingleFileLogic(ctx);
      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "uploadSingleFile",
        "上传单个文件成功，",
        filename
      );
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        filename
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "uploadSingleFile",
          "上传单个文件失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "uploadSingleFile",
        "上传单个文件出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /basic/getnationregionlist 基础信息-获取区域信息
 * @apiName getnationregionlist
 * @apiGroup Basic
 * @apiVersion  1.0.0
 *
 * @apiParam  {Number} parentId 区域父Id，一级区域父Id为0 必须值
 * @apiParam  {Number} status 状态，0禁用，1启用 不传默认取全国省份，传1暂时取广东省份
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: []
  }
 */
exports.getNationRegionList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "基础信息-获取区域信息",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;

      if (!validator.isIntFormat(reqQueryParams.parentId, { min: 0 })) {
        ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
        return;
      }

      const queryParams = {
        parentId: reqQueryParams.parentId,
        status: validator.isInt01(reqQueryParams.status)
          ? reqQueryParams.status
          : -1,
      };

      const regionList = await basicBusiness.getNationRegionListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        regionList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getNationRegionList",
        "获取区域信息出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
