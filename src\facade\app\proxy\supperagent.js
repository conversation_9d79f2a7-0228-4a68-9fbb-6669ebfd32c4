const request = require("superagent");

class HttpSuperagent {
  constructor() {
    this.name = "";
  }

  static baseHttpPostCall(postUrl, postData, timeout) {
    timeout = timeout || 3000;

    return request
      .post(postUrl)
      .send(postData)
      .type("json")
      .timeout(timeout)
      .then((res) => {
        if (res.error || res.status !== 200) {
          throw res ? res.error : "";
        }
        return res.text;
      });
  }

  static baseHttpGetCall(getUrl, timeout) {
    timeout = timeout || 3000;

    return request
      .get(getUrl)
      .timeout(timeout)
      .then((res) => {
        if (res.error || res.status !== 200) {
          throw res ? res.error : "";
        }
        return res.text;
      });
  }
}

module.exports = HttpSuperagent;
