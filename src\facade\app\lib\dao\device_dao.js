const mysqlDB = require("../common/mysql_pool");
const { getDaoShard } = require("../utils/sharding");

function getDeviceSqlAllList(queryParams) {
  if (!queryParams.addImeis.length) {
    return [];
  }
  const sql = `select gd.organ_id,gd.imei
  from gps_device gd
  where imei in (${queryParams.addImeis})`;

  return mysqlDB.all(sql);
}

function getDeviceSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  const sql = `select gd.organ_id,uo.organ_name,gd.plate_no,gd.attention,gc.vin_no,gc.engine_no,gc.car_owner,
  gc.car_brand,gc.car_model,gc.owner_phone,gc.loan_amount,gc.policy_no,gc.policy_exp_date,gc.remark car_remark,
  gd.id device_id,gd.imei,gd.device_name,gd.device_type,gd.device_model,gd.install_location,gd.installer,gd.install_date,
  gd.sim_no,gd.iccid,gd.service_pswd,gd.sim_expire_time,gd.sim_state,gd.province,ifnull(bnr.name,'') province_name,
  gd.online_state,gd.last_online_time,gd.expire_time,gd.active_time,gd.remark device_remark,gd.create_time,gd.patform_expiration_time,
  gd.is_alarm_phone,gd.is_alarm_sms,gd.is_alarm_wechat,gd.alarm_phone,alarm_sms,gd.alarm_wechat_name,gd.alarm_wechat_msg_id,
  gd.alarm_phone_types,gd.alarm_sms_types,gd.alarm_wechat_types
  from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id ${queryParams.hasSubOrgan === 1
      ? " and uo.path like concat('%,',:organ_id,',%')"
      : ""
    }
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
    }
  left join bc_nation_region bnr on bnr.level=1 and bnr.id=concat(gd.province,'0000')
  where (:search_key='' or concat(uo.organ_name,gd.imei,gd.device_name,gd.plate_no,gd.sim_no,gc.car_owner) like concat('%',:search_key,'%')) and gd.is_delete=0
  ${queryParams.onlineState === -1 ? "" : " and gd.online_state=:online_state"}
  ${queryParams.deviceType === -1 ? "" : " and gd.device_type=:device_type"}
  ${queryParams.deviceModel === -1 ? "" : " and gd.device_model=:device_model"}
  ${queryParams.hasSubOrgan === 0 ? " and gd.organ_id=:organ_id" : ""}
  ${queryParams.isSettle === -1 ? "" : " and gd.is_settle=:is_settle"}
  ${queryParams.imeis ? " and gd.imei in (" + queryParams.imeis + ")" : ""}
  order by gd.plate_no,gd.imei`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getBigCarDeviceSqlList(queryParams) {
  const sql = `select gd.organ_id,uo.organ_name,gd.plate_no,gd.id as device_id,gd.imei,gd.device_name,
  gd.device_type,gd.device_model,gd.iccid,gd.online_state,gd.last_online_time
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id ${queryParams.hasSubOrgan === 1
      ? " and uo.path like concat('%,',:organ_id,',%')"
      : ""
    }
  where gd.device_model in(90,91,92,93,94,95,96,97,98,99) and
  (:search_key='' or concat(gd.imei,gd.device_name,gd.plate_no) like concat('%',:search_key,'%')) and gd.is_delete=0
  ${queryParams.hasSubOrgan === 0 ? " and gd.organ_id=:organ_id" : ""}
  order by gd.plate_no,gd.imei`;
  return mysqlDB.queryForPagination(sql, queryParams);
}

function getDeviceDropdownSqlList(queryParams) {
  const sql = `select gd.plate_no,gc.car_owner,gd.imei,gd.device_name
  from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id ${queryParams.hasSubOrgan === 1
      ? " and uo.path like concat('%,',:organ_id,',%')"
      : ""
    }
  where (:search_key='' or concat(gd.device_name,gd.imei,gd.plate_no,gc.car_owner) like concat('%',:search_key,'%'))
  and gd.is_settle=0 and gd.is_delete=0
  ${queryParams.hasSubOrgan === 0 ? " and gd.organ_id=:organ_id" : ""}
  order by gd.plate_no,gd.imei
  limit 50`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceRecordsSql(queryParams) {
  const sql = `select count(*) record,id device_id,device_name,imei
  from gps_device
  where imei=:imei`;

  return mysqlDB.one(sql, queryParams);
}

function getDeviceSqlData(queryParams) {
  const sql = `select gd.organ_id,uo.organ_name,gd.plate_no,gc.car_owner,gc.car_brand,gc.car_model,gc.owner_phone,
  gd.id device_id,gd.device_name,gd.imei,gd.device_type,gd.device_model,gd.attention
  from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.imei=:imei and gd.is_delete=0`;

  return mysqlDB.one(sql, queryParams);
}

function getDeviceAllSqlData(queryParams) {
  if (!queryParams.imeis) {
    return [];
  }
  const sql = `select uo.organ_name,gd.device_name,gd.imei,gd.device_type,gd.device_model,gd.attention,gd.attention_remark,
  gd.plate_no,gc.vin_no,gc.car_owner,gc.remark car_remark
  from gps_car gc
  join gps_device gd on gd.plate_no=gc.plate_no and gd.imei in (${queryParams.imeis})
  join uc_organ uo on gc.organ_id=uo.id`;
  return mysqlDB.all(sql, queryParams);
}


function insertDeviceSql(insertData, operationModel) {
  const sqlTasks = [];
  console.log(insertData);
  const deviceSql = `insert into gps_device(organ_id,plate_no,imei,device_name,device_type,device_model,install_location,installer,install_date,
  encryted_imei,sim_no,iccid,service_pswd,remark,create_user,create_time,patform_expiration_time,expire_time,
  is_alarm_phone,is_alarm_sms,is_alarm_wechat,alarm_phone,alarm_sms,alarm_wechat_name,alarm_wechat_msg_id,
  alarm_phone_types,alarm_sms_types,alarm_wechat_types)
  values(:organ_id,:plate_no,:imei,:device_name,:device_type,:device_model,:install_location,:installer,:install_date,
  :encryted_imei,:sim_no,:iccid,:service_pswd,:device_remark,:user_name,now(),:patform_expiration_time,:expire_time,
  :is_alarm_phone,:is_alarm_sms,:is_alarm_wechat,:alarm_phone,:alarm_sms,
  :alarm_wechat_name,:alarm_wechat_msg_id,:alarm_phone_types,:alarm_sms_types,:alarm_wechat_types
  )`;

  const insertCarSql = `insert into gps_car(organ_id,plate_no,vin_no,engine_no,policy_no,policy_exp_date,car_brand,car_model,
  car_owner,owner_phone,loan_amount,remark,create_user,create_time)
  values(:current_organ_id,:plate_no,:vin_no,:engine_no,:policy_no,:policy_exp_date,:car_brand,:car_model,
  :car_owner,:owner_phone,:loan_amount,:car_remark,:user_name,now())`;

  const updateCarSql = `update gps_car
  set vin_no=:vin_no,engine_no=:engine_no,policy_no=:policy_no,policy_exp_date=:policy_exp_date,
  car_brand=:car_brand,car_model=:car_model,car_owner=:car_owner,owner_phone=:owner_phone,loan_amount=:loan_amount,
  remark=:car_remark,update_user=:user_name,update_time=now()
  where id=:car_id`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,:plate_no,:imei,:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: optSql, params: operationModel });
  sqlTasks.push({ sql: deviceSql, params: insertData });
  if (insertData.carId && insertData.isUpdateCar) {
    sqlTasks.push({ sql: updateCarSql, params: insertData });
  }
  if (!insertData.carId) {
    sqlTasks.push({ field: "insert", sql: insertCarSql, params: insertData });
  }

  return mysqlDB.executeTransaction(sqlTasks);
}

function updateDeviceSql(updateData, operationModel) {
  const sqlTasks = [];

  const deviceSql = `update gps_device
  set plate_no=:plate_no,device_type=:device_type,device_model=:device_model,device_name=:device_name,
  install_location=:install_location,installer=:installer,
  install_date=:install_date,sim_no=:sim_no,service_pswd=:service_pswd,
  remark=:device_remark,update_user=:user_name,update_time=now(),
  expire_time=:expire_time,
  patform_expiration_time=:patform_expiration_time,
  is_alarm_phone=:is_alarm_phone,is_alarm_sms=:is_alarm_sms,is_alarm_wechat=:is_alarm_wechat,
  alarm_phone=:alarm_phone,alarm_sms=:alarm_sms,alarm_wechat_name=:alarm_wechat_name,
  alarm_wechat_msg_id=:alarm_wechat_msg_id,
  alarm_phone_types=:alarm_phone_types,alarm_sms_types=:alarm_sms_types,alarm_wechat_types=:alarm_wechat_types
  where id=:device_id`;

  const insertCarSql = `insert into gps_car(organ_id,plate_no,vin_no,engine_no,policy_no,policy_exp_date,car_brand,car_model,
  car_owner,owner_phone,loan_amount,remark,create_user,create_time)
  values(:organ_id,:plate_no,:vin_no,:engine_no,:policy_no,:policy_exp_date,:car_brand,:car_model,
  :car_owner,:owner_phone,:loan_amount,:car_remark,:user_name,now())`;

  const updateCarSql = `update gps_car
  set vin_no=:vin_no,engine_no=:engine_no,policy_no=:policy_no,policy_exp_date=:policy_exp_date,
  car_brand=:car_brand,car_model=:car_model,car_owner=:car_owner,owner_phone=:owner_phone,loan_amount=:loan_amount,
  remark=:car_remark,update_user=:user_name,update_time=now()
  where id=:car_id`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,:plate_no,:imei,:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: optSql, params: operationModel });
  sqlTasks.push({ sql: deviceSql, params: updateData });
  if (updateData.carId && updateData.isUpdateCar) {
    sqlTasks.push({ sql: updateCarSql, params: updateData });
  }
  if (!updateData.carId) {
    sqlTasks.push({ sql: insertCarSql, params: updateData });
  }

  return mysqlDB.executeTransaction(sqlTasks);
}

function deleteDeviceSql(deleteData, operationModel) {
  const sqlTasks = [];

  const sql = `delete from gps_device where id in(${deleteData.deviceIds.join(
    ","
  )})`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,(select organ_name from uc_organ where id=:organ_id),
  plate_no,imei,:operation_type,
  concat('删除设备名: ',device_name,' 设备Id: ',id,' imei: ',imei,' 设备型号: ', device_model),:realname,now()
  from gps_device
  where id in(${deleteData.deviceIds.join(",")})`;

  sqlTasks.push({ sql: optSql, params: operationModel });
  sqlTasks.push({ sql: sql, params: deleteData });

  return mysqlDB.executeTransaction(sqlTasks);
}

function updateDeviceOrganSql(updateData, operationModel) {
  const sql = `update gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:current_organ_id,',%')
  set gd.organ_id=:organ_id
  where gd.imei in(${updateData.imeis.join(",")})`;

  const content = `concat('设备改组 设备名: ', gd.device_name,' 设备号 ', gd.imei, ' 从 [',gd.organ_id,'] ', uo.organ_name,',  更改的组织 [${operationModel.organId}] ${operationModel.organName}')`;
  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,device_name,
  operation_type,content,create_user,create_time)
  select :user_id,gd.organ_id,uo.organ_name,gd.plate_no,gd.imei,gd.device_name,:operation_type,${content},:realname,now()
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id
  where gd.imei in(${updateData.imeis.join(",")})`;

  const sqlTasks = [];
  sqlTasks.push({ sql: optSql, params: operationModel });
  sqlTasks.push({ field: "update", sql: sql, params: updateData });

  return mysqlDB.executeTransaction(sqlTasks);
}

function getOfflineDeviceSqlList(queryParams) {
  const sql = `select uo.organ_name,gd.plate_no,gd.imei,gd.device_name,gd.sim_no,
  gd.device_type,gd.device_model,gd.last_online_time,gd.last_offline_time,gd.attention,gd.attention_remark
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.online_state=:online_state and gd.is_settle=0 and gd.is_delete=0
  ${queryParams.startTime === 0
      ? " and gd.last_offline_time<:end_time"
      : " and gd.last_offline_time between :start_time and :end_time"
    }
  ${queryParams.deviceType === -1 ? "" : " and gd.device_type=:device_type"}
  order by gd.attention desc,gd.last_online_time desc,gd.imei`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getSameCarDeviceSqlList(queryParams) {
  const sql = `select uo.organ_name,gd.plate_no,gc.car_owner,gc.car_brand,gc.car_model,
  gd.imei,gd.device_name,gd.device_type,gd.device_model
  from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gc.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where (:plate_no='' or gd.plate_no=:plate_no) and (:imei='' or gd.imei=:imei) and gd.is_delete=0`;

  return mysqlDB.all(sql, queryParams);
}

function updateDeviceAttentionSql(updateData) {
  const sql = `update gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  set gd.attention=:attention,gd.attention_remark=:attention_remark
  where ${updateData.plateNo === "" ? "gd.imei=:imei" : "gd.plate_no=:plate_no"
    }`;

  return mysqlDB.update(sql, updateData);
}

function getCarSettleSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  const sql = `select uo.organ_name,gd.plate_no,gd.imei,gd.sim_no,gd.device_name,
  gd.device_type,gd.device_model,gc.vin_no,gc.car_owner,gc.car_brand,gc.car_model,
  gd.is_settle,gd.settle_remark,gd.settle_cancel_remark,gd.settle_time
  from gps_device gd
  join gps_car gc on gc.plate_no=gd.plate_no
  join uc_organ uo on gd.organ_id=uo.id ${queryParams.hasSubOrgan === 1 && queryParams.organId > -1
      ? " and uo.path like concat('%,',:organ_id,',%')"
      : ""
    }
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
    }
  where ${queryParams.imei === "" ? "" : " gd.imei=:imei and "
    } gd.is_settle=:is_settle and gd.is_delete=0
  ${queryParams.hasSubOrgan === 0
      ? " and (:organ_id=-1 or gd.organ_id=:organ_id)"
      : ""
    }`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function updateCarSettleSql(updateData, operationModel) {
  const sqlTasks = [];

  const deviceSql = `update gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  set gd.is_settle=:is_settle${updateData.isSettle === 1
      ? ",gd.settle_remark=:settle_remark,gd.settle_time=now()"
      : ",gd.settle_cancel_remark=:settle_remark,gd.settle_time=now()"
    }
  where gd.imei=:imei`;

  const carSql = `update gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  set gd.is_settle=:is_settle${updateData.isSettle === 1
      ? ",gd.settle_remark=:settle_remark,gd.settle_time=now()"
      : ",gd.settle_cancel_remark=:settle_remark,gd.settle_time=now()"
    }
  where gd.plate_no=:plate_no`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,:plate_no,:imei,:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: optSql, params: operationModel });
  if (updateData.imei) {
    sqlTasks.push({ sql: deviceSql, params: updateData });
  }
  if (!updateData.imei) {
    sqlTasks.push({ sql: carSql, params: updateData });
  }

  return mysqlDB.executeTransaction(sqlTasks);
}

function getAttentionDeviceSqlList(queryParams) {
  const sql = `select gd.organ_id,uo.organ_name,gd.plate_no,gc.vin_no,gc.car_owner,gc.remark car_remark,
  gd.id device_id,gd.imei,gd.device_type,gd.device_model,gd.attention,gd.attention_remark,gd.alarm_type,gd.last_signal_time,
  gd.last_location_time,gd.online_state,gd.last_online_time,gd.last_offline_time
  from gps_device gd
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.is_settle=0 and gd.is_delete=0 and gd.attention>0
  order by gd.online_state desc,gd.plate_no,gd.imei`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getOrganDeviceSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  const ucUserDeviceWhiteTable = getDaoShard('uc_user_device', queryParams.whiteUserId);
  let organSql = "join uc_organ uo1 on gd.organ_id=uo1.id ";
  organSql =
    queryParams.hasSubOrgan === 1
      ? `${organSql} and uo1.path like concat('%,',:organ_id,',%')`
      : `${organSql} and gd.organ_id=:organ_id`;

  // 优化 SQL 查询 - 只查询必要字段，使用更高效的 JOIN
  const sql = `select
    gd.organ_id, uo.organ_name, gd.plate_no, gd.is_speed, gd.is_alarm,
    gc.vin_no, gc.engine_no, gc.car_owner, gc.car_brand, gc.car_model,
    gc.owner_phone, gc.loan_amount, gc.policy_no, gc.policy_exp_date, gc.remark as car_remark,
    gd.id as device_id, gd.imei, gd.device_name, gd.device_type, gd.device_model,
    gd.install_location, gd.installer, gd.install_date, gd.sim_no, gd.iccid,
    gd.service_pswd, gd.sim_expire_time, gd.sim_state, gd.attention, gd.alarm_type,
    gd.last_signal_time, gd.last_location_time, gd.online_state, gd.last_online_time,
    gd.last_offline_time, gd.expire_time, gd.active_time, gd.remark as device_remark,
    gd.create_time, gd.is_alarm_phone, gd.is_alarm_sms, gd.is_alarm_wechat,
    gd.alarm_phone, gd.alarm_sms, gd.alarm_wechat_name, gd.alarm_wechat_msg_id,
    gd.alarm_phone_types, gd.alarm_sms_types, gd.alarm_wechat_types
    ${queryParams.whiteUserId ? `, !ISNULL(uudw.imei) as is_white` : ", 0 as is_white"}
    ${queryParams.noticeUserId && queryParams.noticeType ? `, !ISNULL(uund.imei) as is_notice_imei` : ", 0 as is_notice_imei"}
  from gps_device gd
  inner join uc_organ uo on gd.organ_id = uo.id
    and uo.path like concat('%,',:current_organ_id,',%')
  inner join gps_car gc on gd.plate_no = gc.plate_no
  ${queryParams.isAttention === 0 ? organSql : ""}
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `inner join ${ucUserDeviceTable} uud on gd.imei = uud.imei and uud.user_id = ${currentUser.userId}` : ""
    }
  ${queryParams.whiteUserId ? `left join ${ucUserDeviceWhiteTable} uudw on gd.imei = uudw.imei and uudw.user_id = ${queryParams.whiteUserId}` : ""
    }
  ${queryParams.noticeUserId && queryParams.noticeType ? `left join uc_user_notice_device uund on gd.imei = uund.imei and uund.user_id = ${queryParams.noticeUserId} and uund.type = '${queryParams.noticeType}'` : ""
    }
  where gd.is_settle = 0 and gd.is_delete = 0
    ${queryParams.isAttention === 1 ? " and gd.attention > 0" : ""}
    and (:online_state = -1 or gd.online_state = :online_state)
    ${queryParams.searchKey ? " and (uo.organ_name like concat('%',:search_key,'%') or gd.imei like concat('%',:search_key,'%') or gd.device_name like concat('%',:search_key,'%') or gd.plate_no like concat('%',:search_key,'%') or gd.sim_no like concat('%',:search_key,'%'))" : ""}
    ${queryParams.imeis ? " and gd.imei in (" + queryParams.imeis + ")" : ""}
  order by gd.is_alarm desc, gd.is_speed desc, FIELD(gd.online_state,'1','2','0') asc`;
  // order by gd.online_state desc,gd.plate_no,gd.imei`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getOrganDeviceStaSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  const ucUserDeviceWhiteTable = getDaoShard('uc_user_device', queryParams.whiteUserId);
  let organSql = "join uc_organ uo1 on gd.organ_id=uo1.id ";
  organSql =
    queryParams.hasSubOrgan === 1
      ? `${organSql} and uo1.path like concat('%,',:organ_id,',%')`
      : `${organSql} and gd.organ_id=:organ_id`;

  const sql = `select count(gd.id) total,sum(if(gd.online_state=:online,1,0)) online,
  sum(if(gd.online_state=:offline,1,0)) offline,sum(if(gd.online_state=:notuse,1,0)) notuse,
  sum(if(gd.attention>0,1,0)) attention_count
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:current_organ_id,',%')
  ${organSql}
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
    }
  ${queryParams.whiteUserId ? `join ${ucUserDeviceWhiteTable} uudw on gd.imei=uudw.imei and uudw.user_id=${queryParams.whiteUserId}` : ""
    }
  where gd.is_settle=0 and gd.is_delete=0`;

  return mysqlDB.one(sql, queryParams);
}

function getOfflineCount(queryParams) {
  const sql = `SELECT
	(
		CASE
		WHEN(
			(
				unix_timestamp(now()) - gd.last_offline_time
			) >= 43200
			AND gd.device_type = 1
		)
		OR(
			(
				unix_timestamp(now()) - gd.last_offline_time
			) >= 259200
			AND gd.device_type = 2
		) THEN
			'超长离线'
		ELSE
			NULL
		END
	) offline_interval ,
	count(gd.id) as super_stop_num
FROM
	gps_device gd
	join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
`;

  return mysqlDB.one(sql, queryParams);
}

function getDeviceInfoSqlList(queryParams) {
  const sql = `
  select concat('[ 车 组 ] ',organ_name) name,'DATA_CLASS.CO' data_class_iden,id organ_id,organ_name,0 device_id,'' imei,'' device_name,'' plate_no
  from uc_organ
  where organ_name like concat('%',:search_key,'%') and path like concat('%,',:organ_id,',%')
  union all
  select concat('[ 车牌号 ] ',gd.plate_no) name,'DATA_CLASS.SB' data_class_iden,gd.organ_id,uo.organ_name,gd.id device_id,gd.imei,gd.device_name,gd.plate_no
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id  and uo.path like concat('%,',:organ_id,',%')
  where gd.plate_no like concat('%',:search_key,'%') and gd.is_settle=0 and gd.is_delete=0
  union all
  select concat('[ 设备名 ] ',gd.device_name) name,'DATA_CLASS.SB' data_class_iden,gd.organ_id,uo.organ_name,gd.id device_id,gd.imei,gd.device_name,gd.plate_no
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id  and uo.path like concat('%,',:organ_id,',%')
  where gd.device_name like concat('%',:search_key,'%') and gd.is_settle=0 and gd.is_delete=0
  union all
  select concat('[ 设备号 ] ',gd.imei) name,'DATA_CLASS.SB' data_class_iden,gd.organ_id,uo.organ_name,gd.id device_id,gd.imei,gd.device_name,gd.plate_no
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id  and uo.path like concat('%,',:organ_id,',%')
  where gd.imei like concat('%',:search_key,'%') and gd.is_settle=0 and gd.is_delete=0
  union all
  select concat('[ SIM号 ] ',gd.sim_no) name,'DATA_CLASS.SB' data_class_iden,gd.organ_id,uo.organ_name,gd.id device_id,gd.imei,gd.device_name,gd.plate_no
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id  and uo.path like concat('%,',:organ_id,',%')
  where gd.sim_no like concat('%',:search_key,'%') and gd.is_settle=0 and gd.is_delete=0
  union all
  select concat('[ 车主名 ] ',gc.car_owner) name,'DATA_CLASS.SB' data_class_iden,gd.organ_id,uo.organ_name,gd.id device_id,gd.imei,gd.device_name,gd.plate_no
  from gps_device gd
  join gps_car gc on gc.car_owner like concat('%',:search_key,'%')
  join uc_organ uo on gc.organ_id=uo.id  and uo.path like concat('%,',:organ_id,',%')
  where gd.is_settle=0 and gd.is_delete=0
  limit 40`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceStateCountSql(queryParams) {
  const sql = `select count(gd.id) total,sum(if(gd.online_state=:online,1,0)) online,
  sum(if(gd.online_state=:offline,1,0)) offline,sum(if(gd.online_state=:notuse,1,0)) notuse,
  sum(if(gd.attention>0,1,0)) attention_count
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.is_settle=0 and gd.is_delete=0`;

  return mysqlDB.one(sql, queryParams);
}

function getDeviceImportSqlList(queryParams) {
  const sql = `select id device_import_id,imei,device_name,device_model,install_location,installer,install_date,
  sim_no,iccid,service_pswd,device_remark,is_update_car,plate_no,vin_no,engine_no,policy_no,policy_exp_date,car_brand,car_model,
  car_owner,owner_phone,loan_amount,car_remark,imei_validate,plate_no_validate,sim_no_validate,device_model_validate
  from gps_device_import
  where create_user=:user_name and is_validate=:is_validate`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertDeviceImportSql(insertList, userName) {
  const sqlTasks = [];

  const deleteSql = `delete from gps_device_import
  where create_user=:user_name`;

  const insertSql = `insert into gps_device_import(imei,device_name,device_model,install_location,installer,install_date,
  sim_no,iccid,service_pswd,device_remark,is_update_car,plate_no,vin_no,engine_no,policy_no,policy_exp_date,
  car_brand,car_model,car_owner,owner_phone,loan_amount,car_remark,
  imei_validate,plate_no_validate,sim_no_validate,device_model_validate,is_validate,create_user,create_time)
  values(:imei,:device_name,:device_model,:install_location,:installer,:install_date,
  :sim_no,:iccid,:service_pswd,:device_remark,:is_update_car,:plate_no,vin_no,:engine_no,:policy_no,:policy_exp_date,
  :car_brand,:car_model,:car_owner,:owner_phone,:loan_amount,:car_remark,
  :imei_validate,:plate_no_validate,:sim_no_validate,:device_model_validate,:is_validate,:user_name,now())`;

  const updateSql = `update gps_device_import gdi
  join gps_device gd on gdi.imei=gd.imei
  set gdi.imei_validate='imei已经存在',gdi.is_validate=1
  where gdi.is_validate=0 and gdi.create_user=:user_name`;

  sqlTasks.push({ sql: deleteSql, params: { userName } });

  for (const insertData of insertList) {
    sqlTasks.push({ sql: insertSql, params: insertData });
  }

  sqlTasks.push({ sql: updateSql, params: { userName } });

  return mysqlDB.executeTransaction(sqlTasks);
}

function updateDeviceImportSql(updateData) {
  const sqlTasks = [];

  const updateSql = `update gps_device_import
  set imei=:imei,device_name=:device_name,device_model=:device_model,plate_no=:plate_no,sim_no=:sim_no,
  imei_validate=:imei_validate,plate_no_validate=:plate_no_validate,
  sim_no_validate=:sim_no_validate,device_model_validate=:device_model_validate,is_validate=:is_validate
  where create_user=:user_name and id=:device_import_id`;

  const updateAllSql = `update gps_device_import gdi
  join gps_device gd on gdi.imei=gd.imei
  set gdi.imei_validate='imei已经存在',is_validate=1
  where gdi.is_validate=0 and gdi.create_user=:user_name`;

  sqlTasks.push({ sql: updateSql, params: updateData });
  sqlTasks.push({ sql: updateAllSql, params: updateData });

  return mysqlDB.executeTransaction(sqlTasks);
}


function updateDeviceIsAlarmSql(updateData) {
  const sql = `update gps_device gd
  set gd.is_alarm=:is_alarm
  where imei=:imei`

  return mysqlDB.update(sql, updateData);
}

module.exports = {
  getDeviceAllSqlData,
  getDeviceSqlAllList,
  getDeviceSqlList,
  getDeviceSqlData,
  getDeviceRecordsSql,
  insertDeviceSql,
  updateDeviceSql,
  deleteDeviceSql,
  updateDeviceOrganSql,
  getDeviceDropdownSqlList,
  getOfflineDeviceSqlList,
  getSameCarDeviceSqlList,
  updateDeviceAttentionSql,
  getCarSettleSqlList,
  updateCarSettleSql,
  getAttentionDeviceSqlList,
  getOrganDeviceSqlList,
  getDeviceInfoSqlList,
  getDeviceStateCountSql,
  getOrganDeviceStaSqlList,
  getOfflineCount,
  getDeviceImportSqlList,
  insertDeviceImportSql,
  updateDeviceImportSql,
  getBigCarDeviceSqlList,
  updateDeviceIsAlarmSql,
};
