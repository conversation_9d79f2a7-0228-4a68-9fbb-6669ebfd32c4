const _ = require("lodash");
const fs = require("fs");
const moment = require("moment");
const xlsx = require("node-xlsx");

const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");
const httpHandler = new (require("../lib/common/http_handler"))();
const userPermission = require("../lib/cache/user_permission");

const basicDao = require("../lib/dao/basic_dao");

// 获取当前用户权限信息
// needPerTypes，所需权限类型数组，BUTTON或MENU
async function getCurrentUserPermission(currentUser, needPerTypes) {
  let permissionCache = await userPermission.getCacheUserPermission();
  permissionCache = permissionCache || [];

  const organRoles = (currentUser.organRoleList || "")
    .split(",")
    .filter((f) => !!f);
  const currentUserRole = (currentUser.userRoleList || "")
    .split(",")
    .filter((f) => organRoles.includes(f));
  permissionCache = permissionCache.filter((p) => {
    const cacheRole = (p.roleList || "").split(",");

    return (
      (_.intersection(_.compact(currentUserRole), _.compact(cacheRole)).length >
        0 ||
        currentUser.isSuper === 1) &&
      (needPerTypes.length > 0 ? needPerTypes.includes(p.perType) : true)
    );
  });

  return permissionCache;
}

async function getCurrentUserPermissionDetail(currentUser, needPerTypes) {
  let permissionCache = await getCurrentUserPermission(
    currentUser,
    needPerTypes
  );
  permissionCache = permissionCache.map((p) => ({
    id: p.perId,
    name: p.perName,
    code: p.perCode,
    parentId: p.parentId,
    type: p.perType,
    url: p.perUrl,
    icon: p.perIcon,
    sort: p.sort,
    children: [],
  }));
  permissionCache = _.uniq(_.orderBy(permissionCache, ["sort"], ["asc"]));
  const menuPermission = permissionCache.filter((f) => f.type === "MENU");
  const buttonPermission = permissionCache
    .filter((f) => f.type === "BUTTON" && f.code)
    .map((m) => m.code);

  const permissionTree = commFunc.buildTreeData(menuPermission, {
    id: 2,
    parentId: 0,
    children: [],
  });
  return { menu: permissionTree.children, button: buttonPermission };
}

/**
 * 地址获取经纬度
 * 请求参数:
 "queryParams": {
    "address": "地址"
  }
 *
 */
async function addressToLatlonLogic(queryParams) {
  if (!queryParams.address) {
    throw new RetJson(
      i18n.SYS_ERROR_CODE,
      i18n.ADDRESS_LATLON_HTTP_POINT_ERROR
    );
  }

  const httpResult = await httpHandler.httpApiGetInvoke("GEOCODER", "", {
    address: queryParams.address,
  });
  if (!(httpResult && httpResult.errcode === 0 && httpResult.retobj)) {
    throw new RetJson(
      i18n.SYS_ERROR_CODE,
      i18n.ADDRESS_LATLON_HTTP_POINT_ERROR
    );
  }

  global.context.logger.info(
    "SYSTEM",
    "HTTP",
    "addressToLatlonLogic",
    "HTTP获取地址经纬度：",
    httpResult.retobj
  );

  const lat = httpResult.retobj.location ? httpResult.retobj.location.lat : 0;
  const lon = httpResult.retobj.location ? httpResult.retobj.location.lng : 0;
  if (!lat || !lon) {
    throw new RetJson(
      i18n.SYS_ERROR_CODE,
      i18n.ADDRESS_LATLON_HTTP_POINT_ERROR
    );
  }

  return { lat, lon };
}

/**
 * 经纬度获取地址
 * 请求参数:
 "queryParams": {
    "lat": "纬度",
    "lon": "经度",
  }
 *
 */
async function latlonToAddressLogic(queryParams) {
  if (
    !validator.isFloatFormat(queryParams.lat, { min: 0, neq_zero: true }) ||
    !validator.isFloatFormat(queryParams.lon, { min: 0, neq_zero: true })
  ) {
    return "";
  }

  const httpResult = await httpHandler.httpApiGetInvoke("GEOCODER", "", {
    location: `${queryParams.lat},${queryParams.lon}`,
  });
  if (!(httpResult && httpResult.errcode === 0 && httpResult.retobj)) {
    return "";
  }

  return httpResult.retobj.address || "";
}

async function uploadSingleFileLogic(ctx) {
  const file = ctx.request.files.file;
  if (!file) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.UPLOAD_NOT_FIND_FILE);
  }
  const module = ctx.request.body.module || "";

  const splits = file.name.split(".");
  const extname = splits.length > 1 ? splits[splits.length - 1] : "";
  const filename = `${moment().format("YYYYMMDDHHmm")}${Number.parseInt(
    Math.random() * 100000,
    10
  )}.${extname}`;

  const exists = await fs.existsSync(`./upload/${module ? `/${module}` : ""}`);
  if (!exists) {
    await fs.mkdirSync(`./upload/${module ? `/${module}` : ""}`, "0777");
  }

  // 创建可读流
  const reader = fs.createReadStream(file.path);
  // 创建可写流
  const upStream = fs.createWriteStream(
    `./upload/${module ? `/${module}` : ""}/${filename}`
  );
  // 可读流通过管道写入可写流
  reader.pipe(upStream);

  return `${module ? `/${module}` : ""}/${filename}`;
}

async function getUploadXlsxDataLogic(ctx) {
  const file = ctx.request.files.file;
  if (!file) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.UPLOAD_NOT_FIND_FILE);
  }

  const splits = file.name.split(".");
  const extname = splits.length > 1 ? splits[splits.length - 1] : "";
  if (!["xls", "xlsx"].includes(extname)) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.UPLOAD_NOT_FIND_FILE);
  }

  const data = xlsx.parse(file.path);

  if (!data || data.length < 0) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.UPLOAD_FILE_DATA_EMPTY);
  }

  return data;
}

async function getNationRegionListLogic(queryParams) {
  return await basicDao.getNationRegionSqlList(queryParams);
}

module.exports = {
  getCurrentUserPermission,
  getCurrentUserPermissionDetail,

  addressToLatlonLogic,
  latlonToAddressLogic,

  uploadSingleFileLogic,
  getUploadXlsxDataLogic,

  getNationRegionListLogic,
};
