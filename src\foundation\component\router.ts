import Trouter, { HTT<PERSON><PERSON><PERSON> } from "trouter";
import { Container } from "inversify";
import { Context } from "../../types";
import TokenController from "../../facade/open/Token";
import OrganController from "../../facade/open/Organ";
import CarController from "../../facade/open/Car";
import DeviceController from "../../facade/open/Device";
import LocusController from "../../facade/open/Locus";

/**
 * 路由对象
 */
export class Router {
  // 解析引擎
  private engine = new Trouter();

  /**
   * 注册路由
   * @param method
   * @param url
   * @param callback
   */
  public register(method: HTTPMethod, url: string, callback: Function) {
    this.engine.add(method, url, callback);
  }

  /**
   * 执行请求
   * @param method
   * @param url
   */
  public async handle(ctx: Context) {
    const { handlers, params } = this.engine.find(
      <HTTPMethod>ctx.method,
      ctx.path
    );
    if (!handlers.length) return;
    ctx.param = params;
    await handlers[0](ctx);
  }
}

// 路由准备
export default async (container: Container) => {
  // 绑定路由器
  const router: Router = new Router();
  container.bind("router").toConstantValue(router);

  // 绑定控制器
  container.bind(TokenController).toSelf();
  container.bind(OrganController).toSelf();
  container.bind(CarController).toSelf();
  container.bind(DeviceController).toSelf();
  container.bind(LocusController).toSelf();

  // 注册路由
  router.register("POST", "/api/v1/token", (ctx: Context) =>
    container.get(TokenController).create(ctx)
  );
  router.register("GET", "/api/v1/organ", (ctx: Context) =>
    container.get(OrganController).index(ctx)
  );
  router.register("GET", "/api/v1/car", (ctx: Context) =>
    container.get(CarController).index(ctx)
  );
  router.register("GET", "/api/v1/device", (ctx: Context) =>
    container.get(DeviceController).index(ctx)
  );
  router.register("GET", "/api/v1/loucs/:id", (ctx: Context) =>
    container.get(LocusController).show(ctx)
  );
};
