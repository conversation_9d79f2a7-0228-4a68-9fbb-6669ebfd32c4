const _ = require("lodash");
const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const roleDao = require("../lib/dao/uc_role_dao");

async function getRoleListLogic(queryParams) {
  const roleList = await roleDao.getRoleSqlList(queryParams);
  roleList.rows.forEach((e) => {
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return roleList;
}

function getRoleDropDownListLogic(queryParams) {
  return roleDao.getRoleDropDownSqlList(queryParams);
}

function getRolePermissionLogic(queryParams) {
  return roleDao.getRolePermissionSql(queryParams);
}

function _validateRoleRequest(requestData) {
  const rules = [
    { field: "roleName", title: "角色名", rule: "", msg: "", required: true },
    {
      field: "status",
      title: "状态",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    { field: "remark", title: "备注", rule: "", msg: "" },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  model.remark = model.remark || "";

  return [undefined, model];
}

async function insertRoleLogic(insertData, userName) {
  const [errmsg, roleModel] = _validateRoleRequest(insertData);
  if (errmsg) {
    throw new RetJson(i18n.SYS_ERROR_CODE, errmsg);
  }

  const isExists = await roleDao.getRoleRecordsSql(roleModel);
  if (isExists.record) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }
  roleModel.createUser = userName;

  await roleDao.insertRoleSql(roleModel);
}

async function updateRoleLogic(updateData, userName) {
  const [err, roleModel] = _validateRoleRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await roleDao.getRoleRecordsSql(roleModel);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_OVERTWO_EXISTS);
  }

  if (
    Number.parseInt(roleModel.roleId, 10) !== isExists.roleId &&
    isExists.record
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  roleModel.updateUser = userName;
  await roleDao.updateRoleSql(roleModel);
}

async function insertRolePermissionLogic(rolePermData) {
  const perCompact = _.compact(rolePermData.strPerId.split(","));
  const objRolePer = [];

  for (const item of perCompact) {
    objRolePer.push({
      roleId: rolePermData.roleId,
      perId: Number.parseInt(item, 10),
    });
  }

  const insertModel = { roleId: rolePermData.roleId, objRolePer: objRolePer };

  await roleDao.saveRolePermissionSql(insertModel);
}

module.exports = {
  getRoleListLogic,
  getRoleDropDownListLogic,
  getRolePermissionLogic,
  insertRoleLogic,
  updateRoleLogic,
  insertRolePermissionLogic,
};
