const mysqlDB = require("../common/mysql_pool");

function getOrganSqlList(queryParams) {
  const sql = `select uo.id,uo.id organ_id,uo.parent_id,uop.organ_name parent_name,uo.organ_name,uo.role_ids,uo.level,
  uo.sort,uo.path,uo.status,uo.remark,uo.create_user,uo.create_time
  from uc_organ uo
  left join uc_organ uop on uo.parent_id=uop.id
  where (:status=-1 or uo.status=:status) and (:parent_id=-1 or uo.path like concat('%,',:parent_id,',%'))
  and (:role_id=-1 or uo.role_ids like concat('%,',:role_id,',%')) and (:organ_id=-1 or uo.path like concat('%,',:organ_id,',%'))
  and (:search_key='' or concat(uo.organ_name,uo.remark) like concat('%',:search_key,'%'))
  order by uo.path`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getOrganTreeSql(queryParams) {
  const sql = `select uo.id,uo.id organ_id,uo.parent_id,uo.organ_name,uo.role_ids,uo.level,uo.sort,uo.path,
  uo.status,ifnull(oc.device_count, 0) count,ifnull(oc.device_count, 0) total_count,uo.remark,uo.create_user,uo.create_time
  from uc_organ uo
  left join (
    select gd.organ_id,count(*) device_count
    from gps_device gd
    join gps_car gc on gd.plate_no=gc.plate_no
    where gd.is_settle=0 and gd.is_delete=0
    group by gd.organ_id
  ) oc on uo.id=oc.organ_id
  where (:status=-1 or uo.status=:status) and (:parent_id=-1 or uo.path like  concat('%,',:parent_id,',%'))
  and (:role_id=-1 or role_ids like concat('%,',:role_id,',%')) and (:organ_id=-1 or uo.path like concat('%,',:organ_id,',%'))
  order by uo.path`;

  return mysqlDB.all(sql, queryParams);
}

function getOrganRecordsSql(queryParams) {
  const sql = `select count(*) record,id organ_id
  from uc_organ
  where (organ_name=:organ_name and parent_id=:parent_id)`;

  return mysqlDB.one(sql, queryParams);
}

function insertOrganSql(insertData, operationModel) {
  const sqlTasks = [];

  const insertSql = `insert into uc_organ
  (parent_id,organ_name,role_ids,sort,remark,status,create_user,create_time)
  values
  (:parent_id,:organ_name,:role_ids,:sort,:remark,:status,:create_user,now())`;

  const insertUserSql = `insert into uc_user
  (user_code,user_name,login_name,password,realname,sex,role_ids,organ_id,is_super,type,status,create_user,create_time)
  select ifnull(max(user_code),1000)+1,:organ_name,:organ_name,:password,:organ_name,0,:role_ids,
  last_insert_id(),0,1,:status,:create_user,now()
  from uc_user`;

  const pathSql = `update uc_organ uo
  left join uc_organ uop on uop.id=:parent_id
  set uo.path = concat(ifnull(uop.path,',0,'),(select last_insert_id()),','),
  uo.level=length(ifnull(uop.path,',0,'))-length(replace(ifnull(uop.path,',0,'),',',''))-1
  where uo.id=(select last_insert_id())`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: insertSql, params: insertData });
  sqlTasks.push({ sql: pathSql, params: insertData });
  sqlTasks.push({ sql: insertUserSql, params: insertData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

function updateOrganSql(updateData, operationModel) {
  const sqlTasks = [];

  const sql = `update uc_organ
  set organ_name=:organ_name,role_ids=:role_ids,sort=:sort,remark=:remark,status=:status,
  update_user=:update_user,update_time=now()
  where id=:organ_id`;

  const updateUserSql = `update uc_user
  set user_name=:organ_name,login_name=:organ_name,realname=:organ_name,role_ids=:role_ids,
  update_user=:update_user,update_time=now()
  where organ_id=:organ_id and type=1`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: sql, params: updateData });
  sqlTasks.push({ sql: updateUserSql, params: updateData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

function getOrganSql(queryParams) {
  const sql = `select uo.id organ_id,uo.parent_id,uo.organ_name,uo.path
  from uc_organ uo
  where uo.id=:organ_id`;

  return mysqlDB.one(sql, queryParams);
}

function getOrganParentSqlData(queryParams) {
  const sql = `select uo.id organ_id,uo.organ_name,uo.path,uop.id parent_id,uop.path parent_path
  from uc_organ uo
  join uc_organ uop on uop.id=:parent_id
  where uo.id=:organ_id`;

  return mysqlDB.one(sql, queryParams);
}

function updateOrganParentSql(updateData, operationModel) {
  const sqlTasks = [];

  const childSql = `update uc_organ uo
  join uc_organ uon on uon.id=:parent_id
  join uc_organ uo1 on uo.path like concat(uo1.path,'%') and uo1.id=:organ_id
  set uo.path = replace(uo.path,uo1.path,concat(uon.path,:organ_id,',')),
  uo.level=length(replace(uo.path,uo1.path,concat(uon.path,:organ_id,',')))-length(replace(replace(uo.path,uo1.path,concat(uon.path,:organ_id,',')),',',''))-2
  where uo.id!=:organ_id`;

  const pathSql = `update uc_organ uo
  join uc_organ uop on uop.id=:parent_id
  set uo.parent_id=:parent_id,uo.path = concat(ifnull(uop.path,',0,'),uo.id,','),
  uo.level=length(ifnull(uop.path,',0,'))-length(replace(ifnull(uop.path,',0,'),',',''))-1,
  uo.update_user=:update_user,uo.update_time=now()
  where uo.id=:organ_id`;

  const content = `(select concat('更改父组织 车组名: ', uo.organ_name,' 车组Id ', ${updateData.organId}, ' 从 [',uo.parent_id,'] ', uop.organ_name,',  更改到 [${updateData.parentId}] ${updateData.parentName}')
  from uc_organ uo
  join uc_organ uop on uo.parent_id=uop.id
  where uo.id=${updateData.organId})`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,${content},:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: optSql, params: operationModel });
  sqlTasks.push({ sql: childSql, params: updateData });
  sqlTasks.push({ sql: pathSql, params: updateData });

  return mysqlDB.executeTransaction(sqlTasks);
}

function getOrganInfoStateSql(queryParams) {
  const sql = `select uo.id organ_id,ifnull(gd.device_count,0) device_count,ifnull(uoo.child_count,0) child_count,uo.status
  from uc_organ uo
  left join (
    select organ_id,count(*) device_count
    from gps_device
    where organ_id=:organ_id
  ) gd on uo.id=gd.organ_id
  left join (
    select parent_id organ_id,count(*) child_count
    from uc_organ
    where parent_id=:organ_id
  ) uoo on uo.id=uoo.organ_id
  where uo.id=:organ_id`;

  return mysqlDB.one(sql, queryParams);
}

function deleteOrganSql(deleteData, operationModel) {
  const sqlTasks = [];

  const sql = `delete from uc_organ
  where id=:organ_id`;

  const content = `(select concat('删除车组名: ', uo.organ_name,' 车组Id ', ${deleteData.organId}, ' 父车组 [',uo.parent_id,'] ', uop.organ_name)
  from uc_organ uo
  join uc_organ uop on uo.parent_id=uop.id
  where uo.id=${deleteData.organId})`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,${content},:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: optSql, params: operationModel });
  sqlTasks.push({ sql: sql, params: deleteData });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getOrganSql,
  getOrganSqlList,
  getOrganTreeSql,
  getOrganRecordsSql,
  getOrganInfoStateSql,
  insertOrganSql,
  updateOrganSql,
  getOrganParentSqlData,
  updateOrganParentSql,
  deleteOrganSql,
};
