const mysqlDB = require("../common/mysql_pool");

function getHomeAlarmCountSql(queryParams) {
  const sql = `select ga.alarm_class,count(ga.id) count
  from gps_alarm_cycle ga
  join gps_device gd on ga.imei=gd.imei and gd.is_settle=0 and gd.is_delete=0
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where ga.handle_status=0
  group by ga.alarm_class`;

  // return mysqlDB.all(sql, queryParams);
  return [];
}

function getHomeAttentionCountSql(queryParams) {
  const sql = `select count(*) count
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.is_settle=0 and gd.is_delete=0 and gd.attention>0`;

  return mysqlDB.one(sql, queryParams);
}

function getHomePackingCountSql(queryParams) {
  const sql = `select count(*) count
  from gps_stop gst
  join gps_device gd on gst.imei=gd.imei and gd.is_settle=0 and gd.is_delete=0
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gst.stop_second >= :start_second and gst.stop_second < :end_second`;

  // return mysqlDB.one(sql, queryParams);
  return {count:0}
}

function getMultiDeviceOfflineSqlList(queryParams) {
  const sql = `select gd1.organ_name,gd1.plate_no,gc.car_owner,gc.car_brand,gc.car_model,gc.owner_phone,gc.remark car_remark,gd1.device_info
  from (
    select uo.organ_name,gd.plate_no,group_concat(concat(gd.imei,'$',gd.device_type,'$',gd.device_model,
    '$',gd.online_state,'$',gd.last_online_time,'$',gd.last_offline_time)) device_info
    from gps_device gd
    join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
    where gd.is_settle=0 and gd.is_delete=0
    group by gd.plate_no
    having sum(if(gd.online_state=2,1,0))>1 and sum(if(gd.imei is null,0,1))<10
  ) gd1
  join gps_car gc on gd1.plate_no=gc.plate_no
  order by gd1.plate_no`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceTypeReportSqlList(queryParams) {
  const sql = `select bpm.val device_type_name,ifnull(gds.device_count,0) device_count
  from bc_property_map bpm
  left join (
    select gd.device_type,count(gd.id) device_count,count(distinct gd.plate_no) car_count
    from gps_device gd
    join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
    where gd.is_settle=0 and gd.is_delete=0
    group by gd.device_type
  ) gds on bpm.key=gds.device_type
  where bpm.code='DEVICE_TYPE' and bpm.status=1
  order by bpm.sort`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceModelReportSqlList(queryParams) {
  const sql = `select gd.device_model,count(gd.id) device_count,count(distinct gd.plate_no) car_count
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.is_settle=0 and gd.is_delete=0
  group by gd.device_model`;

  return mysqlDB.all(sql, queryParams);
}

function getOnlineStateReportSqlList(queryParams) {
  const sql = `select bpm.val online_state_name,ifnull(gds.device_count,0) device_count
  from bc_property_map bpm
  left join (
    select gd.online_state,count(gd.id) device_count,count(distinct gd.plate_no) car_count
    from gps_device gd
    join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
    where gd.is_settle=0 and gd.is_delete=0
    group by gd.online_state
  ) gds on bpm.key=gds.online_state
  where bpm.code='ONLINE_STATE' and bpm.status=1
  order by bpm.sort`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceOfflineReportSqlList(queryParams) {
  const sql = `select bpm.val offline_interval_name,ifnull(gds.device_count,0) device_count
  from bc_property_map bpm
  left join (
    select
    (case
        when (unix_timestamp(now())-gd.last_offline_time)<86400 then '0_1'
        when (unix_timestamp(now())-gd.last_offline_time)>=86400 and (unix_timestamp(now())-gd.last_offline_time)<259200 then '1_3'
        when (unix_timestamp(now())-gd.last_offline_time)>=259200 and (unix_timestamp(now())-gd.last_offline_time)<604800 then '3_7'
        when (unix_timestamp(now())-gd.last_offline_time)>=604800 and (unix_timestamp(now())-gd.last_offline_time)<2592000 then '7_30'
        when (unix_timestamp(now())-gd.last_offline_time)>=2592000 then '30_N'
    else NULL end) offline_interval,count(gd.id) device_count,count(distinct gd.plate_no) car_count
    from gps_device gd
    join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
    where gd.online_state=:online_state and gd.is_settle=0 and gd.is_delete=0
    group by offline_interval
  ) gds on bpm.key=gds.offline_interval
  where bpm.code='OFFLINE_INTERVAL' and bpm.status=1
  order by bpm.sort`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceSettleReportSqlList(queryParams) {
  const sql = `select gd.is_settle,count(gd.id) device_count,count(distinct gd.plate_no) car_count
  from gps_device gd
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gd.is_delete=0
  group by gd.is_settle`;

  return mysqlDB.all(sql, queryParams);
}

function getDeviceSimRenewReportSqlList(queryParams) {
  const sql = `select bpm.val expire_interval_name,ifnull(gds.device_count,0) device_count
  from bc_property_map bpm
  left join (
    select
    (case
        when sim_state=:expire_state then '-1'
        when sim_state=:used_state and (unix_timestamp(now())-gd.sim_expire_time)<259200 then '3'
        when sim_state=:used_state and (unix_timestamp(now())-gd.sim_expire_time)>=259200 and (unix_timestamp(now())-gd.sim_expire_time)<604800 then '7'
        when sim_state=:used_state and (unix_timestamp(now())-gd.sim_expire_time)>=604800 and (unix_timestamp(now())-gd.sim_expire_time)<2592000 then '30'
    else NULL end) expire_interval,count(gd.id) device_count,count(distinct gd.plate_no) car_count
    from gps_device gd
    join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
    where gd.is_settle=0 and gd.is_delete=0
    group by expire_interval
  ) gds on bpm.key=gds.expire_interval
  where bpm.code='EXPIRE_INTERVAL' and bpm.status=1
  order by bpm.sort`;

  return mysqlDB.all(sql, queryParams);
}

function getProvinceTop10ReportSqlList(queryParams) {
  const sql = `select bnr.short_name province_name,ifnull(gds.device_count,0) device_count
  from bc_nation_region bnr
  left join (
    select gd.province,count(gd.id) device_count,count(distinct gd.plate_no) car_count
    from gps_device gd
    join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
    where gd.is_settle=0 and gd.is_delete=0
    group by gd.province
  ) gds on bnr.id=concat(gds.province,'0000')
  where bnr.level=1
  order by ifnull(gds.device_count,0) desc`;

  return mysqlDB.all(sql, queryParams);
}

module.exports = {
  getHomeAlarmCountSql,
  getHomeAttentionCountSql,
  getHomePackingCountSql,
  getMultiDeviceOfflineSqlList,

  getDeviceTypeReportSqlList,
  getDeviceModelReportSqlList,
  getOnlineStateReportSqlList,
  getDeviceOfflineReportSqlList,
  getDeviceSettleReportSqlList,
  getDeviceSimRenewReportSqlList,
  getProvinceTop10ReportSqlList,
};
