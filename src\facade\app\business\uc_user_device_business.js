const _ = require("lodash");
const commFunc = require("../lib/common/comm_func");

const deviceDao = require("../lib/dao/device_dao");
const userDeviceDao = require("../lib/dao/uc_user_device_dao");


function getUserDeviceListLogic(queryParams) {
  return userDeviceDao.getUserDeviceSqlList(queryParams)
}

/**
 * 批量更新用户设备信息
 * @param queryParams
 * @param currentUser
 * @returns {Promise<boolean>}
 */
async function batchUpdateUserDeviceLogic(queryParams, currentUser) {
  if(!queryParams.addImeis && !queryParams.delImeis){
    return false;
  }
  queryParams.addDevices = await deviceDao.getDeviceSqlAllList({addImeis: queryParams.addImeis.join(",")});
  queryParams.delImeis = _.uniq(queryParams.addImeis.concat(queryParams.delImeis)).join(","); // 删除所有变化的数据
  queryParams.createName = currentUser.userName;
  await userDeviceDao.batchUpdateUserDeviceSql(queryParams);
}

function _validateUserRequest(requestData) {

  const rules = [
    {
      field: "userId",
      title: "用户Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    }
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }
  return [undefined, model];
}


module.exports = {
  getUserDeviceListLogic,
  batchUpdateUserDeviceLogic,
};
