# 接口服务正式环境控制器
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gps-old-back
  namespace: application-${APP_ENV}
  labels:
    group: gps
    app: old-back
spec:
  selector:
    matchLabels:
      group: gps
      app: old-back
  template:
    metadata:
      labels:
        group: gps
        app: old-back
    spec:
      imagePullSecrets:
        - name: image-pull-secret
      containers:
        - name: gps-old-back
          image: ${IMAGE}
          imagePullPolicy: Always
          # 资源限制
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 250Mi
          # 端口申明
          ports:
            - containerPort: 7001
              name: api
              protocol: TCP
            - containerPort: 7002
              name: open
              protocol: TCP
          # 日志收集
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          env:
            # 日志收集
            - name: aliyun_logs_app-runtime-${APP_ENV}
              value: "stdout"
            # 环境设置
            - name: NODE_ENV
              value: "production"
            - name: SERVICE_STATUS
              value: "prod"
            # 签名配置
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  key: jwt_secret
                  name: env-secret
            # 时区设置
            - name: TZ
              value: "Asia/Shanghai"
            - name: DEVICE_STATE_TIME
              value: "300"
            # MYSQL配置
            - name: MYSQL_HOST
              valueFrom:
                secretKeyRef:
                  key: host
                  name: env-mysql
            - name: MYSQL_USER
              valueFrom:
                secretKeyRef:
                  key: user
                  name: env-mysql
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: env-mysql
            - name: MYSQL_DATABASE
              valueFrom:
                secretKeyRef:
                  key: gps_monitor
                  name: env-mysql
            - name: MYSQL_PORT
              valueFrom:
                secretKeyRef:
                  key: port
                  name: env-mysql
            # MONGO配置
            - name: MONGO_HOST
              valueFrom:
                secretKeyRef:
                  key: host
                  name: env-mongo
            - name: MONGO_USER
              valueFrom:
                secretKeyRef:
                  key: user
                  name: env-mongo
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: env-mongo
            - name: MONGO_DATABASE
              valueFrom:
                secretKeyRef:
                  key: gps_monitor
                  name: env-mongo
            - name: MONGO_PORT
              valueFrom:
                secretKeyRef:
                  key: port
                  name: env-mongo
            # REDIS配置
            - name: REDIS_HOST
              valueFrom:
                secretKeyRef:
                  key: host
                  name: env-redis
            - name: REDIS_PORT
              valueFrom:
                secretKeyRef:
                  key: port
                  name: env-redis
            - name: REDIS_PASS
              valueFrom:
                secretKeyRef:
                  key: password
                  name: env-redis
            - name: REDIS_DB
              valueFrom:
                secretKeyRef:
                  key: db
                  name: env-redis
            # OLD_REDIS配置
            - name: OLD_REDIS_HOST
              value: "**************"
            - name: OLD_REDIS_PORT
              value: "6379"
            - name: OLD_REDIS_PASS
              value: "Monitor_1109"
            - name: OLD_REDIS_DB
              value: "0"
            # redis同步数据
            - name: OLD_REDIS_SYNC
              value: "true"
            # 报警定时任务规则
            - name: ALAR_SCHEDULE_STR
              value: "0 * * * * *"
            - name: ALAR_SCHEDULE_MIN
              value: "1"
---
# 内部服务
apiVersion: v1
kind: Service
metadata:
  name: gps-old-back
  namespace: application-${APP_ENV}
  labels:
    group: gps
    app: old-back
spec:
  type: ClusterIP
  selector:
    group: gps
    app: old-back
  ports:
    - name: http-api
      port: 7001
      protocol: TCP
      targetPort: 7001
    - name: http-open
      port: 7002
      protocol: TCP
      targetPort: 7002
