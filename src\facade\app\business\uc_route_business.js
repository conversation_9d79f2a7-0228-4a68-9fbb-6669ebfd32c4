const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const routeDao = require("../lib/dao/uc_route_dao");

async function getRouteListLogic(queryParams) {
  const [routeList, keyValConfig] = await Promise.all([
    routeDao.getRouteSqlList(queryParams),
    global.context.getPropertymapKeyVal(["SYSTEM_TYPE", "ROUTE_TYPE"]),
  ]);

  routeList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    e.middlewares = JSON.parse(e.middlewares || "[]").join("，");
    e.routeTypeName = keyValConfig.ROUTE_TYPE[e.routeType] || "";
    e.systemTypeName = keyValConfig.SYSTEM_TYPE[e.systemType] || "";
  });

  return routeList;
}

async function insertRouteLogic(routeData, repeatPers) {
  if (repeatPers.length > 0) {
    throw new RetJson(
      i18n.SYS_ERROR_CODE,
      `${i18n.ROUTE_PERMISSION_BUTTON_REPEAT}： ${repeatPers}`
    );
  }

  if (!routeData || !validator.isIntFormat(routeData.systemType, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  const rules = [
    {
      field: "systemType",
      rule: "isIntFormat",
      opt: { min: 1 },
      required: true,
    },
    { field: "middlewares", rule: "isArray", opt: { min: 0 }, required: true },
    {
      field: "routeType",
      rule: "isIntFormat",
      opt: { min: 1 },
      required: true,
    },
    { field: "httpMethod", required: true },
    { field: "routePath", required: true },
    { field: "methodName", required: true },
  ];

  routeData.routes = routeData.routes.filter(
    (f) => typeof f.routePath === "string"
  );
  if (!validator.isArray(routeData.routes, { min: 1 }, rules)) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  await routeDao.insertRouteSql(routeData);
}

module.exports = {
  getRouteListLogic,
  insertRouteLogic,
};
