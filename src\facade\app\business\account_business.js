const utility = require("utility");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const accountDao = require("../lib/dao/account_dao");

/**
 * 验证登录数据是否合法
 * 请求参数:
 "userName": "用户名",
 "password": "密码"
 *
 */
async function checkSignInAccountLogic(signinData) {
  const signinResult = await accountDao.getUserDataByUserNameSql(signinData);

  if (!signinResult) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.SIGN_IN_USER_NOT_EXIST);
  }

  if (utility.md5(signinData.password) !== signinResult.password) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.SIGN_IN_PASSWORD_ERROR);
  }

  delete signinResult.password;
  return signinResult;
}

module.exports = {
  checkSignInAccountLogic,
};
