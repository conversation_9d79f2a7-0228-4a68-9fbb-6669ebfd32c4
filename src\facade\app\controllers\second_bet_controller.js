const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const secondBetBusiness = require("../business/second_bet_business");

/**
 * @api {GET} /secondbet/getsecondbetlist 二押点管理-获取二押点列表
 * @apiName getsecondbetlist
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-10
 *
 * @apiParam  {String} searchKey 搜索关键字，车牌号，车架号，车主
 * @apiParam  {Number} secondBetType 二押点类型
 * @apiParam  {Number} dangerLevel 危险等级
 * @apiParam  {Number} shapeType 形状类型
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getSecondBetList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点管理-获取二押点列表",
  buttonPerm: "SB-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: currentUser.organId,
        isShare: -1,
        secondBetType: validator.isIntFormat(reqQueryParams.secondBetType, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.secondBetType, 10)
          : -1,
        dangerLevel: validator.isIntFormat(reqQueryParams.dangerLevel, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.dangerLevel, 10)
          : -1,
        shapeType: validator.isIntFormat(reqQueryParams.shapeType, { min: 1 })
          ? Number.parseInt(reqQueryParams.shapeType, 10)
          : -1,

        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };

      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const secondBetList = await secondBetBusiness.getSecondBetListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        secondBetList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getSecondBetList",
          "获取二押点列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getSecondBetList",
        "获取二押点列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /secondbet/insertsecondbet 二押点管理-添加二押点
 * @apiName insertsecondbet
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-11
 *
 * @apiParam  {String} secondbetName 二押点名称
 * @apiParam  {Number} shapeType 形状类型
 * @apiParam  {Number} dangerLevel 危险等级
 * @apiParam  {String} shapeData 二押点数据
 * @apiParam  {Number} stopMinutes 停留分钟数
 * @apiParam  {Number} isShare 是否分享
 * @apiParam  {String} remark 备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertSecondBet = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点管理-添加二押点",
  buttonPerm: "SB-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.organId = currentUser.organId;
      requestData.isBaidu = true;

      const insertCount = await secondBetBusiness.insertSecondBetLogic(
        requestData
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertSecondBet",
        `添加二押点成功 ${insertCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertSecondBet",
          "添加二押点失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertSecondBet",
        "添加二押点出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /secondbet/updatesecondbet 二押点管理-更新二押点
 * @apiName updatesecondbet
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-12
 *
 * @apiParam  {Number} secondBetId 二押点Id
 * @apiParam  {String} secondbetName 二押点名称
 * @apiParam  {Number} shapeType 形状类型
 * @apiParam  {Number} dangerLevel 危险等级
 * @apiParam  {String} shapeData 二押点数据
 * @apiParam  {Number} stopMinutes 停留分钟数
 * @apiParam  {Number} isShare 是否分享
 * @apiParam  {String} remark 备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateSecondBet = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点管理-更新二押点",
  buttonPerm: "SB-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.isBaidu = true;

      const updateCount = await secondBetBusiness.updateSecondBetLogic(
        requestData
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateSecondBet",
        `更新二押点成功 ${updateCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateSecondBet",
          "更新二押点失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateSecondBet",
        "更新二押点出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /secondbet/deletesecondbet 二押点管理-删除二押点
 * @apiName deletesecondbet
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-13
 *
 * @apiParam  {Number} secondBetIds 二押点Ids
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteSecondBet = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点管理-删除二押点",
  buttonPerm: "SB-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      const deleteCount = await secondBetBusiness.deleteSecondBetLogic(
        requestData
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteSecondBet",
        `删除二押点成功 ${deleteCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteSecondBet",
          "删除二押点失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteSecondBet",
        "删除二押点出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /secondbet/getorganbetdevicelist 二押点绑定-获取组织设备列表
 * @apiName getorganbetdevicelist
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-25
 *
 * @apiParam  {Number} organIds 机构Ids
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getOrganBetDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点绑定-获取组织设备列表",
  buttonPerm: "SB-25",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organIds: reqQueryParams.organIds,
        organId: currentUser.organId,

        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };

      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const secondBetList = await secondBetBusiness.getOrganBetDeviceListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        secondBetList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getOrganBetDeviceList",
          "获取组织设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOrganBetDeviceList",
        "获取组织设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /secondbet/getsecondbetdevicelist 二押点绑定-获取二押点绑定设备列表
 * @apiName getsecondbetdevicelist
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-20
 *
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getSecondBetDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点绑定-获取二押点绑定设备列表",
  buttonPerm: "SB-20",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: currentUser.organId,

        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };

      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const secondBetList = await secondBetBusiness.getSecondBetDeviceListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        secondBetList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getSecondBetDeviceList",
          "获取二押点绑定设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getSecondBetDeviceList",
        "获取二押点绑定设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /secondbet/insertsecondbetdevice 二押点绑定-添加二押点绑定设备
 * @apiName insertsecondbetdevice
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-21
 *
 * @apiParam  {Array} bindData 绑定数据，如下：{ imei 设备号 }
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertSecondBetDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点绑定-添加二押点绑定设备",
  buttonPerm: "SB-21",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      const insertList = requestData.bindData.map((m) => {
        m.userName = currentUser.loginName;
        return m;
      });
      const insertCount = await secondBetBusiness.insertSecondBetDeviceLogic(
        insertList
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertSecondBetDevice",
        `添加二押点绑定设备成功 ${insertCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertSecondBetDevice",
          "添加二押点绑定设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertSecondBetDevice",
        "添加二押点绑定设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /secondbet/deletesecondbetdevice 二押点绑定-删除二押点绑定设备
 * @apiName deletesecondbetdevice
 * @apiGroup SecondBet
 * @apiVersion  1.0.0
 * @apiPermission SB-23
 *
 * @apiParam  {Number} betDeviceIds 二押点设备Ids
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteSecondBetDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "二押点绑定-删除二押点绑定设备",
  buttonPerm: "SB-23",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      const deleteCount = await secondBetBusiness.deleteSecondBetDeviceLogic(
        requestData
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteSecondBetDevice",
        `删除二押点绑定设备成功 ${deleteCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteSecondBetDevice",
          "删除二押点绑定设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteSecondBetDevice",
        "删除二押点绑定设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
