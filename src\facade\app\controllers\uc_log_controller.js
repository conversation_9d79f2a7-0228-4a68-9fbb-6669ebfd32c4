const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const logBusiness = require("../business/uc_log_business");
const validator = require("../lib/common/validator_extend");

/**
 * @api {GET} /uclog/getloggerlist 日志管理-获取日志分页列表
 * @apiName getloggerlist
 * @apiGroup UcLog
 * @apiVersion  1.0.0
 * @apiPermission UL-10
 *
 * @apiParam  {string} collection 必须 -- 格式为：YYYYMM 如：201705
 * @apiParam  {string} source 日志来源
 * @apiParam  {string} logLevel 日志级别
 * @apiParam  {string} logType 日志类型
 * @apiParam  {string} logModule 模块
 * @apiParam  {string} timeEnable 是否时间筛选
 * @apiParam  {string} startDate 开始日期
 * @apiParam  {string} endDate 结束日期
 * @apiParam  {string} logContent 内容
 * @apiParam  {string} clientKey 客户端Id
 * @apiParam  {number=0,1} status 状态 0：禁用、1：启用
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiParamExample  {json} 请求示例:
   {
     collection : 201705,
     clientKey : cm001
   }
 *
 * @apiSuccessExample {json} 响应示例:
   {
     "errcode":0,
     "errmsg":"操作成功",
     "retobj":{
        _id: "5ad9507a952c3825123e9529",
        createTime: "2018-04-20 10:29:14",
        logLevel: "INFO",
        nodeId: "16007",
        source: "USER-CENTER",
        key: "cmadmin",
        logType: "USER",
        module: "signIn",
        logContent: "用户登录成功"
     }
   }
 */
exports.getLoggerList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "日志管理-获取日志分页列表",
  buttonPerm: "UL-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;

      const collection = reqQueryParams.collection || "";
      // if (!collection) {
      //   ctx.body = new RetJson(i18n.SYS_ERROR_CODE, `传入年月${i18n.REQUEST_INPUT_DATA_EMPTY}`, { total: 0, rows: [] });
      //   return;
      // }
      //
      // if (!validator.isDateFormat(collection, 'YYYYMM')) {
      //   ctx.body = new RetJson(i18n.SYS_ERROR_CODE, `传入年月${i18n.REQUEST_INPUT_DATA_ERROR}`, { total: 0, rows: [] });
      //   return;
      // }

      const queryParams = {
        collection: collection.replace("-", ""),
        source: reqQueryParams.source || "ALL",
        logLevel: reqQueryParams.logLevel || "ALL",
        logType: reqQueryParams.logType || "",
        logModule: reqQueryParams.logModule || "",
        timeEnable: `${reqQueryParams.timeEnable}` === "1",
        startDate: reqQueryParams.startDate || "",
        endDate: reqQueryParams.endDate || "",
        logContent: reqQueryParams.logContent || "",
        clientKey: reqQueryParams.clientKey || "",
      };

      const currentPage = `${reqQueryParams.currentPage}`;
      queryParams.pageSize = validator.isIntFormat(
        `${reqQueryParams.pageSize}`,
        { min: 1 }
      )
        ? Number.parseInt(reqQueryParams.pageSize, 10)
        : 20;
      queryParams.offset = validator.isIntFormat(currentPage, { min: 1 })
        ? (Number.parseInt(currentPage, 10) - 1) * queryParams.pageSize
        : 0;
      queryParams.clientKey = queryParams.clientKey
        .split(" ")
        .filter((key) => key.trim().length > 0);

      if (
        queryParams.timeEnable &&
        !(
          validator.isDateFormat(queryParams.startDate, "YYYYMMDDHHmm") &&
          validator.isDateFormat(queryParams.endDate, "YYYYMMDDHHmm")
        )
      ) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.LOG_TIME_FORMAT_ERROR,
          { total: 0, rows: [] }
        );
        return;
      }

      if (
        queryParams.timeEnable &&
        moment(`${queryParams.startDate}:00`).format("x") >=
          moment(`${queryParams.endDate}:00`).format("x")
      ) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.LOG_TIME_FORMAT_ERROR,
          { total: 0, rows: [] }
        );
        return;
      }

      const loggerList = await logBusiness.getLoggerListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        loggerList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MONGODB",
        "getLoggerList",
        "获取日志分页列表错误:",
        err
      );

      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};
