const staticConfig = require("../../config/config_static");
const permissionDao = require("../../lib/dao/uc_permission_dao");

exports.getCacheUserPermission = async function () {
  const systemType = 2;
  let permissionCache = await global.context.redisClient.get(
    staticConfig.redisPrename.permission
  );
  permissionCache = JSON.parse(permissionCache || "[]");
  if (permissionCache.length > 0) {
    permissionCache = permissionCache.filter(
      (f) => `${f.systemType}` === `${systemType}`
    );
    return permissionCache;
  }

  const [permissionSql, rolePermission] = await Promise.all([
    permissionDao.getPermissionSqlList({
      roleIds: "",
      status: 1,
      systemType,
      parentId: -1,
    }),
    permissionDao.getRolePermissionSqlList(),
  ]);

  permissionCache = permissionSql.map((mp) => {
    mp.roleList = rolePermission
      .filter((frp) => frp.perId === mp.perId)
      .map((mrp) => mrp.roleId)
      .join(",");

    mp.roleList = mp.roleList.length > 0 ? `,${mp.roleList},` : "";
    return mp;
  });

  await global.context.redisClient.set(
    staticConfig.redisPrename.permission,
    permissionCache
  );
  return permissionCache;
};
