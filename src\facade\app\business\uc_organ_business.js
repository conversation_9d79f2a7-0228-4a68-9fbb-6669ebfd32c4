const _ = require("lodash");
const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const utility = require("utility");

const organDao = require("../lib/dao/uc_organ_dao");
const roleDao = require("../lib/dao/uc_role_dao");

async function getOrganTreeLogic(parentNode) {
  const queryParams = {
    status: -1,
    parentId: -1,
    organId: parentNode.organId || -1,
    pagination: false,
    roleId: -1,
  };

  const organSql = await organDao.getOrganTreeSql(queryParams);
  if (!organSql || organSql.length === 0) {
    return {};
  }

  organSql.forEach((m) => {
    m.path = m.path.substring(1, m.path.length - 1);
    // m.countName = `${m.organName} (共 ${m.totalCount} 台)`;
  });

  parentNode = commFunc.buildTreeData(organSql, parentNode);
  parentNode = commFunc.calculationTreeCount(parentNode);
  return parentNode;
}

async function getOrganListLogic(queryParams) {
  const [organList, roleList] = await Promise.all([
    organDao.getOrganSqlList(queryParams),
    roleDao.getRoleDropDownSqlList({ status: -1, organId: 0 }),
  ]);

  organList.rows.forEach((e, i) => {
    commFunc.formatNullToString(e);

    const compactRoles = Array.from(
      new Set((e.roleIds || "").split(","))
    ).filter((f) => !!f);
    const userRoles = compactRoles.map((roleId) =>
      _.result(
        roleList.find((f) => `${f.roleId}` === `${roleId}`),
        "roleName"
      )
    );

    e.index = queryParams.offset + i + 1;
    e.strOrganName = `${"-".repeat((e.level - 1) * 2)}${e.organName}`;
    e.strRoleNames = userRoles.join(",");
    e.strRoleIds = e.roleIds.substring(1, e.roleIds.length - 1);
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
    delete e.path;
  });

  return organList;
}

async function getOrganOneLogic(queryParams) {
  const organ = await organDao.getOrganSql(queryParams)
  return organ;
}

function _validateOrganRequest(requestData) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    },
    {
      field: "organName",
      title: "机构名称",
      rule: "",
      msg: "",
      required: true,
    },
    {
      field: "parentId",
      title: "父机构",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    { field: "sort", title: "排序", rule: "isIntFormat", msg: "" },
    {
      field: "strRoleIds",
      title: "角色Id列表",
      rule: "isCommaSeparated",
      msg: "",
      required: true,
    },
    {
      field: "status",
      title: "状态",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  model.sort = model.sort || 0;
  model.remark = model.remark || "";
  const strRoleIds = Array.from(new Set((model.strRoleIds || "").split(",")))
    .filter((f) => !!f)
    .sort()
    .join(",");
  model.roleIds = `,${strRoleIds},`;

  delete model.strRoleIds;
  return [undefined, model];
}

async function insertOrganLogic(insertData, currentUser) {
  insertData.opt = "i";
  const [err, userModel] = _validateOrganRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await organDao.getOrganRecordsSql(userModel);
  if (isExists.record) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  userModel.createUser = currentUser.loginName;
  userModel.password = utility.md5("000000");

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "新增组织",
    content: `新增组织 ${userModel.organName} `,
  };
  return await organDao.insertOrganSql(userModel, operationModel);
}

async function updateOrganLogic(updateData, currentUser) {
  updateData.opt = "u";
  const [err, userModel] = _validateOrganRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await organDao.getOrganRecordsSql(userModel);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_OVERTWO_EXISTS);
  }

  if (
    Number.parseInt(userModel.organId, 10) !== isExists.organId &&
    isExists.record
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  userModel.updateUser = currentUser.loginName;

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "编辑组织",
    content: `编辑组织 ${isExists.organId}, ${userModel.organName} `,
  };
  await organDao.updateOrganSql(userModel, operationModel);
}

async function updateOrganParentLogic(updateData, currentUser) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "parentId",
      title: "父机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err, updateModel] = commFunc.checkRequestData(updateData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  updateModel.updateUser = currentUser.loginName;

  const parentData = await organDao.getOrganParentSqlData(updateData);

  // 判断父机构在不在机构的子节点中
  if (!parentData || parentData.parentPath.indexOf(parentData.path) > -1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.CHANGE_ORGAN_PARENT);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "更改父组织",
  };
  await organDao.updateOrganParentSql(updateModel, operationModel);
}

async function deleteOrganLogic(deleteData, currentUser) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err, deleteModel] = commFunc.checkRequestData(deleteData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const organData = await organDao.getOrganInfoStateSql(deleteModel);
  if (!organData || organData.status === 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.BEFORE_DELETE_STATUS_DISABLE);
  }

  if (organData.deviceCount > 0 || organData.childCount > 0) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.BEFORE_DEL_ORGAN_DEL);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "删除组织",
  };
  await organDao.deleteOrganSql(deleteModel, operationModel);
}

module.exports = {
  getOrganOneLogic,
  getOrganTreeLogic,
  getOrganListLogic,
  insertOrganLogic,
  updateOrganLogic,
  updateOrganParentLogic,
  deleteOrganLogic,
};
