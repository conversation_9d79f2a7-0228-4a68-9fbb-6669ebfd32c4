import { Context } from "../../types";
import { Car } from "../../domain/entity/Car";
import { injectable, inject } from "inversify";
import { In, Repository } from "typeorm";
import { Organ } from "../../domain/entity/Organ";

/**
 * 授权接口
 */
@injectable()
export default class {
  @inject("carRepository")
  carRepository: Repository<Car>;

  @inject("organRepository")
  organRepository: Repository<Organ>;

  /**
   * 获取子组织
   * @param id
   */
  async getChildrens(id: number) {
    const res = [id];
    const list = await this.organRepository.find({
      where: {
        parentId: id,
        status: 1,
      },
    });
    if (list.length <= 0) return res;
    const jobs = list.map((item) => this.getChildrens(item.id));
    const childrens = await Promise.all(jobs);
    return res.concat(...childrens);
  }

  /**
   * 创建访问凭据
   * @param ctx
   */
  async index(ctx: Context) {
    const ids = await this.getChildrens(ctx.token.aud);

    // 查找车辆
    const cars = await this.carRepository.find({
      where: {
        organId: In(ids),
        isDelete: false,
      },
    });

    // 生成结果
    ctx.body = cars.map((car: Car) => ({
      id: car.id,
      organ: car.organId,
      plate_no: car.plateNo,
      brand: car.brand,
      model: car.model,
      remark: car.remark,
      created_at: Math.floor(car.createdAt.getTime() / 1000),
      updated_at: Math.floor(car.updatedAt.getTime() / 1000),
    }));
  }
}
