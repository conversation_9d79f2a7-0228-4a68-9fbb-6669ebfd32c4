import { Context } from "../../types";
import { Organ } from "../../domain/entity/Organ";
import { injectable, inject } from "inversify";
import { Repository } from "typeorm";

/**
 * 授权接口
 */
@injectable()
export default class {
  @inject("organRepository")
  organRepository: Repository<Organ>;

  /**
   * 转树结果
   * @param id
   */
  async toTree(id: number) {
    const list = await this.organRepository.find({
      where: {
        parentId: id,
        status: 1,
      },
      order: {
        sort: "DESC",
      },
    });
    const res = list.map(async (item) => {
      const data: any = {
        id: item.id,
        name: item.name,
        remark: item.remark,
      };
      const children = await this.toTree(item.id);
      if (children.length) data.children = children;
      return data;
    });
    return await Promise.all(res);
  }

  /**
   * 获取当前组织旗下组织
   * @param ctx
   */
  async index(ctx: Context) {
    // 查找组织
    const organ = await this.organRepository.findOne(ctx.token.aud);
    if (!organ) {
      ctx.body = { error: "用户已失效" };
      ctx.status = 401;
      return;
    }

    // 生成数据
    ctx.body = await this.toTree(organ.id);
    ctx.status = 200;
  }
}
