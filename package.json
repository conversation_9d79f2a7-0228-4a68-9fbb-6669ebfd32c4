{"name": "gps-back", "version": "1.5.1", "description": "gps back", "homepage": "", "license": "MIT", "author": "wadecha", "apidoc": {"title": "GPS接口文档", "url": "http://develop.350gps.com/apidoc"}, "main": "app.js", "private": true, "engines": {"node": ">=8.9.3"}, "repository": {"type": "git", "url": "*********************:5f3cf8fe5fd102f22f6bb432/gps-back.git"}, "scripts": {"start": "node app/index", "dev": "cross-env NODE_ENV=development nodemon", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "build": "tsc"}, "dependencies": {"@log4js-node/redis": "^1.0.0", "axios": "^0.20.0", "co": "^4.6.0", "co-request": "^1.0.0", "delay": "^4.4.0", "dotenv": "^8.2.0", "glob": "^7.1.4", "inversify": "^5.0.1", "ioredis": "^4.17.3", "jsonwebtoken": "^8.3.0", "koa": "^2.13.0", "koa-body": "^4.1.0", "koa-busboy": "^1.1.1", "koa-compose": "^4.1.0", "koa-compress": "^3.0.0", "koa-helmet": "^4.0.0", "koa-jwt": "^3.5.1", "koa-jwt-redis-session": "^0.0.30", "koa-request": "^1.0.0", "koa-router": "^7.4.0", "koa2-cors": "^2.0.6", "lodash": "^4.17.4", "log4js": "^3.0.6", "moment": "^2.29.1", "mongodb": "^2.2.25", "mongoskin": "^2.1.0", "mysql2": "^2.1.0", "node-async-mysql": "^1.1.0", "node-idgen": "^0.1.0", "node-pinyin": "^0.2.3", "node-schedule": "^1.3.2", "node-xlsx": "^0.10.0", "qiniu": "^7.2.0", "qr-image": "^3.2.0", "querystring": "^0.2.0", "read-pkg": "^5.2.0", "redis": "^3.0.2", "reflect-metadata": "^0.1.13", "socket.io-client": "^2.3.1", "sprintf-js": "^1.1.2", "superagent": "^3.5.1", "trouter": "^3.1.0", "typeorm": "0.2.26", "urlencode": "^1.1.0", "utility": "^1.11.0", "uuid": "^3.1.0", "validator": "^7.0.0"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/callsite": "^1.0.30", "@types/co": "^4.6.2", "@types/delay": "^3.1.0", "@types/inversify": "^2.0.33", "@types/ioredis": "^4.17.5", "@types/jsonwebtoken": "^8.5.0", "@types/koa": "^2.11.4", "@types/koa-compress": "^4.0.1", "@types/koa-helmet": "^5.2.0", "@types/koa-router": "^7.4.1", "@types/koa2-cors": "^2.0.1", "@types/mongodb": "^3.5.26", "@types/node": "^14.11.8", "@types/node-schedule": "^1.3.0", "@types/read-pkg": "^5.1.0", "@types/sprintf-js": "^1.1.2", "@types/trouter": "^3.1.0", "cross-env": "^7.0.2", "nodemon": "^2.0.4", "prettier": "^2.1.1", "ts-node": "^9.0.0", "typescript": "^4.0.2"}}