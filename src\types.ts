import { Context as BaseContext } from "koa";

export interface Token {
  iss: string;
  sub: string;
  aud: number;
  exp: number;
  nbf: number;
  iat: number;
}

export interface Context extends BaseContext {
  token: Token;
  param: {
    [key: string]: string;
  };
}

export interface ImeiCache {
  imei: number;
  encryted_imei: number;
  activeTime: number;
  status: number;
  statusTime: number;
  signalTime: number;
  id: number;

  // 速度
  speed: number;
  direct: number;
  signals: number;

  // 电源
  power: number;
  alarms: string;
  ACC: number;
  voltage: number;
  satellite: number;
  gsm: string;
  wifi: string;

  // 经度
  lon: number;

  // 纬度
  lat: number;

  locTime: number;
  locType: number;

  // 城市代码
  districtCode: number;

  // 地址
  address: string;

  provinceCodeUpdateTime: number;

  stopSaveTime: number;

  stopFlag: number;

  stopLon: number;

  stopLat: number;
  stopLocType: number;
  stopStartTime: number;
  stopEndTime: number;
  stopPeriod: number;
  stopLocId: number;
  rideStartTime: number;
  rideEndTime: number;
  rideMileage: number;
  mileage: number;
  monthMileage: number;
  yearMileage: number;
  mileageTime: number;
  mileageSaveTime: number;
}
