const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");
const basicBusiness = require("../business/basic_business");
const deviceBusiness = require("../business/device_business");

/**
 * @api {GET} /device/getdevicelist 设备管理-获取设备列表
 * @apiName getdevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-10
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配设备号、车牌号和车主
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {Number} onlineState 在线状态
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {Number} isSettle 是否结清
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
    ]
  }
 */
exports.getDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取设备列表",
  buttonPerm: "DC-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        imeis: (reqQueryParams.imeis || "").trim(),
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: reqQueryParams.hasSubOrgan === "true" ? 1 : 0,
        deviceType: validator.isIntFormat(reqQueryParams.deviceType, {
          min: -1,
          max: 2,
        })
          ? Number.parseInt(reqQueryParams.deviceType, 10)
          : -1,
        deviceModel: validator.isIntFormat(reqQueryParams.deviceModel, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.deviceModel, 10)
          : -1,
        onlineState: validator.isIntFormat(reqQueryParams.onlineState, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.onlineState, 10)
          : -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        isSettle: validator.isInt01(reqQueryParams.isSettle)
          ? Number.parseInt(reqQueryParams.isSettle, 10)
          : -1,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await deviceBusiness.getDeviceListLogic(queryParams, currentUser);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getDeviceList",
          "获取设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDeviceList",
        "获取设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getbigcardevicelist 资料管理-获取渣土设备列表
 * @apiName getbigcardevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-50
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配设备号、车牌号
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
  errcode: 0,
    errmsg: "操作成功",
  retobj: [
]
}
 */
exports.getBigCarDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "资料管理-获取渣土设备列表",
  buttonPerm: "DC-50",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: reqQueryParams.hasSubOrgan === "true" ? 1 : 0,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await deviceBusiness.getBigCarDeviceListLogic(
        queryParams
      );
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getBigCarDeviceList",
          "获取渣土设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getBigCarDeviceList",
        "获取渣土设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/exportdevicelist 设备管理-导出设备列表
 * @apiName exportdevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-15
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配设备号、车牌号和车主
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {Number} onlineState 在线状态
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {Number} isSettle 是否结清
 * @apiParam  {boolean} pagination=true 分页: 默认true开启, false关闭
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportDeviceList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-导出设备列表",
  buttonPerm: "DC-15",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        searchKey: (requestBody.searchKey || "").trim(),
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        deviceType: validator.isIntFormat(requestBody.deviceType, {
          min: -1,
          max: 2,
        })
          ? Number.parseInt(requestBody.deviceType, 10)
          : -1,
        deviceModel: validator.isIntFormat(requestBody.deviceModel, { min: 1 })
          ? Number.parseInt(requestBody.deviceModel, 10)
          : -1,
        onlineState: validator.isIntFormat(requestBody.onlineState, { min: 1 })
          ? Number.parseInt(requestBody.onlineState, 10)
          : -1,
        isSettle: validator.isInt01(requestBody.isSettle)
          ? Number.parseInt(requestBody.isSettle, 10)
          : -1,
        pagination: requestBody.pagination !== false || false,
        currentPage: requestBody.currentPage || 1,
        pageSize: requestBody.pageSize,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      const offlineList = await deviceBusiness.getDeviceListLogic(queryParams);

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "车架号",
        "发动机号",
        "车主",
        "车主手机号",
        "车品牌",
        "车型号",
        "贷款金额(万)",
        "车辆备注",
        "设备号",
        "设备名称",
        "设备类型",
        "设备型号",
        "安装位置",
        "安装人",
        "安装时间",
        "客户到期时间",
        "SIM卡号",
        "iccid",
        "服务密码",
        "sim卡到期日期",
        "sim卡状态",
        "当前省份",
        "设备状态",
        "最后在线时间",
        "设备备注",
        "关注度",
        "创建时间",
      ]);

      offlineList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.vinNo,
          e.engineNo,
          e.carOwner,
          e.ownerPhone,
          e.carBrand,
          e.carModel,
          e.loanAmount,
          e.carRemark,
          e.imei,
          e.deviceName,
          e.deviceTypeName,
          e.deviceModelName,
          e.installLocation,
          e.installer,
          e.installDate,
          e.expireTime,
          e.simNo,
          e.iccid,
          e.servicePswd,
          e.simExpireTime,
          e.simStateName,
          e.provinceName,
          e.onlineStateName,
          e.lastOnlineTime,
          e.deviceRemark,
          e.attentionName,
          e.createTime,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `终端列表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportDeviceList",
          "导出设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportDeviceList",
        "导出设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/insertdevice 设备管理-添加设备
 * @apiName insertdevice
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-11
 *
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {String} imei 设备号
 * @apiParam  {String} deviceName 设备名称
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {String} installLocation 安装位置
 * @apiParam  {String} installer 安装人
 * @apiParam  {String} installDate 安装日期
 * @apiParam  {String} expireTime 客户到期时间
 * @apiParam  {String} simNo sim卡号
 * @apiParam  {String} iccid iccid号
 * @apiParam  {String} servicePswd 服务密码
 * @apiParam  {String} deviceRemark 设备备注
 *
 * @apiParam  {String} plateNo 车牌号
 * @apiParam  {String} vinNo 车架号
 * @apiParam  {String} engineNo 发动机号
 * @apiParam  {String} policyNo 保单号
 * @apiParam  {String} policyExpDate 保单过期日期
 * @apiParam  {String} carBrand 车品牌
 * @apiParam  {String} carModel 车型号
 * @apiParam  {String} carOwner 车主
 * @apiParam  {String} ownerPhone 车主手机
 * @apiParam  {String} loanAmount 贷款金额
 * @apiParam  {String} carRemark 车备注
 * @apiParam  {String} patformExpirationTime 平台到期时间
 * @apiParam  {String} expireTime 客户到期时间
 *
 * @apiParam  {Number} isAlarmPhone 是否手机告警 1 开启，0 关闭(默认)
 * @apiParam  {Number} isAlarmSms 是否短信告警 1 开启，0 关闭(默认)
 * @apiParam  {Number} isAlarmWechat 是否微信告警 1 开启，0 关闭(默认)
 * @apiParam  {String} alarmPhone 告警手机号
 * @apiParam  {String} alarmSms 告警短信手机号
 * @apiParam  {String} alarmWechatName 告警微信名称
 * @apiParam  {String} alarmWechatId 告警微信标识
 * @apiParam  {String} alarmPhoneTypes 告警手机类型 空值=>所有告警类型
 * @apiParam  {String} alarmSmsTypes 告警短信类型 空值=>所有告警类型
 * @apiParam  {String} alarmWechaTypes 告警微信类型 空值=>所有告警类型
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-添加设备",
  buttonPerm: "DC-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.currentOrganId = currentUser.organId;
      requestData.isUpdateCar = true;
      requestData.plateNo = requestData.plateNo || "粤B00000";

      await deviceBusiness.insertDeviceLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertDevice",
        `添加设备成功，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertDevice",
          "添加设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertDevice",
        "添加设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/updatedevice 设备管理-更新设备
 * @apiName updatedevice
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-12
 *
 * @apiParam  {Number} deviceId 设备Id
 * @apiParam  {String} imei 设备号
 * @apiParam  {String} deviceName 设备名称
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {String} installLocation 安装位置
 * @apiParam  {String} installer 安装人
 * @apiParam  {String} installDate 安装日期
 * @apiParam  {String} expireTime 客户到期时间
 * @apiParam  {String} simNo sim卡号
 * @apiParam  {String} iccid iccid号
 * @apiParam  {String} servicePswd 服务密码
 * @apiParam  {String} deviceRemark 设备备注
 *
 * @apiParam  {String} plateNo 车牌号
 * @apiParam  {String} vinNo 车架号
 * @apiParam  {String} engineNo 发动机号
 * @apiParam  {String} policyNo 保单号
 * @apiParam  {String} policyExpDate 保单过期日期
 * @apiParam  {String} carBrand 车品牌
 * @apiParam  {String} carModel 车型号
 * @apiParam  {String} carOwner 车主
 * @apiParam  {String} ownerPhone 车主手机
 * @apiParam  {String} loanAmount 贷款金额
 * @apiParam  {String} carRemark 车备注
 * @apiParam  {String} patformExpirationTime 平台到期时间
 * @apiParam  {String} expireTime 客户到期时间
 *
 * @apiParam  {Number} isAlarmPhone 是否手机告警 1 开启，0 关闭(默认)
 * @apiParam  {Number} isAlarmSms 是否短信告警 1 开启，0 关闭(默认)
 * @apiParam  {Number} isAlarmWechat 是否微信告警 1 开启，0 关闭(默认)
 * @apiParam  {String} alarmPhone 告警手机号
 * @apiParam  {String} alarmSms 告警短信手机号
 * @apiParam  {String} alarmWechatName 告警微信名称
 * @apiParam  {String} alarmWechatId 告警微信标识
 * @apiParam  {String} alarmPhoneTypes 告警手机类型 空值=>所有告警类型
 * @apiParam  {String} alarmSmsTypes 告警短信类型 空值=>所有告警类型
 * @apiParam  {String} alarmWechaTypes 告警微信类型 空值=>所有告警类型
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-更新设备",
  buttonPerm: "DC-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.currentOrganId = currentUser.organId;
      requestData.isUpdateCar = true;
      requestData.plateNo = requestData.plateNo || "粤B00000";

      await deviceBusiness.updateDeviceLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateDevice",
        `更新设备成功，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateDevice",
          "更新设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateDevice",
        "更新设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/deletedevice 设备管理-删除设备
 * @apiName deletedevice
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-13
 *
 * @apiParam  {Number} deviceIds 设备Ids
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-删除设备",
  buttonPerm: "DC-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      const deleteCount = await deviceBusiness.deleteDeviceLogic(
        requestData,
        currentUser
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteDevice",
        `删除设备成功 ${deleteCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteDevice",
          "删除设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteDevice",
        "删除设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/updatedeviceorgan 设备管理-更改设备车组
 * @apiName updatedeviceorgan
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-14
 *
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Array} imeis 设备号
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateDeviceOrgan = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-更改设备车组",
  buttonPerm: "DC-14",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.currentOrganId = currentUser.organId;

      const updateResult = await deviceBusiness.updateDeviceOrganLogic(
        requestData,
        currentUser
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateDeviceOrgan",
        `更改设备车组成功 ${
          updateResult ? updateResult.update : "0"
        } 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateDeviceOrgan",
          "更改设备车组失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateDeviceOrgan",
        "更改设备车组出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getdevicedropdownlist 设备管理-获取设备下拉列表
 * @apiName getdevicedropdownlist
 * @apiGroup Device
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配设备号、车牌号和车主
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
    ]
  }
 */
exports.getDeviceDropdownList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "设备管理-获取设备下拉列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : -1,
        hasSubOrgan: reqQueryParams.hasSubOrgan === "true" ? 1 : 0,
      };

      const deviceList = await deviceBusiness.getDeviceDropdownListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getDeviceDropdownList",
          "获取设备下拉列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDeviceDropdownList",
        "获取设备下拉列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getofflinedevicelist 设备管理-获取离线设备列表
 * @apiName getofflinedevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-16
 *
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} startSecond 开始秒
 * @apiParam  {Number} endSecond 结束秒，如果endSecond为N，传入为0
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getOfflineDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取离线设备列表",
  buttonPerm: "DC-16",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(currentUser.organId, { min: 1 })
          ? currentUser.organId
          : -1,
        deviceType: validator.isIntFormat(reqQueryParams.deviceType, {
          min: -1,
          max: 2,
        })
          ? Number.parseInt(reqQueryParams.deviceType, 10)
          : -1,
        startSecond: reqQueryParams.startSecond,
        endSecond: reqQueryParams.endSecond,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await deviceBusiness.getOfflineDeviceListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getOfflineDeviceList",
          "获取离线设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOfflineDeviceList",
        "获取离线设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/exportofflinedevicelist 设备管理-导出离线设备列表
 * @apiName exportofflinedevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-17
 *
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} startSecond 开始秒
 * @apiParam  {Number} endSecond 结束秒，如果endSecond为N，传入为0
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportOfflineDeviceList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-导出离线设备列表",
  buttonPerm: "DC-17",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(currentUser.organId, { min: 1 })
          ? currentUser.organId
          : -1,
        deviceType: validator.isIntFormat(requestBody.deviceType, {
          min: -1,
          max: 2,
        })
          ? Number.parseInt(requestBody.deviceType, 10)
          : -1,
        startSecond: requestBody.startSecond,
        endSecond: requestBody.endSecond,
        pagination: false,
      };

      const offlineList = await deviceBusiness.getOfflineDeviceListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "设备I号",
        "设备类型",
        "设备型号",
        "SIM卡号",
        "当前地址",
        "最后在线时间",
        "离线时长",
        "关注度",
        "关注度备注",
      ]);

      offlineList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.imei,
          e.deviceTypeName,
          e.deviceModelName,
          e.simNo,
          e.currentAddress,
          e.lastOnlineTime,
          e.offlineLong,
          e.attentionName,
          e.attentionRemark,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `离线统计 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportOfflineDeviceList",
          "导出离线设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportOfflineDeviceList",
        "导出离线设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getsamecardevicelist 设备管理-获取同车设备报表
 * @apiName getsamecardevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-20
 *
 * @apiParam  {String} plateNo 车牌号
 * @apiParam  {String} imei 设备号
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
    ]
  }
 */
exports.getSameCarDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取同车设备报表",
  buttonPerm: "DC-20",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(currentUser.organId, { min: 1 })
          ? currentUser.organId
          : -1,
        plateNo: reqQueryParams.plateNo || "",
        imei: reqQueryParams.imei || "",
      };

      const deviceList = await deviceBusiness.getSameCarDeviceListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getSameCarDeviceList",
          "获取同车设备报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getSameCarDeviceList",
        "获取同车设备报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/updatedeviceattention 设备管理-更新设备关注度
 * @apiName updatedeviceattention
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-21
 *
 * @apiParam  {String} imei 设备号 必需值
 * @apiParam  {String} plateNo 车牌号
 * @apiParam  {Number} attention 关注度 必需值
 * @apiParam  {String} attentionRemark 关注备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateDeviceAttention = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-更新设备关注度",
  buttonPerm: "DC-21",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.organId = validator.isIntFormat(currentUser.organId, {
        min: 1,
      })
        ? currentUser.organId
        : -1;

      const updateCount = await deviceBusiness.updateDeviceAttentionLogic(
        requestData
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateDeviceAttention",
        `更新设备关注度成功 ${updateCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateDeviceAttention",
          "更新设备关注度失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateDeviceAttention",
        "更新设备关注度出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getcarsettlelist 设备管理-获取结清车辆列表
 * @apiName getcarsettlelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-25
 *
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {String} imei 设备号
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getCarSettleList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取结清车辆列表",
  buttonPerm: "DC-24",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        imei: reqQueryParams.imei || "",
        hasSubOrgan: reqQueryParams.hasSubOrgan === "true" ? 1 : 0,
        isSettle: 1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await deviceBusiness.getCarSettleListLogic(
        queryParams,
        currentUser
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getCarSettleList",
          "获取结清车辆列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getCarSettleList",
        "获取结清车辆列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/updatecarsettle 设备管理-更新车辆结清状态
 * @apiName updatecarsettle
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-22
 *
 * @apiParam  {String} plateNo 车牌号
 * @apiParam  {Number} isSettle 是否结清 1 结清，0 撤消 必需值
 * @apiParam  {String} settleRemark 结清备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateCarSettle = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-更新车辆结清状态",
  buttonPerm: "DC-22",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.organId = validator.isIntFormat(currentUser.organId, {
        min: 1,
      })
        ? currentUser.organId
        : -1;

      await deviceBusiness.updateCarSettleLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateCarSettle",
        `更新车辆结清状态成功，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateCarSettle",
          "更新车辆结清状态失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateCarSettle",
        "更新车辆结清状态出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getattentiondevicelist 设备管理-获取关注设备列表
 * @apiName getattentiondevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-40
 *
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {}
  }
 */
exports.getAttentionDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取关注设备列表",
  buttonPerm: "DC-40",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: currentUser.organId,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await deviceBusiness.getAttentionDeviceListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getAttentionDeviceList",
          "获取关注设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getAttentionDeviceList",
        "获取关注设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/exportattentiondevicelist 设备管理-导出关注设备列表
 * @apiName exportattentiondevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-41
 *
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportAttentionDeviceList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-导出关注设备列表",
  buttonPerm: "DC-41",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        organId: currentUser.organId,
        pagination: false,
      };

      const deviceList = await deviceBusiness.getAttentionDeviceListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "车架号",
        "车主",
        "车辆备注",
        "设备号",
        "设备类型",
        "设备型号",
        "关注度",
        "关注度备注",
        "速度km/h",
        "最后上线时间",
        "定位",
        "设备状态",
        "离线时长",
        "报警类型",
      ]);

      deviceList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.vinNo,
          e.carOwner,
          e.carRemark,
          e.imei,
          e.deviceTypeName,
          e.deviceModelName,
          e.attentionName,
          e.attentionRemark,
          e.speed,
          e.lastOnlineTime,
          e.locationTypeName,
          e.onlineStateName,
          e.offlineLong,
          e.alarmTypeName,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `重点关注 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportAttentionDeviceList",
          "导出关注设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportAttentionDeviceList",
        "导出关注设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getorgandevicelist 设备管理-获取组织设备列表
 * @apiName getorgandevicelist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-18
 *
 * @apiParam  {String} searchKey 搜索
 * @apiParam  {Number} deviceState 在线状态
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} whiteUserId 白名单用户ID
 * @apiParam  {Number} hasSubOrgan 包含子组
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {}
  }
 */
exports.getOrganDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取组织设备列表",
  buttonPerm: "DC-18",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        imeis: (reqQueryParams.imeis || "").trim(),
        organId: reqQueryParams.organId,
        hasSubOrgan: reqQueryParams.hasSubOrgan,
        deviceState: reqQueryParams.deviceState,
        currentOrganId: currentUser.organId,
        whiteUserId: reqQueryParams.whiteUserId,
        noticeUserId: reqQueryParams.noticeUserId || 0,
        noticeType: reqQueryParams.noticeType || '',
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 300,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const [
        deviceList,
        statistics,
      ] = await deviceBusiness.getOrganDeviceListLogic(queryParams, currentUser);

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE, {
        deviceList,
        statistics,
      });
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getOrganDeviceList",
          "获取组织设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOrganDeviceList",
        "获取组织设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getdetails 设备管理-获取设备详情
 * @apiName getdetails
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-18
 *
 * @apiParam  {String} imei 设备imei
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {}
  }
 */
exports.getDetails = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "设备管理-获取设备详情",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        imei: reqQueryParams.imei,
        organId: currentUser.organId,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const device = await deviceBusiness.getDeviceDataLogic(queryParams, currentUser);

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE, {
        device,
      });
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getDetails",
          "获取设备详情失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDetails",
        "获取设备详情出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getdeviceinfodropdownlist 设备管理-获取设备信息下拉列表
 * @apiName getdeviceinfodropdownlist
 * @apiGroup Device
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配设备号、车牌号和车主
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
    ]
  }
 */
exports.getDeviceInfoDropdownList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "设备管理-获取设备信息下拉列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: currentUser.organId,
      };

      const deviceList = await deviceBusiness.getDeviceInfoListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getDeviceInfoDropdownList",
          "获取设备信息下拉列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDeviceInfoDropdownList",
        "获取设备信息下拉列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getdevicestatecount 设备管理-获取设备在线状态统计
 * @apiName getdevicestatecount
 * @apiGroup Device
 * @apiVersion  1.0.0
 *
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {}
  }
 */
exports.getDeviceStateCount = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "设备管理-获取设备统计信息",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        organId: currentUser.organId,
      };

      const deviceData = await deviceBusiness.getDeviceStateCountLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceData
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getDeviceStateCount",
          "获取设备在线状态统计失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDeviceStateCount",
        "获取设备在线状态统计出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /device/getdeviceimportlist 设备管理-获取导入设备列表
 * @apiName getdeviceimportlist
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-30
 *
 * @apiParam  {Number} isValidate 验证成功或失败，0 成功，1 失败
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getDeviceImportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-获取导入设备列表",
  buttonPerm: "DC-30",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        userName: currentUser.loginName,
        isValidate: validator.isInt01(reqQueryParams.isValidate)
          ? reqQueryParams.isValidate
          : 1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const deviceList = await deviceBusiness.getDeviceImportListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        deviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getDeviceImportList",
          "获取导入设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getDeviceImportList",
        "获取导入设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/insertdeviceimport 设备管理-导入设备列表
 * @apiName insertdeviceimport
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-31
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertDeviceImport = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-导入设备列表",
  buttonPerm: "DC-31",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      let xlsxData = await basicBusiness.getUploadXlsxDataLogic(ctx);
      xlsxData = xlsxData[0].data;
      xlsxData.shift();
      if (xlsxData.length > 1000) {
        ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_SUCCESS_MESSAGE);
        return;
      }

      const deviceList = [];
      const keys = `imei,deviceName,deviceModelName,plateNo,simNo,servicePswd,iccid,installLocation,installer,installDate,deviceRemark,
      isUpdateCar,vinNo,engineNo,carOwner,ownerPhone,carBrand,carModel,loanAmount,carRemark,policyNo,policyExpDate`;
      for (const data of xlsxData) {
        deviceList.push(commFunc.excelArrayToObject(keys, data));
      }

      await deviceBusiness.insertDeviceImportLogic(deviceList, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertDeviceImport",
        `导入设备列表成功，数量：`,
        deviceList.length
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertDeviceImport",
          "导入设备列表失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertDeviceImport",
        "导入设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/updatedeviceimport 设备管理-更新导入设备
 * @apiName updatedeviceimport
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-32
 *
 * @apiParam  {Number} deviceImportId 设备导入Id
 * @apiParam  {String} imei 设备号
 * @apiParam  {String} deviceName 设备名称
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {String} simNo sim卡号
 * @apiParam  {String} plateNo 车牌号
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateDeviceImport = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-更新导入设备",
  buttonPerm: "DC-32",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      await deviceBusiness.updateDeviceImportLogic(requestData, currentUser);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateDeviceImport",
        `更新导入设备成功，内容：`,
        requestData
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateDeviceImport",
          "更新导入设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateDeviceImport",
        "更新导入设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /device/finishimportdevice 设备管理-完成导入设备
 * @apiName finishimportdevice
 * @apiGroup Device
 * @apiVersion  1.0.0
 * @apiPermission DC-33
 *
 * @apiParam  {Number} organId 机构Id
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.finishImportDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "设备管理-完成导入设备",
  buttonPerm: "DC-33",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.currentOrganId = currentUser.organId;

      const result = deviceBusiness.finishImportDeviceLogic(
        requestData,
        currentUser
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "finishImportDevice",
        `完成导入设备成功，内容：`,
        requestData
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "finishImportDevice",
          "完成导入设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "finishImportDevice",
        "完成导入设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
