const utility = require("utility");
const jwt = require("jsonwebtoken");

const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const staticConfig = require("../config/config_static");
const validator = require("../lib/common/validator_extend");

const accountBusiness = require("../business/account_business");
const userBusiness = require("../business/uc_user_business");

/**
 *
 * @api {POST} /signin 用户登录
 * @apiName signin
 * @apiGroup Account
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} userName 登录名或工号
 * @apiParam  {String} password 密码
 *
 * @apiParamExample  {Object} 请求示例:
 {
   userName : admin,
   password : 12345
 }
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
   errcode: 0,
   errmsg: "操作成功",
   retobj: {"userName":"admin","realname":"超级管理员"}
 }
 *
 */
exports.signIn = {
  method: "POST",
  middlewares: [],
  routeDesc: "用户登录",
  handler: async (ctx) => {
    const userName = ctx.request.body.userName;
    const password = ctx.request.body.password;

    try {
      if (
        !(
          validator.isLength(`${userName}`, 2) &&
          validator.isLength(`${password}`, 2)
        )
      ) {
        ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SIGN_IN_INPUT_ERROR);
        return;
      }

      global.context.logger.info(
        userName,
        "USER",
        "signIn",
        "用户使用浏览器登录情况:",
        ctx.headers["user-agent"]
      );

      const signInResult = await accountBusiness.checkSignInAccountLogic({
        userName,
        password,
      });

      global.context.logger.info(
        userName,
        "USER",
        "signIn",
        "用户登录成功:",
        signInResult
      );

      const authToken = jwt.sign(
        JSON.parse(JSON.stringify(signInResult)),
        staticConfig.jwtSecret,
        { expiresIn: "2d" }
      );
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        authToken
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          userName,
          "USER",
          "signIn",
          "用户登录失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        userName,
        "MYSQL",
        "signIn",
        "用户登录错误：",
        err ? err.message || err : undefined
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 *
 * @api {GET} /getuserlogindata 获取用户登录信息
 * @apiName getuserlogindata
 * @apiGroup Account
 * @apiVersion  1.0.0
 *
 * @apiSuccess (Success 200) {Number} errcode 错误代码：0：成功，1：失败
 * @apiSuccess (Success 200) {String} errmsg 错误消息
 * @apiSuccess (Success 200) {Object} retobj 返回对象
 *
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
   errcode: 0,
   errmsg: "操作成功",
   retobj: {
   }
 }
 *
 */
exports.getUserLoginData = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "获取当前登录的用户信息",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    ctx.body = new RetJson(
      i18n.SYS_SUCCESS_CODE,
      i18n.SYS_SUCCESS_MESSAGE,
      currentUser
    );
  },
};

/**
 * @api {GET} /signout 退出登录 BUG REDIS存储
 * @apiName signout
 * @apiGroup Account
 * @apiVersion  1.0.0
 */
exports.signout = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "退出登录",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;

    global.context.logger.info(
      currentUser.loginName,
      "USER",
      "signout",
      "用户退出登录",
      ""
    );

    // req.session.destroy();
    // res.clearCookie(staticConfig.cookieSecret, { path: '/' });
    ctx.body = "您已退出系统，请重新登录!";
  },
};

/**
 *
 * @api {POST} /updateuserpassword 用户修改密码
 * @apiName updateuserpassword
 * @apiGroup Account
 * @apiVersion  1.0.0
 *
 * @apiParam  {string} userName 登录用户名
 * @apiParam  {string} oldPswd 旧密码
 * @apiParam  {string} newPswd 新密码
 *
 * @apiParamExample  {json} 请求示例:
 {
   userName: "登录用户名",
   oldPswd: "旧密码，md5加密后的密码",
   newPswd: "新密码"
 }
 *
 * @apiSuccessExample {json} 响应示例:
 {
   property : {"errcode":0,"errmsg":"操作成功"}
 }
 *
 */
exports.updateUserPassword = {
  method: "POST",
  middlewares: ["loginRequired"],
  routeDesc: "用户修改密码",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const updateData = ctx.request.body;

      if (
        !(
          validator.isLength(`${updateData.userName}`, 2) &&
          validator.isLength(`${updateData.oldPswd}`, 2) &&
          validator.isLength(`${updateData.newPswd}`, 2)
        )
      ) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_DATA_ERROR
        );
        return;
      }

      updateData.oldPswd = utility.md5(updateData.oldPswd);
      await userBusiness.updateUserPasswordLogic(updateData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateUserPassword",
        "用户修改密码成功",
        ""
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateUserPassword",
          "用户修改密码失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateUserPassword",
        "用户修改密码错误：",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
