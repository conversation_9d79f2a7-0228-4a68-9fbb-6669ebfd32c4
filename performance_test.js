/**
 * GPS 设备列表接口性能测试脚本
 * 用于测试 /device/getorgandevicelist 接口的优化效果
 */

const axios = require('axios');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:3000', // 根据实际环境调整
  endpoint: '/device/getorgandevicelist',
  // 测试用例
  testCases: [
    {
      name: '基础查询',
      params: {
        organId: 1,
        hasSubOrgan: true,
        pageSize: 20,
        currentPage: 1
      }
    },
    {
      name: '大数据量查询',
      params: {
        organId: 1,
        hasSubOrgan: true,
        pageSize: 300,
        currentPage: 1
      }
    },
    {
      name: '搜索查询',
      params: {
        organId: 1,
        hasSubOrgan: true,
        searchKey: '测试',
        pageSize: 50,
        currentPage: 1
      }
    },
    {
      name: '状态过滤查询',
      params: {
        organId: 1,
        hasSubOrgan: true,
        deviceState: 'ONLINE_STATE.ONLINE',
        pageSize: 100,
        currentPage: 1
      }
    }
  ],
  // 每个测试用例执行次数
  iterations: 5,
  // 期望的最大响应时间（毫秒）
  maxResponseTime: 2000
};

/**
 * 执行单次请求测试
 */
async function performSingleTest(testCase, headers) {
  const startTime = Date.now();
  
  try {
    const response = await axios.get(TEST_CONFIG.baseURL + TEST_CONFIG.endpoint, {
      params: testCase.params,
      headers: headers,
      timeout: 10000 // 10秒超时
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: true,
      responseTime,
      dataCount: response.data.retobj?.deviceList?.total || 0,
      status: response.status
    };
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: false,
      responseTime,
      error: error.message,
      status: error.response?.status || 'TIMEOUT'
    };
  }
}

/**
 * 执行性能测试
 */
async function runPerformanceTest() {
  console.log('🚀 开始执行 GPS 设备列表接口性能测试...\n');
  
  // 这里需要根据实际情况设置认证头
  const headers = {
    'Content-Type': 'application/json',
    // 'Authorization': 'Bearer your-token-here',
    // 'Cookie': 'session=your-session-here'
  };
  
  const results = [];
  
  for (const testCase of TEST_CONFIG.testCases) {
    console.log(`📊 测试用例: ${testCase.name}`);
    console.log(`   参数: ${JSON.stringify(testCase.params)}`);
    
    const caseResults = [];
    
    for (let i = 0; i < TEST_CONFIG.iterations; i++) {
      console.log(`   执行第 ${i + 1} 次测试...`);
      const result = await performSingleTest(testCase, headers);
      caseResults.push(result);
      
      if (result.success) {
        console.log(`   ✅ 响应时间: ${result.responseTime}ms, 数据量: ${result.dataCount}`);
      } else {
        console.log(`   ❌ 失败: ${result.error}, 响应时间: ${result.responseTime}ms`);
      }
      
      // 请求间隔 100ms
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 计算统计信息
    const successfulTests = caseResults.filter(r => r.success);
    const responseTimes = successfulTests.map(r => r.responseTime);
    
    const stats = {
      testCase: testCase.name,
      totalTests: TEST_CONFIG.iterations,
      successfulTests: successfulTests.length,
      failedTests: caseResults.length - successfulTests.length,
      avgResponseTime: responseTimes.length > 0 ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0,
      minResponseTime: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
      maxResponseTime: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
      successRate: Math.round((successfulTests.length / TEST_CONFIG.iterations) * 100),
      meetsSLA: responseTimes.length > 0 ? responseTimes.every(t => t <= TEST_CONFIG.maxResponseTime) : false
    };
    
    results.push(stats);
    
    console.log(`   📈 统计结果:`);
    console.log(`      成功率: ${stats.successRate}%`);
    console.log(`      平均响应时间: ${stats.avgResponseTime}ms`);
    console.log(`      最小响应时间: ${stats.minResponseTime}ms`);
    console.log(`      最大响应时间: ${stats.maxResponseTime}ms`);
    console.log(`      是否满足 SLA (≤${TEST_CONFIG.maxResponseTime}ms): ${stats.meetsSLA ? '✅' : '❌'}`);
    console.log('');
  }
  
  // 生成测试报告
  generateReport(results);
  
  return results;
}

/**
 * 生成测试报告
 */
function generateReport(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = `performance_report_${timestamp}.json`;
  
  const report = {
    timestamp: new Date().toISOString(),
    testConfig: TEST_CONFIG,
    results: results,
    summary: {
      totalTestCases: results.length,
      overallSuccessRate: Math.round(results.reduce((sum, r) => sum + r.successRate, 0) / results.length),
      casesPassingSLA: results.filter(r => r.meetsSLA).length,
      avgResponseTimeAcrossAllCases: Math.round(results.reduce((sum, r) => sum + r.avgResponseTime, 0) / results.length)
    }
  };
  
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  console.log('📋 测试报告已生成:');
  console.log(`   文件: ${reportFile}`);
  console.log(`   总体成功率: ${report.summary.overallSuccessRate}%`);
  console.log(`   满足 SLA 的测试用例: ${report.summary.casesPassingSLA}/${report.summary.totalTestCases}`);
  console.log(`   平均响应时间: ${report.summary.avgResponseTimeAcrossAllCases}ms`);
}

// 执行测试
if (require.main === module) {
  runPerformanceTest()
    .then(() => {
      console.log('✅ 性能测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 性能测试失败:', error);
      process.exit(1);
    });
}

module.exports = { runPerformanceTest };
