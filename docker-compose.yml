version: "2"
services:
  mysql:
    image: mysql:5.7
    ports:
      - 3306:3306
    volumes:
      - ./storage/mysql:/var/lib/mysql:rw
      - ./config/mysql.cnf:/etc/mysql/conf.d/mysql.cnf
    environment:
      MYSQL_DATABASE: "gps_monitor"
      MYSQL_ROOT_PASSWORD: "123456"
  redis:
    image: redis
    ports:
      - 6379:6379
    volumes:
      - ./storage/redis:/type:rw
  mongo:
    image: mongo
    tty: true
    ports:
      - 27017:27017
    volumes:
      - ./storage/mongo:/data
      - ./config/mongo.js:/docker-entrypoint-initdb.d/init.js
    environment:
      MONGO_INITDB_ROOT_USERNAME: "root"
      MONGO_INITDB_ROOT_PASSWORD: "123456"
      MONGO_INITDB_DATABASE: "gps_monitor"
