const mysqlDB = require("../common/mysql_pool");

function getPropertymapSqlList(queryParams) {
  const sql = `select id,name,parent_id,code,
  \`key\`,val,sort,status,remark,create_user,create_time,update_user,update_time
  from bc_property_map
  where (:status='' or status=:status) and (:code='' or code like concat('%',:code,'%'))
  order by code,sort,\`key\``;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function insertPropertymapSql(insertData) {
  const sql = `insert into bc_property_map
  (name,parent_id,code,\`key\`,val,sort,status,remark,create_user,create_time)
  values
  (:name,:parentId,:code,:key,:val,:sort,:status,:remark,:userName,now())`;

  return mysqlDB.insert(sql, insertData);
}

function updatePropertymapSql(updateData) {
  const sql = `update bc_property_map
  set name=:name,parent_id=:parentId,code=:code,\`key\`=:key,val=:val,
  sort=:sort,status=:status,remark=:remark,update_user=:userName,update_time=now()
  where id=:id`;

  return mysqlDB.update(sql, updateData);
}

function getPropertymapRecordSql(queryParams) {
  const sql = `select count(*) record,id
  from bc_property_map
  where code=:code and \`key\`=:key`;

  return mysqlDB.one(sql, queryParams);
}

module.exports = {
  getPropertymapSqlList,
  insertPropertymapSql,
  updatePropertymapSql,
  getPropertymapRecordSql,
};
