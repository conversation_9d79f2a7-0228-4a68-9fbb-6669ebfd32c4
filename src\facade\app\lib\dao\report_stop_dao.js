const mysqlDB = require("../common/mysql_pool");
const {getDaoShard} = require("../utils/sharding");

function getStopReportSqlList(queryParams, currentUser) {
  currentUser = currentUser || {}
  const ucUserDeviceTable = getDaoShard('uc_user_device', currentUser.userId);
  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.plate_no,gd.attention,
  gd.imei,gd.device_name,gd.device_type,gd.device_model,
  gst.stop_start_time,gst.stop_second,gst.stop_address
  from gps_stop gst
  join gps_device gd on gst.imei=gd.imei and gd.is_settle=0 and gd.is_delete=0
  ${queryParams.deviceType === -1 ? "" : " and gd.device_type=:device_type"}
  ${queryParams.deviceModel === -1 ? "" : " and gd.device_model=:device_model"}
  join uc_organ uo on gd.organ_id=uo.id ${
    queryParams.organId === -1 ? "" : organSql
  }
  ${
    // 子账户需要连表, 白名单内的的设备才能被查看
    currentUser.type === 0 ? `join ${ucUserDeviceTable} uud on gd.imei=uud.imei and uud.user_id=${currentUser.userId}` : ""
  }
  where gst.stop_second >= :stop_second ${
    queryParams.startSecond === "" ? "" : " and gst.stop_second < :start_second"
  }
  ${queryParams.imei === "" ? "" : " and gst.imei=:imei"}
  order by gst.stop_start_time desc,gst.imei desc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getHomeStopReportSqlList(queryParams) {
  const sql = `select uo.organ_name,gd.plate_no,gc.vin_no,gc.car_owner,gc.remark car_remark,
  gd.imei,gd.device_name,gd.device_type,gd.device_model,gd.attention,gd.attention_remark,
  gst.stop_start_time,gst.stop_second,gst.stop_lat lat,gst.stop_lon lon,gst.stop_address
  from (SELECT * FROM gps_stop group by imei order by create_time desc) as gst
  join gps_device gd on gst.imei=gd.imei and gd.is_settle=0 and gd.is_delete=0
  join gps_car gc on gd.plate_no=gc.plate_no
  join uc_organ uo on gd.organ_id=uo.id and uo.path like concat('%,',:organ_id,',%')
  where gst.stop_second >= :start_second and gst.stop_second < :end_second
  order by gst.stop_start_time desc,gd.plate_no`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

module.exports = {
  getStopReportSqlList,
  getHomeStopReportSqlList,
};
