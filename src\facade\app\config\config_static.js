/**
 * Created by Administrator on 2017/3/24.
 */
const { env } = require("process");
const { join } = require("path");
const dotenv = require("dotenv");

// 环境载入
if (env.NODE_ENV === "development") {
  dotenv.config({ path: join(__dirname, "../../../../.env") });
}

module.exports = {
  jwtSecret: env.JWT_SECRET,
  redisPrename: {
    permission: "BQPERMISSION",
    propertymap: "BQPROPERTYMAP",
  },
  monitorHost: {
    GPSMONITORAPP: "http://127.0.0.1:6560/api/",
  },
  mysqlConfig: {
    host: env.MYSQL_HOST || "mysql",
    user: env.MYSQL_USER || "root",
    password: env.MYSQL_PASSWORD || "123456",
    database: env.MYSQL_DATABASE || "gps_monitor",
    port: env.MYSQL_PORT || 3306,
    queueLimit: env.MYSQL_QUEUE_LIMIT || 1024,
    debug: Number(env.MYSQL_DEBUG || 0),
  },
  mysqlSharding:{
    uc_user_device: env.MYSQL_SHARDING_UUD || 0
  },
  mongoConfig: {
    // connstr: '*********************************************************',
    connstr: [
      "mongodb://",
      env.MONGO_USER || "monitor",
      ":",
      env.MONGO_PASSWORD || "123456",
      "@",
      env.MONGO_HOST || "mongo",
      ":",
      env.MONGO_PORT || 27017,
      "/",
      env.MONGO_DATABASE || "gps_monitor",
    ].join(""),
    options: {
      authSource: "admin",
      reconnectTries: 1800,
      reconnectInterval: 500,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 60000,
      autoReconnect: true,
      poolSize: 10,
    },
  },
  redisConfig: {
    host: env.REDIS_HOST || "redis",
    port: env.REDIS_PORT || 6379,
    pass: env.REDIS_PASS || "",
    db: env.REDIS_DB || 0,
  },
  oldRedisConfig: {
    host: env.OLD_REDIS_HOST || "redis",
    port: env.OLD_REDIS_PORT || 6379,
    pass: env.OLD_REDIS_PASS || "",
    db: env.OLD_REDIS_DB || 0,
  },
  socketConfig: {
    wsHost: "ws://www.350gps.com:7086",
  },
  thirdPartyConfig: {
    wxDomain: env.WXDOMAIN || "http://php-policy.application-prod",
    // wxDomain: env.WXDOMAIN || "http://wx.350gps.cn",
  },
  loggerConfig: {
    categories: {
      default: {
        appenders: [
          env.LOGGER_APPENDERS || "redis"
        ],
        level: "info",
      },
    },
    appenders: {
      console: {
        type: "console",
      },
      redis: {
        type: "@log4js-node/redis",
        host: env.REDIS_HOST || "redis",
        port: env.REDIS_PORT || 6379,
        pass: env.REDIS_PASS || "",
        channel: "topic_log",
        category: "redis",
        layout: {
          type: "pattern",
          pattern: "%d{yyyy-MM-dd hh:mm:ss:SSS}#%p#%z#%m",
        },
      },
    },
  },
  // 七牛云存储
  qiniu: {
    accessKey: "",
    secretKey: "",
    bucketName: "",
    qiniuDomain: "",
  },
  // 半自动发布项目
  // 部署到测试服务器上
  publishDev: {
    remotePath: "/data/pro-upload/",
    host: "",
    user: "root",
    pass: "",
    port: 22,
  },
  // 部署正式服务器上
  publishProd: {
    remotePath: "/data/upload-formal/",
    host: "",
    user: "root",
    pass: "",
    port: 22,
  },
};
