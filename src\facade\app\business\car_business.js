const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");

const carDao = require("../lib/dao/car_dao");

/**
 * 获取车辆列表
 * 请求参数:
 "organId": "机构Id",
 "searchKey": "搜索关键字"
 *
 */
async function getCarListLogic(queryParams) {
  const carList = await carDao.getCarSqlList(queryParams);

  carList.rows.forEach((e, i) => {
    commFunc.formatNullToString(e);
    e.index = queryParams.offset + i + 1;
    e.isSettleName = e.isSettle === 1 ? "是" : "";
    e.policyExpDate = e.policyExpDate
      ? moment.unix(e.policyExpDate).format("YYYY-MM-DD")
      : "";
    e.settleTime = e.settleTime
      ? moment.unix(e.settleTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return carList;
}

/**
 * 获取车辆下拉列表
 * 请求参数:
 "searchKey": "搜索关键词",
 "organId": "机构Id"
 *
 */
async function getCarDropdownLogic(queryParams) {
  const carList = await carDao.getCarDropdownSqlList(queryParams);

  carList.forEach((e) => {
    commFunc.formatNullToString(e);
    e.policyExpDate = e.policyExpDate
      ? moment.unix(e.policyExpDate).format("YYYY-MM-DD")
      : "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return carList;
}

/**
 * 获取车辆数据
 * 请求参数:
 "plateNo": "车牌号",
 "organId": "机构Id"
 *
 */
async function getCarDataLogic(queryParams) {
  const carData = await carDao.getCarSqlData(queryParams);
  if (!carData) {
    return null;
  }
  commFunc.formatNullToString(carData);
  carData.policyExpDate = carData.policyExpDate
    ? moment.unix(carData.policyExpDate).format("YYYY-MM-DD")
    : "";
  carData.createTime = carData.createTime
    ? moment(carData.createTime).format("YYYY-MM-DD HH:mm")
    : "";
  return carData;
}

/**
 * 车辆数据验证
 *
 */
function _validateCarRequest(requestData) {
  const rules = [
    {
      field: "carId",
      title: "车辆Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    },
    {
      field: "plateNo",
      title: "车牌号",
      rule: "isCarPlateNo",
      msg: "",
      required: true,
      opt: "i",
    },
    { field: "ownerPhone", title: "车主手机", rule: "isInt11", msg: "" },
    {
      field: "organId",
      title: "所属车组",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "i",
    },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  commFunc.formatNullToString(model);
  model.policyExpDate = model.policyExpDate
    ? moment(model.policyExpDate).format("X")
    : 0;

  return [undefined, model];
}

/**
 * 新增车辆列表
 * 请求参数:
 "carId": "车辆Id",
 "organId": "机构Id",
 "plateNo": "车牌号",
 "vinNo": "车架号",
 "engineNo": "发动机号",
 "policyNo": "保单号",
 "policyExpDate": "保单过期日期",
 "carBrand": "车品牌",
 "carModel": "车型号",
 "carOwner": "车主",
 "ownerPhone": "车主手机",
 "remark": "备注",
 "userName": "操作人"
 *
 */
async function insertCarLogic(insertData, currentUser) {
  insertData.opt = "i";
  const [err, carModel] = _validateCarRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await carDao.getCarRecordsSql(carModel);
  if (isExists.record) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    plateNo: carModel.plateNo,
    operationType: "新增车辆",
    content: `新增车辆 ${carModel.plateNo} `,
  };
  return await carDao.insertCarSql(carModel, operationModel);
}

/**
 * 更新车辆列表
 * 请求参数:
 "carId": "车辆Id",
 "vinNo": "车架号",
 "engineNo": "发动机号",
 "policyNo": "保单号",
 "policyExpDate": "保单过期日期",
 "carBrand": "车品牌",
 "carModel": "车型号",
 "carOwner": "车主",
 "ownerPhone": "车主手机",
 "remark": "备注",
 "userName": "操作人"
 *
 */
async function updateCarLogic(updateData, currentUser) {
  updateData.opt = "u";
  const [err, carModel] = _validateCarRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await carDao.getCarRecordsSql(carModel);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_OVERTWO_EXISTS);
  }

  if (
    Number.parseInt(carModel.carId, 10) !== isExists.carId &&
    isExists.record
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    plateNo: carModel.plateNo,
    operationType: "编辑车辆",
    content: `编辑车辆 ${carModel.plateNo} `,
  };
  return await carDao.updateCarSql(carModel, operationModel);
}

/**
 * 删除车辆
 * 请求参数:
 "carId": "车辆Id"
 *
 */
async function deleteCarLogic(deleteData, currentUser) {
  if (!validator.isIntFormat(deleteData.carId, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  const carData = await carDao.getCarInfoStateSql(deleteData);

  if (carData && carData.deviceCount > 0) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.BEFORE_DEL_CAR_DEL);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "删除车辆",
    content: `删除车辆 ${deleteData.carId} `,
  };
  return await carDao.deleteCarSql(deleteData, operationModel);
}

module.exports = {
  getCarListLogic,
  getCarDataLogic,
  getCarDropdownLogic,
  insertCarLogic,
  updateCarLogic,
  deleteCarLogic,
};
