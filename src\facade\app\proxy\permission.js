const permissionBusiness = require("../business/uc_permission_business");

class Permission {
  constructor() {
    this.id = 0;
    this.perId = 0;
    this.perName = "";
    this.path = "";
    this.parentId = 0;
    this.path = "";
    this.isCanDelete = 0;
    this.children = [];
    this.selected = false;
    this.indeterminate = false;
  }

  getFullTree(userRoleList) {
    return permissionBusiness.getPermissionTree(this, userRoleList);
  }
}

module.exports = Permission;
