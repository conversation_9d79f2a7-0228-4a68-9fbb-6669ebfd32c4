const mysqlDB = require("../common/mysql_pool");

function getParkingReportSqlList(queryParams) {
  let organSql = "";
  if (queryParams.hasSubOrgan === 0 && queryParams.organId !== -1) {
    organSql = ` and uo.id=:organ_id`;
  }
  if (queryParams.hasSubOrgan === 1 && queryParams.organId !== -1) {
    organSql = ` and uo.path like concat(\'%,\',:organ_id,\',%\')`;
  }

  const sql = `select uo.organ_name,gd.plate_no,
  gd.imei,gd.device_name,gd.device_type,gd.device_model,
  gst.stop_start_time,gst.stop_second,gst.stop_address
  from gps_stop gst
  join gps_device gd on gst.imei=gd.imei 
  ${queryParams.deviceType === -1 ? "" : " and gd.device_type=:device_type"}
  ${queryParams.deviceModel === -1 ? "" : " and gd.device_model=:device_model"}
  and gd.is_settle=0 and gd.is_delete=0
  join uc_organ uo on gd.organ_id=uo.id ${
    queryParams.organId === -1 ? "" : organSql
  }
  where gst.stop_second >= :stop_second ${
    queryParams.imei === "" ? "" : " and gst.imei=:imei"
  }
  order by gst.create_time desc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

module.exports = {
  getParkingReportSqlList,
};
