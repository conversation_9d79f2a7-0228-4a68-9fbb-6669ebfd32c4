import { injectable, inject } from "inversify";
import { Context } from "../../types";
import { Repository, MongoRepository } from "typeorm";
import { Location } from "../../domain/entity/Location";
import { Device } from "../../domain/entity/Device";
import { Organ } from "../../domain/entity/Organ";

const locType = {
  GPS: 1,
  LBS: 2,
  WIFI: 3,
};

@injectable()
export default class {
  @inject("locationRepository")
  locationRepository: MongoRepository<Location>;

  @inject("deviceRepository")
  deviceRepository: Repository<Device>;

  @inject("organRepository")
  organRepository: Repository<Organ>;

  /**
   * 获取子组织
   * @param id
   */
  async getChildrens(id: number): Promise<number[]> {
    const res = [id];
    const list = await this.organRepository.find({
      where: {
        parentId: id,
        status: 1,
      },
    });
    if (list.length <= 0) return res;
    const jobs = list.map((item) => this.getChildrens(item.id));
    const childrens = await Promise.all(jobs);
    return res.concat(...childrens);
  }

  /**
   * 轨迹列表
   * @param ctx
   */
  async show(ctx: Context) {
    const id = Number(ctx.param.id);
    if (isNaN(id)) {
      ctx.body = { error: "参数错误" };
      ctx.status = 400;
      return;
    }

    // 查询设备
    const device = await this.deviceRepository.findOne(id);
    if (!device) {
      ctx.body = { error: "设备不存在" };
      ctx.status = 404;
      return;
    }

    // 归属判定
    const ids = await this.getChildrens(ctx.token.aud);
    if (!ids.includes(device.organId)) {
      ctx.body = { error: "设备不属于该组织,不允许查询" };
      ctx.status = 404;
      return;
    }

    const startAt = Number(ctx.query.start_at);
    if (isNaN(startAt)) {
      ctx.body = { error: "参数错误" };
      ctx.status = 400;
      return;
    }

    const endAt = Number(ctx.query.end_at);
    if (isNaN(endAt)) {
      ctx.body = { error: "参数错误" };
      ctx.status = 400;
      return;
    }

    if (endAt < startAt) {
      ctx.body = { error: "结束时间不能早于开始时间" };
      ctx.status = 400;
      return;
    }

    const type = locType[ctx.query.type.toUpperCase()];
    if (type === undefined) {
      ctx.body = { error: "定位类型不存在" };
      ctx.status = 400;
      return;
    }

    // 查询数据
    const data = await this.locationRepository.find({
      order: { locTime: "DESC" },
      where: {
        imei: device.imei,
        locType: type,
        locTime: {
          $gt: startAt,
          $lt: endAt,
        },
      },
    });

    ctx.body = data.map((value) => ({
      lon: value.lon,
      lat: value.lat,
      speed: value.speed,
      time: value.locTime,
    }));
  }
}
