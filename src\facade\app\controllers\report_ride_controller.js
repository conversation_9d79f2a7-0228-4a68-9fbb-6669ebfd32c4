const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const rideBusiness = require("../business/report_ride_business");

/**
 * @api {GET} /report/getridereportlist 工作记录-获取工作报表
 * @apiName getRideReportList
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RA-10
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 18:00
 * @apiParam  {String} endTime 结束时间，格式：2019-03-18 18:00
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getRideReportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "工作报表-获取工作报表",
  buttonPerm: "GZ-01",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId, { min: 1 })
          ? reqQueryParams.organId
          : currentUser.organId,
        hasSubOrgan: validator.isInt01(reqQueryParams.hasSubOrgan)
          ? Number.parseInt(reqQueryParams.hasSubOrgan)
          : 0,
        imei: (reqQueryParams.imei || "").trim(),
        startTime: reqQueryParams.startTime || "",
        endTime: reqQueryParams.endTime || "",
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const rideList = await rideBusiness.getRideReportListLogic(queryParams, currentUser);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        rideList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getRideReportList",
          "获取工作报表出错：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getRideReportList",
        "获取工作报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /report/exportRideReportList 工作记录-导出工作报表
 * @apiName exportRideReportList
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RA-11
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} alarmTypes 报警类型数组，格式：用,隔开
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {boolean} pagination=true 分页: 默认true开启, false关闭
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportRideReportList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "工作报表-导出工作报表",
  buttonPerm: "GZ-02",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        imei: (requestBody.imei || "").trim(),
        startTime: requestBody.startTime || "",
        endTime: requestBody.endTime || "",
        pagination: requestBody.pagination !== false || false,
        currentPage: requestBody.currentPage || 1,
        pageSize: requestBody.pageSize,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      const rideList = await rideBusiness.getRideReportListLogic(queryParams);

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "设备名称",
        "设备号",
        "开始时间",
        "结束时间",
        "工作时长",
        "里程(千米)",
      ]);

      rideList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.deviceName,
          e.imei,
          e.startTime,
          e.endTime,
          e.totalTime,
          e.totalMileage,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `工作报表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportRideReportList",
          "获取工作报表出错：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportRideReportList",
        "获取工作报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /report/getMileageReportList 里程记录-获取里程报表
 * @apiName getMileageReportList
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RA-10
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 18:00
 * @apiParam  {String} endTime 结束时间，格式：2019-03-18 18:00
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getMileageReportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "里程报表-获取里程报表",
  buttonPerm: "LC-01",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId, { min: 1 })
          ? reqQueryParams.organId
          : currentUser.organId,
        hasSubOrgan: validator.isInt01(reqQueryParams.hasSubOrgan)
          ? Number.parseInt(reqQueryParams.hasSubOrgan)
          : 0,
        imei: (reqQueryParams.imei || "").trim(),
        startTime: reqQueryParams.startTime || "",
        endTime: reqQueryParams.endTime || "",
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const mileageList = await rideBusiness.getMileageReportListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        mileageList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getMileageReportList",
          "获取里程报表出错：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getMileageReportList",
        "获取里程报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /report/exportMileageReportList 里程记录-导出里程报表
 * @apiName exportMileageReportList
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RA-11
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} alarmTypes 报警类型数组，格式：用,隔开
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {boolean} pagination=true 分页: 默认true开启, false关闭
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportMileageReportList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "里程报表-导出里程报表",
  buttonPerm: "LC-02",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        imei: (requestBody.imei || "").trim(),
        startTime: requestBody.startTime || "",
        endTime: requestBody.endTime || "",
        pagination: requestBody.pagination !== false || false,
        currentPage: requestBody.currentPage || 1,
        pageSize: requestBody.pageSize,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      const mileageList = await rideBusiness.getMileageReportListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "设备名称",
        "设备号",
        "统计时间",
        "本日里程(千米)",
        "本月里程(千米)",
        "本年里程(千米)",
      ]);

      mileageList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.deviceName,
          e.imei,
          e.dayTime,
          e.mileage,
          e.monthMileage,
          e.yearMileage,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `里程报表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportMileageReportList",
          "获取里程报表出错：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportMileageReportList",
        "获取里程报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
