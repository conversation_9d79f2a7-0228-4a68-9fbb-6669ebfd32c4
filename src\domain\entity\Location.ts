import { Entity, Column, ObjectIdColumn, ObjectID } from "typeorm";

@Entity({ name: "location" })
export class Location {
  @ObjectIdColumn()
  id: ObjectID;

  @Column({
    name: "imei",
    comment: "所属机构",
  })
  imei: string;

  @Column({
    name: "lon",
    comment: "经度",
  })
  lon: number;

  @Column({
    name: "lat",
    comment: "纬度",
  })
  lat: number;

  @Column({
    name: "locType",
    comment: "定位方式：1 GPS, 2 LBS基站, 3 Wifi",
  })
  locType: number;

  @Column({
    name: "locTime",
    comment: "定位时间",
  })
  locTime: number;

  @Column({
    name: "speed",
    comment: "速度",
  })
  speed: number;

  @Column({
    name: "stopEndTime",
  })
  stopEndTime: number;

  @Column({
    name: "stopPeriod",
  })
  stopPeriod: number;

  @Column({
    name: "stopStartTime",
  })
  stopStartTime: number;
}
