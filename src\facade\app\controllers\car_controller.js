const moment = require("moment");
const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const carBusiness = require("../business/car_business");

/**
 * @api {GET} /car/getcarlist 车辆管理-获取车辆列表
 * @apiName getcarlist
 * @apiGroup Car
 * @apiVersion  1.0.0
 * @apiPermission CC-10
 *
 * @apiParam  {String} searchKey 搜索关键字，车牌号，车架号，车主
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getCarList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "车辆管理-获取车辆列表",
  buttonPerm: "CC-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: reqQueryParams.hasSubOrgan === "true" ? 1 : 0,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };

      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const carList = await carBusiness.getCarListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        carList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getCarList",
          "获取车辆列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getCarList",
        "获取车辆列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /car/exportcarlist 车辆管理-导出车辆列表
 * @apiName exportcarlist
 * @apiGroup Car
 * @apiVersion  1.0.0
 * @apiPermission CC-15
 *
 * @apiParam  {String} searchKey 搜索关键字，车牌号，车架号，车主
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {boolean} pagination=true 分页: 默认true开启, false关闭
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportCarList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "车辆管理-导出车辆列表",
  buttonPerm: "CC-15",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        searchKey: (requestBody.searchKey || "").trim(),
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        pagination: requestBody.pagination !== false || false,
        currentPage: requestBody.currentPage || 1,
        pageSize: requestBody.pageSize,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;
      const carList = await carBusiness.getCarListLogic(queryParams);

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "车架号",
        "发动机号",
        "保单号",
        "保单到期日期",
        "车品牌",
        "车型号",
        "车主",
        "车主手机",
        "贷款金额",
        "备注",
        "是否结清",
        "结清时间",
        "创建人",
        "创建时间",
      ]);

      carList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.vinNo,
          e.engineNo,
          e.policyNo,
          e.policyExpDate,
          e.carBrand,
          e.carModel,
          e.carOwner,
          e.ownerPhone,
          e.loanAmount,
          e.remark,
          e.isSettleName,
          e.settleTime,
          e.createUser,
          e.createTime,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `车辆列表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportCarList",
          "导出车辆列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportCarList",
        "导出车辆列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /car/insertcar 车辆管理-添加车辆
 * @apiName insertcar
 * @apiGroup Car
 * @apiVersion  1.0.0
 * @apiPermission CC-11
 *
 * @apiParam  {Number} organId 机构Id
 * @apiParam  {String} plateNo 车牌号
 * @apiParam  {String} vinNo 车架号
 * @apiParam  {String} engineNo 发动机号
 * @apiParam  {String} policyNo 保单号
 * @apiParam  {String} policyExpDate 保单过期日期
 * @apiParam  {String} carBrand 车品牌
 * @apiParam  {String} carModel 车型号
 * @apiParam  {String} carOwner 车主
 * @apiParam  {String} ownerPhone 车主手机
 * @apiParam  {String} loanAmount 贷款金额
 * @apiParam  {String} remark 备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertCar = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "车辆管理-添加车辆",
  buttonPerm: "CC-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      let insertResult = await carBusiness.insertCarLogic(
        requestData,
        currentUser
      );
      insertResult = insertResult.insert ? insertResult.insert : "";
      const insertCount =
        insertResult && insertResult.hasOwnProperty("insertId")
          ? insertResult.insertId
          : 0;
      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertCar",
        `添加车辆成功 ${insertCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertCar",
          "添加车辆失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertCar",
        "添加车辆出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /car/updatecar 车辆管理-更新车辆
 * @apiName updatecar
 * @apiGroup Car
 * @apiVersion  1.0.0
 * @apiPermission CC-12
 *
 * @apiParam  {Number} carId 车辆Id
 * @apiParam  {String} vinNo 车架号
 * @apiParam  {String} engineNo 发动机号
 * @apiParam  {String} policyNo 保单号
 * @apiParam  {String} policyExpDate 保单过期日期
 * @apiParam  {String} carBrand 车品牌
 * @apiParam  {String} carModel 车型号
 * @apiParam  {String} carOwner 车主
 * @apiParam  {String} ownerPhone 车主手机
 * @apiParam  {String} loanAmount 贷款金额
 * @apiParam  {String} remark 备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateCar = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "车辆管理-更新车辆",
  buttonPerm: "CC-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      let updateResult = await carBusiness.updateCarLogic(
        requestData,
        currentUser
      );
      updateResult = updateResult.update ? updateResult.update : "";
      const updateCount =
        updateResult && updateResult.hasOwnProperty("changedRows")
          ? updateResult.changedRows
          : 0;

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateCar",
        `更新车辆成功 ${updateCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateCar",
          "更新车辆失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateCar",
        "更新车辆出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /car/deletecar 车辆管理-删除车辆
 * @apiName deletecar
 * @apiGroup Car
 * @apiVersion  1.0.0
 * @apiPermission CC-13
 *
 * @apiParam  {Number} carId 车辆Id
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteCar = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "车辆管理-删除车辆",
  buttonPerm: "CC-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      const deleteCount = await carBusiness.deleteCarLogic(
        requestData,
        currentUser
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteCar",
        `删除车辆成功 ${deleteCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteCar",
          "删除车辆失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteCar",
        "删除车辆出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /car/getcardropdownlist 车辆管理-获取车辆下拉列表
 * @apiName getcardropdownlist
 * @apiGroup Car
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} searchKey 搜索关键词 匹配车牌号和车主
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
    ]
  }
 */
exports.getCarDropdownList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "车辆管理-获取车辆下拉列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
      };

      const carList = await carBusiness.getCarDropdownLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        carList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getCarDropdownList",
          "获取车辆下拉列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getCarDropdownList",
        "获取车辆下拉列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
