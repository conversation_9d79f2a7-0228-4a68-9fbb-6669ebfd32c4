const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const deviceReportDao = require("../lib/dao/device_report_dao");
const deviceDao = require("../lib/dao/device_dao");

async function getHomeAlarmCountReportLogic(queryParams) {
  const [
    alarmCount,
    attentionCount,
    packingCount,
    superStop,
    MultiDevice,
    nameKeyConfig,
  ] = await Promise.all([
    deviceReportDao.getHomeAlarmCountSql(queryParams),
    deviceReportDao.getHomeAttentionCountSql(queryParams),
    deviceReportDao.getHomePackingCountSql(queryParams),
    deviceDao.getOfflineCount(queryParams),
    getMultiDeviceOfflineListLogic(queryParams),
    global.context.getPropertymapNameKey(["ALARM_CLASS"]),
  ]);

  const statisticsData = nameKeyConfig.ALARM_CLASS;
  for (const key of Object.keys(statisticsData)) {
    const alarmData = alarmCount.find(
      (f) => `${f.alarmClass}` === `${statisticsData[key]}`
    );
    statisticsData[key] = alarmData ? alarmData.count : 0;
    if (`${key}` === "ATTENTION") {
      statisticsData[key] = attentionCount ? attentionCount.count : 0;
    }
  }
  statisticsData.OFFLINE = MultiDevice.length;
  statisticsData.PACKING = packingCount.count || 0;
  statisticsData.LONGOFFLINE = superStop.superStopNum || 0;
  return statisticsData;
}

async function getDeviceTypeReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await deviceReportDao.getDeviceTypeReportSqlList(queryParams);
}

async function getMultiDeviceOfflineListLogic(queryParams) {
  const [multiDeviceList, keyValConfig] = await Promise.all([
    deviceReportDao.getMultiDeviceOfflineSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "DEVICE_MODEL",
      "DEVICE_TYPE",
      "ONLINE_STATE",
    ]),
  ]);

  multiDeviceList.forEach((f, i) => {
    f.index = i + 1;
    const devices = f.deviceInfo.split(",");
    f.deviceList = devices.map((m) => {
      const device = m.split("$");
      return {
        imei: device[0],
        deviceTypeName: keyValConfig.DEVICE_TYPE[device[1]] || "",
        deviceModelName: keyValConfig.DEVICE_MODEL[device[2]] || "",
        onlineStateName: keyValConfig.ONLINE_STATE[device[3]] || "",
        offlineLong: device[4]
          ? commFunc.secondsToHumanize(moment().format("X") - device[4])
          : "",
      };
    });
  });

  return multiDeviceList;
}

async function getDeviceModelReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [deviceModelList, keyValConfig] = await Promise.all([
    deviceReportDao.getDeviceModelReportSqlList(queryParams),
    global.context.getPropertymapKeyVal(["DEVICE_MODEL"]),
  ]);

  deviceModelList.forEach((e) => {
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
  });

  return deviceModelList;
}

async function getOnlineStateReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await deviceReportDao.getOnlineStateReportSqlList(queryParams);
}

async function getDeviceOfflineReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "ONLINE_STATE",
  ]);
  queryParams.onlineState = nameKeyConfig.ONLINE_STATE.OFFLINE;

  return await deviceReportDao.getDeviceOfflineReportSqlList(queryParams);
}

async function getDeviceSettleReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const settleSqlList = await deviceReportDao.getDeviceSettleReportSqlList(
    queryParams
  );

  const settleList = [1, 0].map((m) => {
    const data = settleSqlList.find((f) => f.isSettle === m);
    if (!data) {
      return { isSettle: m, deviceCount: 0 };
    }
    return data;
  });
  settleList.forEach((e) => {
    e.isSettleName = e.isSettle === 1 ? "结清" : "未结清";
  });

  return settleList;
}

async function getDeviceSimRenewReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "SIM_STATE",
  ]);
  queryParams.expireState = nameKeyConfig.SIM_STATE.GQ;
  queryParams.usedState = nameKeyConfig.SIM_STATE.ZC;

  return await deviceReportDao.getDeviceSimRenewReportSqlList(queryParams);
}

async function getProvinceTop10ReportListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await deviceReportDao.getProvinceTop10ReportSqlList(queryParams);
}

module.exports = {
  getHomeAlarmCountReportLogic,
  getDeviceTypeReportListLogic,
  getMultiDeviceOfflineListLogic,
  getDeviceModelReportListLogic,
  getOnlineStateReportListLogic,
  getDeviceOfflineReportListLogic,
  getDeviceSettleReportListLogic,
  getDeviceSimRenewReportListLogic,
  getProvinceTop10ReportListLogic,
};
