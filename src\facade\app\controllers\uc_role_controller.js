const _ = require("lodash");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const Permission = require("../proxy/permission");
const staticConfig = require("../config/config_static");
const validator = require("../lib/common/validator_extend");

const roleBusiness = require("../business/uc_role_business");
const userBusiness = require("../business/uc_user_business");

/**
 * @api {GET} /ucrole/getrolelist 角色管理-获取角色分页列表
 * @apiName getrolelist
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 * @apiPermission URL-10
 *
 * @apiParam  {number=0,1} status 状态 0：禁用、1：启用
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 *
 * @apiSuccessExample {json} 响应示例:
   {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": {
      "total": 2,
      "rows": [
        {
          "roleId": 15,
          "roleName": "业务员",
          "status": 1,
          "remark": null,
          "createUser": "cmadmin",
          "createTime": "2017-08-18 10:14"
        }
      ]
    }
  }
 */
exports.getRoleList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "角色管理-获取角色分页列表",
  buttonPerm: "URL-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        status: validator.isInt01(reqQueryParams.status)
          ? reqQueryParams.status
          : -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const roleList = await roleBusiness.getRoleListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        roleList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getRoleList",
        "获取角色分页列表错误:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};

/**
 * @api {GET} /ucrole/getpermissiontree 角色管理-获取权限树列表
 * @apiName getpermissiontree
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 * @apiPermission URL-16
 *
 * @apiParam  {Number} systemType 所属系统
 *
 * @apiParamExample  {json} 请求示例:
 Content-Type:application/json;charset=utf-8
 {
 }
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
   errcode : 0,
   errmsg : '操作成功',
   retobj : [
   {
     "id": 1,
     "perId": 1,
     "parentId": 0,
     "perCode": "",
     "perName": "厨盟用户中心",
     "path": "0,1",
     "perUrl": "",
     "perIcon": "",
     "perType": "",
     "sort": 0,
     "systemType": 1,
     "status": 1,
     "remark": null,
     "createUser": null,
     "createTime": null,
     "children": [
       {
         "id": 11,
         "perId": 11,
         "parentId": 1,
         "perCode": "",
         "perName": "系统设置",
         "path": "0,1,11",
         "perUrl": "",
         "perIcon": "",
         "perType": "MENU",
         "sort": 0,
         "systemType": 1,
         "status": 1,
         "remark": null,
         "createUser": null,
         "createTime": null
       }
     ]
   ]
 }
 */
exports.getPermissionTree = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "角色管理-获取权限树列表",
  buttonPerm: "URL-16",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const permission = new Permission();
      const treeData = await permission.getFullTree.call(
        permission,
        currentUser.isSuper === 1 ? "" : currentUser.userRoleList
      );
      if (treeData && treeData.hasOwnProperty("children")) {
        ctx.body = new RetJson(
          i18n.SYS_SUCCESS_CODE,
          i18n.SYS_SUCCESS_MESSAGE,
          treeData.children
        );
        return;
      }

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        []
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getPermissionTree",
        "角色管理-获取权限树列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {GET} /ucrole/getroledropdownlist 角色管理-获取角色下拉列表
 * @apiName getroledropdownlist
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 *
 * @apiParam  {number} organId 角色ID
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用
 *
 * @apiSuccessExample {json} 响应示例:
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": [
      {
        "roleId": 10,
        "roleName": "超级管理员",
        "status": 1
      }
    ]
  }
 */
exports.getRoleDropDownList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "角色管理-获取角色下拉列表",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        organId: validator.isIntFormat(ctx.request.query.organId, { min: 0 })
          ? ctx.request.query.organId
          : currentUser.organId,
        userId: validator.isIntFormat(ctx.request.query.userId, { min: 0 })
          ? ctx.request.query.userId : 0,
        status: validator.isInt01(ctx.request.query.status)
          ? ctx.request.query.status
          : -1,
      };

      // 获取主账号角色
      if(queryParams.userId){
        queryParams.type = 1;
        const user = await userBusiness.getUserLogic(queryParams) || {};
        queryParams.roleIds = _.trim(user.roleIds, ',') || '';
      }

      const dropDownList = await roleBusiness.getRoleDropDownListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        dropDownList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getRoleDropDownList",
        "获取角色下拉列表错误:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {POST} /ucrole/insertrole 角色管理-新增角色
 * @apiName insertrole
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 * @apiPermission URL-11
 *
 * @apiParam  {String} roleName 角色名 必需值
 * @apiParam  {String} remark 备注
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
    errcode : 0,
    errmsg : '操作成功',
    retobj : ''
 }
 */
exports.insertRole = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "角色管理-新增角色",
  buttonPerm: "URL-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      await roleBusiness.insertRoleLogic(requestData, currentUser.loginName);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertRole",
        "新增角色表记录成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertRole",
          "新增角色表记录失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertRole",
        "新增角色表记录出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /ucrole/updaterole 角色管理-编辑角色
 * @apiName updaterole
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 * @apiPermission URL-12
 *
 * @apiParam  {Number} roleId 角色Id 必需值
 * @apiParam  {String} roleName 角色名 必需值
 * @apiParam  {String} remark 备注
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 *
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
    errcode : 0,
    errmsg : '操作成功',
    retobj : ''
 }
 */
exports.updateRole = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "角色管理-更新角色",
  buttonPerm: "URL-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      if (!validator.isIntFormat(requestData.roleId, { min: 1 })) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_ID_ERROR
        );
        return;
      }

      await roleBusiness.updateRoleLogic(requestData, currentUser.loginName);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateRole",
        "更新角色表记录成功：",
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateRole",
          "更新角色表记录失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateRole",
        "更新角色表记录成功：",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /ucrole/getrolepermission 角色管理-获取角色权限列表
 * @apiName getrolepermission
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 * @apiPermission URL-20
 *
 * @apiParam  {Number} roleId 角色Id
 *
 * @apiSuccessExample {json} 响应示例:
  {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": []
  }
 */
exports.getRolePermission = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "角色管理-获取角色权限列表",
  buttonPerm: "URL-20",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const queryParams = {
        roleId: ctx.request.query.roleId,
      };
      if (!validator.isIntFormat(queryParams.roleId)) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_ID_ERROR,
          []
        );
        return;
      }

      const rpList = await roleBusiness.getRolePermissionLogic(queryParams);

      rpList.forEach((f) => {
        f.path = f.path.substring(1, f.path.length - 1);
      });

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        rpList
      );
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getRolePermByRoleId",
        "获取角色权限列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, []);
    }
  },
};

/**
 * @api {POST} /ucrole/insertrolepermission 角色管理-设置角色权限
 * @apiName insertrolepermission
 * @apiGroup UcRole
 * @apiVersion  1.0.0
 * @apiPermission URL-21
 *
 * @apiParam  {Number} roleId 角色Id 必需值
 * @apiParam  {String} roleName 角色名 必需值
 * @apiParam  {String} remark 备注
 * @apiParam  {Number=0,1} status 状态 0：禁用、1：启用 必需值
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
      errcode : 0,
      errmsg : '操作成功',
      retobj : { changedRows:1 }
   }
 */
exports.insertRolePermission = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "角色管理-设置角色权限",
  buttonPerm: "URL-21",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;

      if (!validator.isIntFormat(requestData.roleId, { min: 1 })) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_ID_ERROR
        );
        return;
      }

      if (
        requestData.strPerId &&
        !validator.isCommaSeparated(requestData.strPerId)
      ) {
        ctx.body = new RetJson(
          i18n.SYS_ERROR_CODE,
          i18n.REQUEST_INPUT_DATA_ERROR
        );
        return;
      }

      await roleBusiness.insertRolePermissionLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertRolePermission",
        `为角色${requestData.roleId}保存权限成功`,
        ""
      );

      await global.context.redisClient.del(
        staticConfig.redisPrename.permission
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertRolePermission",
        `为角色${ctx.request.body.roleId}保存权限失败，`,
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
