const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const parkingBusiness = require("../business/report_parking_business");

/**
 * @api {GET} /report/getparkingreportlist 报警记录-获取停车报表
 * @apiName getparkingreportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RP-10
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {Number} parkingSecond 大于停留秒数
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getParkingReportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-获取停车报表",
  buttonPerm: "RP-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: validator.isInt01(reqQueryParams.hasSubOrgan)
          ? Number.parseInt(reqQueryParams.hasSubOrgan)
          : 0,
        imei: (reqQueryParams.imei || "").trim(),
        startTime: reqQueryParams.startTime || "",
        endTime: reqQueryParams.startTime || "",
        deviceType: validator.isIntFormat(reqQueryParams.deviceType, { min: 1 })
          ? Number.parseInt(reqQueryParams.deviceType, 10)
          : -1,
        deviceModel: validator.isIntFormat(reqQueryParams.deviceModel, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.deviceModel, 10)
          : -1,
        parkingSecond: validator.isIntFormat(reqQueryParams.parkingSecond, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.parkingSecond, 10)
          : 0,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const parkingList = await parkingBusiness.getParkingListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        parkingList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getParkingReportList",
          "获取停车报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getParkingReportList",
        "获取停车报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /report/exportparkingreportlist 报警记录-导出停车报表
 * @apiName exportparkingreportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission RP-11
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {Number} deviceType 设备类型
 * @apiParam  {Number} deviceModel 设备型号
 * @apiParam  {Number} parkingSecond 大于停留秒数
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportParkingReportList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-导出停车报表",
  buttonPerm: "RP-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        imei: (requestBody.imei || "").trim(),
        startTime: requestBody.startTime || "",
        endTime: reqQueryParams.startTime || "",
        deviceType: validator.isIntFormat(requestBody.deviceType, { min: 1 })
          ? Number.parseInt(requestBody.deviceType, 10)
          : -1,
        deviceModel: validator.isIntFormat(requestBody.deviceModel, { min: 1 })
          ? Number.parseInt(requestBody.deviceModel, 10)
          : -1,
        parkingSecond: validator.isIntFormat(requestBody.parkingSecond, {
          min: 1,
        })
          ? Number.parseInt(requestBody.parkingSecond, 10)
          : 0,
        pagination: false,
      };

      const parkingList = await parkingBusiness.getParkingListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "所属车组",
        "车牌号",
        "设备号",
        "设备名称",
        "设备类型",
        "设备型号",
        "开始停车时间",
        "停车时长",
        "停车地址",
      ]);

      parkingList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.organName,
          e.plateNo,
          e.imei,
          e.deviceName,
          e.deviceTypeName,
          e.deviceModelName,
          e.parkingStartTime,
          e.parkingSecond,
          e.parkingAddress,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `停车报表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportParkingReportList",
          "导出停车报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportParkingReportList",
        "导出停车报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
