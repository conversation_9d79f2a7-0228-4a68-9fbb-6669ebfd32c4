const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");

const operationDao = require("../lib/dao/report_operation_dao");

/**
 * 获取操作记录
 * 请求参数:
 "searchKey": "搜索信息",
 "organId": "机构Id",
 "imei": "IMEI号",
 "operationType": "操作类型",
 "startTime": "开始时间戳",
 "endTime": "结束时间戳",
 "userId": "",
 "pagination": true,
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getOperationListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "startTime",
      title: "开始时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
    {
      field: "endTime",
      title: "结束时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDDHHmm" }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.startTime = `${queryParams.startTime}:00`;
  queryParams.endTime = `${queryParams.endTime}:00`;

  const operationList = await operationDao.getOperationReportSqlList(
    queryParams,
    currentUser
  );

  operationList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return operationList;
}

module.exports = {
  getOperationListLogic,
};
