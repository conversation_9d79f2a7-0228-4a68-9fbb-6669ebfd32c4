const moment = require("moment");
const utility = require("utility");
const jwt = require("jsonwebtoken");
const urlencode = require("urlencode");

const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("./../lib/common/comm_func");
const staticConfig = require("../config/config_static");
const basicBusiness = require("../business/basic_business");

// 登录 Token 认证
async function loginRequired(ctx, next) {
  try {
    const auth = ctx.get("Authorization");
    const token = auth.split(" ")[1];
    ctx.state.user = jwt.verify(token, staticConfig.jwtSecret);
    const currentUser = JSON.parse(JSON.stringify(ctx.state.user));
    delete currentUser.iat;
    delete currentUser.exp;
    ctx.set(
      "Authorization",
      jwt.sign(currentUser, staticConfig.jwtSecret, { expiresIn: "2d" })
    );
    await next();
  } catch (err) {
    ctx.status = 403;
    ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_LOGIN_AUTH_ERROR);
    global.context.logger.error(
      "AUTH",
      "SYS",
      "ERRORHANDLER",
      `${commFunc.getUserClientIP(ctx)}；${ctx.originalUrl}`,
      err.message
    );
  }
}
exports.loginRequired = loginRequired;

// 权限认证
async function permissionRequired(ctx, next) {
  const currentUser = ctx.state.user;
  try {
    if (currentUser.isSuper === 1) {
      await next();
      return;
    }

    let permissionCache = await basicBusiness.getCurrentUserPermission(
      currentUser,
      ["BUTTON"]
    );
    permissionCache = permissionCache.filter(
      (p) => (p.perUrl || "").toLowerCase() === ctx.request.path.toLowerCase()
    );
    if (permissionCache.length === 0) {
      ctx.status = 401;
      ctx.body = new RetJson(
        i18n.SYS_ERROR_CODE,
        i18n.SYS_ERROR_MESSAGE,
        i18n.SYS_PERMISSION_AUTH_ERROR
      );
      return;
    }

    await next();
  } catch (err) {
    global.context.logger.error(
      currentUser.loginName,
      "PERMISSION",
      "permissionRequired",
      "用户获取权限出错：",
      err
    );

    ctx.status = 401;
    ctx.body = new RetJson(
      i18n.SYS_ERROR_CODE,
      i18n.SYS_ERROR_MESSAGE,
      i18n.SYS_PERMISSION_AUTH_ERROR
    );
  }
}
exports.permissionRequired = permissionRequired;

// api 接口认证
async function authApiToken(ctx, next) {
  const { apikey, timestamp, sign } = ctx.request.query;

  if (!apikey || !timestamp || !sign) {
    ctx.status = 403;
    ctx.body = new RetJson(50200, "您的参数不合法!");
    return;
  }

  if (moment().format("X") - timestamp > 10 * 60) {
    ctx.body = new RetJson(50200, "您的签名已经过期!");
    return;
  }

  const userToken = global.context.apiTokenConfig.find(
    (api) => `${api.apikey}` === `${apikey}`
  );

  if (!userToken) {
    ctx.body = new RetJson(50200, "您的Key不合法!");
    return;
  }

  const reqParams = ctx.request.query;

  let gather = "";
  let keysTemp = Object.keys(reqParams);
  keysTemp = keysTemp.filter((k) => k !== "sign");
  keysTemp.sort();

  for (const key of keysTemp) {
    gather += `${key}=${reqParams[key]}`;
  }
  gather = userToken.secretkey + gather + userToken.secretkey;

  if (utility.md5(urlencode(gather)).toUpperCase() !== sign) {
    ctx.body = new RetJson(50200, "您的签名不合法!");
    return;
  }

  await next();
}
exports.authApiToken = authApiToken;
