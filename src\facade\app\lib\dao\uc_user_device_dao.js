const _ = require("lodash");
const {getDaoShard} = require("../utils/sharding");

const mysqlDB = require("../common/mysql_pool");

function getUserDeviceSqlList(queryParams) {
  const ucUserDeviceTable = getDaoShard('uc_user_device', queryParams.userId);
  const sql = `select uud.imei
      from ${ucUserDeviceTable} uud
      join uc_organ uo on uud.organ_id=uo.id and (:current_organ_id=-1 or path like concat('%,',:current_organ_id,',%'))
      where user_id=:user_id
      and (:search_organ_id=-1 or ifnull(uo.path, '') like concat('%',:search_organ_id,'%'))
      order by uud.create_time asc`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function batchUpdateUserDeviceSql(userDeviceData) {
  const sqlTasks = [];
  const ucUserDeviceTable = getDaoShard('uc_user_device', userDeviceData.userId);
  // 删除旧用户设备
  const delSql = `delete from ${ucUserDeviceTable} where user_id=${userDeviceData.userId} and imei in (${userDeviceData.delImeis})`;
  // 添加新用户设备
  const addSql = `insert into ${ucUserDeviceTable}
          (organ_id, user_id, imei, create_user, create_time)
          values
          (:organ_id, :user_id, :imei, :create_user, now())`;
  if(userDeviceData.delImeis.length){
    sqlTasks.push({sql: delSql});
  }
  // 批量添加
  userDeviceData.addDevices.forEach((device) => {
    sqlTasks.push({
      sql: addSql, params: {
        organId: device.organId,
        userId: userDeviceData.userId,
        createUser: userDeviceData.createName,
        imei: device.imei
      }
    });
  });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getUserDeviceSqlList,
  batchUpdateUserDeviceSql
};
