const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");
const permissionDao = require("../lib/dao/uc_permission_dao");

function _validatePermissionRequest(requestData) {
  const rules = [
    {
      field: "parentId",
      title: "父目录",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    { field: "perCode", title: "权限编码", rule: [], msg: "" },
    { field: "perName", title: "权限名", rule: [], msg: "", required: true },
    {
      field: "path",
      title: "节点路径",
      rule: "isCommaSeparated",
      msg: "",
      required: true,
    },
    { field: "perUrl", title: "路径", rule: [], msg: "" },
    { field: "perIcon", title: "图标", rule: [], msg: "" },
    { field: "perType", title: "类型", rule: [], msg: "" },
    {
      field: "sort",
      title: "排序",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "值必须大于等于0",
    },
    {
      field: "systemType",
      title: "所属系统",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "值必须大于等于0",
      required: true,
    },
    {
      field: "status",
      title: "状态",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    { field: "remark", title: "备注", rule: [], msg: "" },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  model.sort = model.sort || 0;
  model.path = `,${model.path},`.trim();
  return [undefined, model];
}

async function getPermissionTree(parentNode, roleIds) {
  const permissionSql = await permissionDao.getPermissionSqlList({
    roleIds,
    status: 1,
    systemType: -1,
    parentId: -1,
  });
  if (!permissionSql || permissionSql.length === 0) {
    return {};
  }

  permissionSql.forEach((m) => {
    m.path = m.path.substring(1, m.path.length - 1);
    m.selected = false;
    m.indeterminate = false;
  });
  parentNode = commFunc.buildTreeData(permissionSql, parentNode);

  return parentNode;
}

async function getPermissionMenuTree(systemType) {
  const permissionSql = await permissionDao.getPermissionSqlList({
    roleIds: "",
    status: -1,
    systemType,
    parentId: -1,
  });
  permissionSql.forEach((m) => {
    m.path = m.path.substring(1, m.path.length - 1);
  });

  const permissionNodes = permissionSql.filter(
    (f) => `${f.parentId}` === `${systemType}`
  );
  permissionNodes.forEach((f) => {
    f.children = permissionSql.filter((sb) => sb.parentId === f.id);
  });

  return permissionNodes;
}

async function getPermissionButtonList(parentId) {
  const permissionSql = await permissionDao.getPermissionSqlList({
    roleIds: "",
    status: -1,
    systemType: -1,
    parentId,
  });
  permissionSql.forEach((m) => {
    m.path = m.path.substring(1, m.path.length - 1);
  });

  return permissionSql;
}

async function insertPermissionLogic(insertData, userName) {
  const [err, permissionModel] = _validatePermissionRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await permissionDao.getPermissionRecordsSql(permissionModel);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  permissionModel.createUser = userName;
  await permissionDao.insertPermissionSql(permissionModel);
}

async function updatePermissionLogic(updateData, userName) {
  const [err, permissionModel] = _validatePermissionRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await permissionDao.getPermissionRecordsSql(permissionModel);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  if (
    Number.parseInt(permissionModel.perId, 10) !== isExists.perId &&
    isExists.record
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  permissionModel.updateUser = userName;
  await permissionDao.updatePermissionSql(permissionModel);
}

function _validateBatchRequestData(requestData) {
  const rules = [
    {
      field: "parentId",
      title: "父目录",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    {
      field: "path",
      title: "节点路径",
      rule: "isCommaSeparated",
      msg: "",
      required: true,
    },
    {
      field: "systemType",
      title: "所属系统",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  const itemRules = [
    { field: "perName", required: true },
    { field: "perUrl", required: true },
  ];

  if (!validator.isArray(requestData.routeList, { min: 1 }, itemRules)) {
    return [i18n.REQUEST_INPUT_DATA_ERROR];
  }

  model.sort = 0;
  model.perIcon = "";
  model.perType = "BUTTON";
  model.status = 1;
  model.remark = "";
  model.path = `,${model.path},`.trim();

  return [undefined, model];
}

async function batchInsertPermissionLogic(insertData, userName) {
  const [err, permissionModel] = _validateBatchRequestData(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const insertList = permissionModel.routeList.map((m) => ({
    perName: m.perName,
    perUrl: m.perUrl,
    parentId: permissionModel.parentId,
    systemType: permissionModel.systemType,
    path: permissionModel.path,
    sort: permissionModel.sort,
    perCode: m.buttonPerm,
    perIcon: permissionModel.perIcon,
    perType: permissionModel.perType,
    status: permissionModel.status,
    remark: permissionModel.remark,
    createUser: userName,
  }));

  await permissionDao.batchInsertPermissionSql(insertList);

  return "";
}

async function setPermissionEnableLogic(updateData) {
  const permissionData = await permissionDao.getPermissionSqlData(updateData);
  if (!permissionData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_NOT_FOUND);
  }

  const queryParams = {
    perIds: permissionData.path.replace(/^,+/, "").replace(/,+$/, ""),
    perId: updateData.perId,
  };
  const permissionList = await permissionDao.getPermissionByIdsSql(queryParams);
  if (permissionList.filter((f) => !f.status).length > 0) {
    throw new RetJson(
      i18n.SYS_ERROR_CODE,
      i18n.RESPONSE_PERMISSION_PARENT_DISABLED
    );
  }

  Object.assign(updateData, { path: permissionData.path });
  await permissionDao.setPermissionEnableSql(updateData);
  return "";
}

async function deleteBatchPermissionLogic(deleteList) {
  // const permissionData = await permissionDao.getPermissionSqlData(updateData);
  // if (!permissionData) {
  //   throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_NOT_FOUND);
  // }
  // if (permissionData.isCanDelete !== 1) {
  //   throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_PERMISSION_HAS_ASSISGEND);
  // }

  await permissionDao.deleteBatchPermissionSql(deleteList);

  return "";
}

module.exports = {
  getPermissionTree,
  getPermissionMenuTree,
  getPermissionButtonList,
  insertPermissionLogic,
  updatePermissionLogic,
  batchInsertPermissionLogic,
  setPermissionEnableLogic,
  deleteBatchPermissionLogic,
};
