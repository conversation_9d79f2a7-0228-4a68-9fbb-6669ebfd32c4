const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");

const routeBusiness = require("../business/uc_route_business");

/**
 * @api {GET} /ucroute/getroutelist 路由管理-获取路由列表
 * @apiName getroutelist
 * @apiGroup UcRoute
 * @apiVersion  1.0.0
 * @apiPermission URT-10
 *
 * @apiParam  {string} searchKey 搜索关键词 匹配路径、中间件、方法名、方法描述
 * @apiParam  {Number} isPerm 是否是权限
 * @apiParam  {string} httpMethod 请求方法 用下拉 ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
 * @apiParam  {Number} systemType 所属系统：1、用户中心，2、快么后台，3、采集系统
 * @apiParam  {Number} routeType 接口类型：1、内部接口，2、外部接口
 * @apiParam  {number=0,1} status 状态 0：禁用、1：启用
 * @apiParam  {number} pageSize=20 行数
 * @apiParam  {number} currentPage=0 当前页
 *
 * @apiSuccess (200) {json} errcode 错误代码：0：成功，1：失败
 * @apiSuccess (200) {json} errmsg 错误消息
 * @apiSuccess (200) {json} retobj 返回对象
 *
 * @apiParamExample  {json} 请求示例:
   {
       searchKey : "getServiceGoodsList",
       httpMethod : "GET",
       ....
   }
 *
 * @apiSuccessExample {json} 响应示例:
   {
    "errcode": 0,
    "errmsg": "操作成功",
    "retobj": {
        "total": 251,
        "rows": [
            {
                "routeId": "路由Id",
                "systemType": "所属系统：1、用户中心，2、快么后台，3、采集系统",
                "buttonPerm": "按钮权限",
                "httpMethod": "请求方法",
                "routeType": "接口类型：1、内部接口，2、外部接口",
                "routePath": "路由路径",
                "middlewares": "中间件",
                "methodName": "方法名",
                "methodDesc": "方法描述",
                "status": "状态：0、禁用，1、启用"
            }
        ]
    }
  }
 */
exports.getRouteList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "路由管理-获取基础路由列表",
  buttonPerm: "URT-10",
  handler: async (ctx) => {
    try {
      const requestParams = ctx.request.query;

      const queryParams = {
        searchKey: (requestParams.searchKey || "").trim(),
        isPerm: requestParams.isPerm ? 1 : 0,
        httpMethod: (requestParams.httpMethod || "").trim(),
        systemType: validator.isIntFormat(requestParams.systemType, { min: 1 })
          ? requestParams.systemType
          : -1,
        routeType: validator.isIntFormat(requestParams.routeType, { min: 1 })
          ? requestParams.routeType
          : -1,
        status: validator.isInt01(requestParams.status)
          ? requestParams.status
          : -1,
        pageSize: validator.isIntFormat(requestParams.pageSize, { min: 1 })
          ? Number.parseInt(requestParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(requestParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(requestParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const routeList = await routeBusiness.getRouteListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        routeList
      );
    } catch (err) {
      global.context.logger.error(
        ctx.state.user.userName,
        "MYSQL",
        "getRouteList",
        "获取基础路由列表出错:",
        err
      );

      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};

/**
 * @api {POST} /ucroute/insertroute 路由管理-初始化路由
 * @apiName insertroute
 * @apiGroup UcRoute
 * @apiVersion  1.0.0
 *
 * @apiParamExample  {json} 请求示例:
   Content-Type:application/json;charset=utf-8
   { }
 *
 * @apiSuccessExample {Object} 响应示例:
   HTTP/1.1 200 OK
   {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertRoute = async (ctx) => {
  try {
    const requestData = ctx.request.body.routeData;
    const repeatPers = ctx.request.body.repeatPers;

    await routeBusiness.insertRouteLogic(requestData, repeatPers);

    global.context.logger.info(
      ctx.state.user.userName,
      "USER",
      "insertRoute",
      "新增基础路由",
      "成功"
    );

    ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
  } catch (err) {
    if (err.hasOwnProperty("errcode")) {
      global.context.logger.error(
        ctx.state.user.userName,
        "USER",
        "insertRoute",
        "新增基础路由失败:",
        err
      );
      ctx.body = err;
      return;
    }

    global.context.logger.error(
      ctx.state.user.userName,
      "MYSQL",
      "insertRoute",
      "新增基础路由出错:",
      err
    );

    ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
  }
};
