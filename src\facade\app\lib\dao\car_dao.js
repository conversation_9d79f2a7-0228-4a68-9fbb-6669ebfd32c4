const mysqlDB = require("../common/mysql_pool");

function getCarSqlList(queryParams) {
  const sql = `select gc.id car_id,gc.organ_id,gc.plate_no,gc.vin_no,gc.engine_no,
  gc.policy_no,gc.policy_exp_date,gc.car_brand,gc.car_model,gc.car_owner,gc.owner_phone,gc.loan_amount,
  gc.is_settle,gc.settle_time,gc.remark,uo.organ_name,gc.create_user,gc.create_time
  from gps_car gc
  join uc_organ uo on gc.organ_id=uo.id ${
    queryParams.hasSubOrgan === 1
      ? " and uo.path like concat('%,',:organ_id,',%')"
      : ""
  }
  where (:search_key='' or concat(gc.plate_no,gc.vin_no,gc.car_owner) like concat('%',:search_key,'%'))
  ${queryParams.hasSubOrgan === 0 ? " and gc.organ_id=:organ_id" : ""}`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getCarDropdownSqlList(queryParams) {
  const sql = `select gc.id car_id,gc.plate_no,gc.vin_no,gc.engine_no,gc.policy_no,gc.policy_exp_date,
  gc.car_brand,gc.car_model,gc.car_owner,gc.owner_phone,gc.loan_amount,gc.remark carRemark
  from gps_car gc
  where (:search_key='' or concat(gc.plate_no,gc.car_owner) like concat('%',:search_key,'%'))
  order by gc.plate_no
  limit 50`;

  return mysqlDB.all(sql, queryParams);
}

function getCarSqlData(queryParams) {
  const sql = `select gc.id car_id,gc.plate_no,gc.vin_no,gc.engine_no,gc.policy_no,gc.policy_exp_date,
  gc.car_brand,gc.car_model,gc.car_owner,gc.owner_phone,gc.loan_amount,gc.remark carRemark
  from gps_car gc
  where gc.plate_no=:plate_no`;

  return mysqlDB.one(sql, queryParams);
}

function getCarRecordsSql(queryParams) {
  const sql = `select count(*) record,id car_id
  from gps_car
  where plate_no=:plate_no`;

  return mysqlDB.one(sql, queryParams);
}

function insertCarSql(insertData, operationModel) {
  const sqlTasks = [];

  const sql = `insert into gps_car(organ_id,plate_no,vin_no,engine_no,policy_no,policy_exp_date,car_brand,car_model,
  car_owner,owner_phone,loan_amount,remark,create_user,create_time)
  values(:organ_id,:plate_no,:vin_no,:engine_no,:policy_no,:policy_exp_date,:car_brand,:car_model,
  :car_owner,:owner_phone,:loan_amount,:remark,:user_name,now())`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,:plate_no,'',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ field: "insert", sql: sql, params: insertData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

function updateCarSql(updateData, operationModel) {
  const sqlTasks = [];

  const sql = `update gps_car 
  set vin_no=:vin_no,engine_no=:engine_no,policy_no=:policy_no,policy_exp_date=:policy_exp_date,
  car_brand=:car_brand,car_model=:car_model,car_owner=:car_owner,owner_phone=:owner_phone,loan_amount=:loan_amount,
  remark=:remark,update_user=:user_name,update_time=now()
  where id=:car_id`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,:plate_no,'',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ field: "update", sql: sql, params: updateData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

function getCarInfoStateSql(queryParams) {
  const sql = `select count(*) device_count 
    from gps_device gd
    join gps_car gc on gc.id=:car_id and gc.plate_no=gd.plate_no`;

  return mysqlDB.one(sql, queryParams);
}

function deleteCarSql(deleteData, operationModel) {
  const sqlTasks = [];

  const sql = `delete from gps_car where id=:car_id`;

  const optSql = `insert into gps_operation_log(user_id,organ_id,organ_name,plate_no,imei,
  operation_type,content,create_user,create_time)
  select :user_id,:organ_id,organ_name,'','',:operation_type,:content,:realname,now()
  from uc_organ
  where id=:organ_id`;

  sqlTasks.push({ sql: sql, params: deleteData });
  sqlTasks.push({ sql: optSql, params: operationModel });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getCarSqlList,
  getCarDropdownSqlList,
  getCarSqlData,
  getCarRecordsSql,
  insertCarSql,
  updateCarSql,
  getCarInfoStateSql,
  deleteCarSql,
};
