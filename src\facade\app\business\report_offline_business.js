const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");

const offlineDao = require("../lib/dao/report_offline_dao");

/**
 * 获取离线记录
 * 请求参数:
 "organId": "机构Id",
 "imei": "IMEI号",
 "deviceType": "设备类型",
 "startTime": "开始时间戳",
 "endTime": "结束时间戳",
 "pagination": true,
 "pageSize": 20,
 "currentPage": 1,
 "offset": 1
 *
 */
async function getOfflineListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: "isIntFormat",
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "" },
    {
      field: "startSecond",
      title: "开始时间",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "endSecond",
      title: "结束时间",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  if (queryParams.endSecond <= queryParams.startTime) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  // 计算离线时间区间值，如果传过来的是 大于30天 这样的值，如 startSecond:30*3600*24，endSecond:0，则startTime设置为0
  queryParams.startTime = moment().format("X") - queryParams.endSecond;
  queryParams.endTime = moment().format("X") - queryParams.startSecond;

  const [offlineList, keyValConfig] = await Promise.all([
    offlineDao.getOfflineReportSqlList(queryParams, currentUser),
    global.context.getPropertymapKeyVal(["DEVICE_TYPE", "DEVICE_MODEL"]),
  ]);

  offlineList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.currentAddress = "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.offlineLong = e.lastOfflineTime
      ? commFunc.secondsToHumanize(moment().format("X") - e.lastOfflineTime)
      : "";
    e.lastOnlineTime = e.lastOnlineTime
      ? moment.unix(e.lastOnlineTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.lastOfflineTime = e.lastOfflineTime
      ? moment.unix(e.lastOfflineTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return offlineList;
}

module.exports = {
  getOfflineListLogic,
};
