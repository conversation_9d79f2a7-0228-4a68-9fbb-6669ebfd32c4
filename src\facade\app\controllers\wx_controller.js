"use strict";
const utility = require("utility");
const jwt = require("jsonwebtoken");

const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const staticConfig = require("../config/config_static");
const co = require("co");
const request = require("co-request");

/**
 *
 * @api {GET} /getFriendList 获取微信个人列表
 * @apiName getFriendList
 * @apiGroup WX
 * @apiVersion  1.0.0
 *
 */
exports.getFriendList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "获取",
  handler: async (ctx) => {
    try {
      let result = await co(function* () {
        let result = yield request({
          uri: staticConfig.thirdPartyConfig.wxDomain + "/zt/api.php?act=get_friend_list",
          method: "GET",
          headers: {
            // "Content-Type":"application/x-www-form-urlencoded; charset=UTF-8",
            Accept: "application/json",
          },
        });
        return result;
      });
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        JSON.parse(result.body)
      );
    } catch (err) {
      global.context.logger.error("get_friend_list", "获取微信个人列表:", err);
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};

/**
 *
 * @api {GET} /getGroupList 获取微信分组(群)列表
 * @apiName getGroupList
 * @apiGroup WX
 * @apiVersion  1.0.0
 *
 * @apiParam  {String} act 接口名称
 *
 */
exports.getGroupList = {
  method: "GET",
  middlewares: ["loginRequired"],
  routeDesc: "获取",
  handler: async (ctx) => {
    try {
      let result = await co(function* () {
        let result = yield request({
          uri: staticConfig.thirdPartyConfig.wxDomain + "/zt/api.php?act=get_group_list",
          method: "GET",
          headers: {
            // "Content-Type":"application/x-www-form-urlencoded; charset=UTF-8",
            Accept: "application/json",
          },
        });
        return result;
      });
      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        JSON.parse(result.body)
      );
    } catch (err) {
      global.context.logger.error(
        "get_group_list",
        "获取微信分组(群)列表:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE, {
        total: 0,
        rows: [],
      });
    }
  },
};
