import { Entity, PrimaryColumn, Column } from "typeorm";

@Entity({ name: "gps_alarm_cycle" })
export class Alarm {
  @PrimaryColumn()
  id: number;

  @Column({
    name: "imei",
    comment: "设备IMEI号",
  })
  imei: string;

  @Column({
    name: "alarm_class",
    comment: "风险报警，具体数值看配置表",
  })
  alarmClass: number;

  @Column({
    name: "alarm_type",
    comment: "报警类型，具体数值看配置表",
  })
  alarmType: number;

  @Column({
    name: "alarm_time",
    comment: "报警时间",
  })
  alarmTime: number;

  @Column({
    name: "alarm_content",
    comment: "报警内容",
  })
  alarmContent: string;

  @Column({
    name: "location_type",
    comment: "定位类型，具体数值看配置表（GPS，基站，WIFY）",
  })
  locationType: number;

  @Column({
    name: "lat",
    comment: "报警纬度",
  })
  lat: number;

  @Column({
    name: "lon",
    comment: "报警经度",
  })
  lon: number;

  @Column({
    name: "alarm_address",
    comment: "报警地址",
  })
  alarmAddress: string;

  @Column({
    name: "fence_id",
    comment: "围栏Id",
  })
  fenceId: number;

  @Column({
    name: "second_bet_id",
    comment: "二押点Id",
  })
  secondBetId: number;

  @Column({
    name: "handle_status",
    comment: "处理状态， 0 未处理，1 已处理",
  })
  handleStatus: boolean;

  @Column({
    name: "problem_type",
    comment: "问题类型，具体数值看配置表",
  })
  problemType: number;

  @Column({
    name: "handle_remark",
    comment: "处理备注",
  })
  handleRemark: string;

  @Column({
    name: "handle_time",
    comment: "处理时间",
  })
  handleTime: number;

  @Column({
    name: "handle_user",
    comment: "处理人",
  })
  handleUser: string;

  @Column({
    name: "handle_mobile",
    comment: "处理人手机号",
  })
  handleMobile: string;

  @Column({
    name: "create_user",
    comment: "创建人",
  })
  createUser: string;

  @Column({
    name: "create_time",
    comment: "创建时间",
  })
  createdAt: Date;

  @Column({
    name: "generate_time",
    comment: "数据生成时间",
  })
  generateAt: Date;
}
