const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const fenceBusiness = require("../business/fence_business");

/**
 * @api {GET} /fence/getfencelist 围栏管理-获取围栏列表
 * @apiName getfencelist
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-10
 *
 * @apiParam  {String} searchKey 搜索关键字
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getFenceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏管理-获取围栏列表",
  buttonPerm: "FC-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        searchKey: (reqQueryParams.searchKey || "").trim(),
        organId: currentUser.organId,
        isShare: -1,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };

      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const fenceList = await fenceBusiness.getFenceListLogic(queryParams);

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        fenceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getFenceList",
          "获取围栏列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getFenceList",
        "获取围栏列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /fence/insertfence 围栏管理-添加围栏
 * @apiName insertfence
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-11
 *
 * @apiParam  {String} fenceName 围栏名称
 * @apiParam  {Number} shapeType 形状类型
 * @apiParam  {String} shapeData 围栏数据
 * @apiParam  {Number} stopMinutes 停留分钟数
 * @apiParam  {Number} isShare 是否分享
 * @apiParam  {Number} inOut 禁止进出
 * @apiParam  {String} remark 备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertFence = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏管理-添加围栏",
  buttonPerm: "FC-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.organId = currentUser.organId;
      requestData.isBaidu = true;

      const insertCount = await fenceBusiness.insertFenceLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertFence",
        `添加围栏成功 ${insertCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertFence",
          "添加围栏失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertFence",
        "添加围栏出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /fence/updatefence 围栏管理-更新围栏
 * @apiName updatefence
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-12
 *
 * @apiParam  {Number} fenceId 围栏Id
 * @apiParam  {String} fenceName 围栏名称
 * @apiParam  {Number} shapeType 形状类型
 * @apiParam  {String} shapeData 围栏数据
 * @apiParam  {Number} stopMinutes 停留分钟数
 * @apiParam  {Number} isShare 是否分享
 * @apiParam  {Number} inOut 禁止进出
 * @apiParam  {String} remark 备注
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.updateFence = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏管理-更新围栏",
  buttonPerm: "FC-12",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;
      requestData.isBaidu = true;

      const updateCount = await fenceBusiness.updateFenceLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "updateFence",
        `更新围栏成功 ${updateCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "updateFence",
          "更新围栏失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "updateFence",
        "更新围栏出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /fence/deletefence 围栏管理-删除围栏
 * @apiName deletefence
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-13
 *
 * @apiParam  {Number} fenceId 围栏Id
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteFence = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏管理-删除围栏",
  buttonPerm: "FC-13",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      const deleteCount = await fenceBusiness.deleteFenceLogic(requestData);

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteFence",
        `删除围栏成功 ${deleteCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteFence",
          "删除围栏失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteFence",
        "删除围栏出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {GET} /fence/getfencedevicelist 围栏绑定-获取围栏绑定设备列表
 * @apiName getfencedevicelist
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-20
 *
 * @apiParam  {Number} fenceId 围栏Id
 *
 * @apiSuccessExample {json} 响应示例:
 {
    errcode: 0,
    errmsg: "操作成功",
    retobj: [
    ]
  }
 */
exports.getFenceDeviceList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏绑定-获取围栏绑定设备列表",
  buttonPerm: "FC-20",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        fenceId: reqQueryParams.fenceId,
      };

      const fenceDeviceList = await fenceBusiness.getFenceDeviceListLogic(
        queryParams
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        fenceDeviceList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getFenceDeviceList",
          "获取围栏绑定设备列表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getFenceDeviceList",
        "获取围栏绑定设备列表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /fence/insertfencedevice 围栏绑定-添加围栏绑定设备
 * @apiName insertfencedevice
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-21
 *
 * @apiParam  {Number} fenceId 围栏Id
 * @apiParam  {Array} bindData 绑定数据，如下：{ organId 所属车组，imei 设备号，dataClass 数据类别 }
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.insertFenceDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏绑定-添加围栏绑定设备",
  buttonPerm: "FC-21",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      const insertList = requestData.bindData.map((m) => {
        m.fenceId = requestData.fenceId;
        m.userName = currentUser.loginName;
        return m;
      });
      const insertCount = await fenceBusiness.insertFenceDeviceLogic(
        insertList
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "insertFenceDevice",
        `添加围栏绑定设备成功 ${insertCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "insertFenceDevice",
          "添加围栏绑定设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "insertFenceDevice",
        "添加围栏绑定设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /fence/deletefencedevice 围栏绑定-删除围栏绑定的设备
 * @apiName deletefencedevice
 * @apiGroup Fence
 * @apiVersion  1.0.0
 * @apiPermission FC-23
 *
 * @apiParam  {Array} fenceDeviceIds 围栏设备Ids
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 {
      errcode : 0,
      errmsg : '操作成功',
      retobj : ''
   }
 */
exports.deleteFenceDevice = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "围栏绑定-删除围栏绑定的设备",
  buttonPerm: "FC-23",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestData = ctx.request.body;
      requestData.userName = currentUser.loginName;

      const deleteCount = await fenceBusiness.deleteFenceDeviceLogic(
        requestData
      );

      global.context.logger.info(
        currentUser.loginName,
        "USER",
        "deleteFenceDevice",
        `删除围栏绑定的设备成功 ${deleteCount} 行，内容：`,
        JSON.stringify(requestData)
      );

      ctx.body = new RetJson(i18n.SYS_SUCCESS_CODE, i18n.SYS_SUCCESS_MESSAGE);
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "deleteFenceDevice",
          "删除围栏绑定的设备失败，",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "deleteFenceDevice",
        "删除围栏绑定的设备出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
