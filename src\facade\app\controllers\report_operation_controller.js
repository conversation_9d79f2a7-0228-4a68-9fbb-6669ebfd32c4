const xlsx = require("node-xlsx");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const validator = require("../lib/common/validator_extend");
const operationBusiness = require("../business/report_operation_business");
const moment = require("moment");

/**
 * @api {GET} /report/getoperationreportlist 报警记录-获取操作报表
 * @apiName getoperationreportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission ROP-10
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备IMEI
 * @apiParam  {String} operationType 操作类型
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {Number} userId 操作人
 * @apiParam  {Number} pageSize=20 行数
 * @apiParam  {Number} currentPage=0 当前页
 *
 * @apiSuccessExample {json} 响应示例:
  {
    errcode: 0,
    errmsg: "操作成功",
    retobj: {
      total: 9,
      rows: [
      ]
    }
  }
 */
exports.getOperationReportList = {
  method: "GET",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-获取操作报表",
  buttonPerm: "ROP-10",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const reqQueryParams = ctx.request.query;
      const queryParams = {
        organId: validator.isIntFormat(reqQueryParams.organId)
          ? Number.parseInt(reqQueryParams.organId)
          : currentUser.organId,
        hasSubOrgan: validator.isInt01(reqQueryParams.hasSubOrgan)
          ? Number.parseInt(reqQueryParams.hasSubOrgan)
          : 0,
        currentOrganId: currentUser.organId,
        imei: (reqQueryParams.imei || "").trim(),
        operationType: (reqQueryParams.operationType || "").trim(),
        startTime: reqQueryParams.startTime || "",
        endTime: reqQueryParams.endTime || "",
        userId: validator.isIntFormat(reqQueryParams.userId, { min: 1 })
          ? Number.parseInt(reqQueryParams.userId, 10)
          : 0,
        pageSize: validator.isIntFormat(reqQueryParams.pageSize, { min: 1 })
          ? Number.parseInt(reqQueryParams.pageSize, 10)
          : 20,
        currentPage: validator.isIntFormat(reqQueryParams.currentPage, {
          min: 1,
        })
          ? Number.parseInt(reqQueryParams.currentPage, 10)
          : 0,
        offset: 0,
      };
      queryParams.offset = queryParams.currentPage
        ? (queryParams.currentPage - 1) * queryParams.pageSize
        : 0;

      const operationList = await operationBusiness.getOperationListLogic(
        queryParams,
        currentUser
      );

      ctx.body = new RetJson(
        i18n.SYS_SUCCESS_CODE,
        i18n.SYS_SUCCESS_MESSAGE,
        operationList
      );
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "getOperationReportList",
          "获取操作报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "getOperationReportList",
        "获取操作报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};

/**
 * @api {POST} /report/exportgetoperationreportlist 报警记录-导出操作报表
 * @apiName exportgetoperationreportlist
 * @apiGroup Report
 * @apiVersion  1.0.0
 * @apiPermission ROP-11
 *
 * @apiParam  {Number} organId 组织Id
 * @apiParam  {Number} hasSubOrgan 是否包含子组织 0不包含子组织、1包含子组织
 * @apiParam  {String} imei 设备号
 * @apiParam  {String} operationType 操作类型
 * @apiParam  {String} startTime 开始时间，格式：2019-03-18 00:00
 * @apiParam  {String} endTime 开始时间，格式：2019-03-18 00:00
 *
 * @apiSuccessExample {Object} 响应示例:
 HTTP/1.1 200 OK
 */
exports.exportOperationReportList = {
  method: "POST",
  middlewares: ["loginRequired", "permissionRequired"],
  routeDesc: "报警记录-导出操作报表",
  buttonPerm: "ROP-11",
  handler: async (ctx) => {
    const currentUser = ctx.state.user;
    try {
      const requestBody = ctx.request.body;
      const queryParams = {
        organId: validator.isIntFormat(requestBody.organId)
          ? Number.parseInt(requestBody.organId)
          : currentUser.organId,
        currentOrganId: currentUser.organId,
        hasSubOrgan: requestBody.hasSubOrgan ? 1 : 0,
        imei: (requestBody.imei || "").trim(),
        operationType: (requestBody.operationType || "").trim(),
        startTime: requestBody.startTime || "",
        endTime: requestBody.endTime || "",
        pagination: false,
        userId: validator.isIntFormat(requestBody.userId, { min: 1 })
          ? Number.parseInt(requestBody.userId, 10)
          : 0,
      };

      const operationList = await operationBusiness.getOperationListLogic(
        queryParams
      );

      const rows = [];
      rows.push([
        "序号",
        "操作用户",
        "操作时间",
        "操作类型",
        "账户组织",
        "设备组织",
        "设备名称",
        "设备号",
        "车牌号",
        "操作内容",
        "备注"
      ]);

      operationList.rows.forEach((e, i) => {
        rows.push([
          `${i + 1}`,
          e.createUser,
          e.createTime,
          e.operationType,
          e.organName,
          e.deviceOrganName,
          e.deviceName,
          e.imei,
          e.plateNo,
          e.content,
          e.remark,
        ]);
      });

      const result = xlsx.build([{ data: rows }]);
      const fileName = encodeURIComponent(
        `操作报表 - ${moment().format("YYYYMMDDHHmm")}`
      );
      ctx.set("Content-Type", "application/vnd.openxmlformats");
      ctx.set("Content-Disposition", `attachment; filename=${fileName}.xlsx`);
      ctx.set("Access-Control-Expose-Headers", "Content-Disposition");
      ctx.body = Buffer.from(result, "binary");
    } catch (err) {
      if (err.errcode) {
        global.context.logger.warn(
          currentUser.loginName,
          "USER",
          "exportOperationReportList",
          "导出操作报表失败：",
          `ERRORCODE：${err.errcode}，ERRORMSG：${err.errmsg}`
        );
        ctx.body = err;
        return;
      }
      global.context.logger.error(
        currentUser.loginName,
        "MYSQL",
        "exportOperationReportList",
        "导出操作报表出错:",
        err
      );
      ctx.body = new RetJson(i18n.SYS_ERROR_CODE, i18n.SYS_ERROR_MESSAGE);
    }
  },
};
