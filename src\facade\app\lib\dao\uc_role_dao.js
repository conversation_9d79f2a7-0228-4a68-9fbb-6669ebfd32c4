const mysqlDB = require("../common/mysql_pool");

function getRoleSqlList(queryParams) {
  const sql = `select id role_id,role_name,status,remark,create_user,create_time
  from uc_role
  where (:status=-1 or status=:status)
  order by status desc,role_name`;

  return mysqlDB.queryForPagination(sql, queryParams);
}

function getRoleDropDownSqlList(queryParams) {
  const roleIds = queryParams.roleIds || '' ;
  const sql = `select ur.id role_id,ur.role_name,ur.status
  from uc_role ur
  ${
    `${queryParams.organId}` === "0"
      ? ""
      : 'join uc_organ uo on uo.role_ids like concat("%,",ur.id,",%") and uo.id=:organ_id'
  }
  where (:status=-1 or ur.status=:status)
  ${
    `${roleIds}` ? `and ur.id in (${roleIds})` : ""
  }
  order by ur.status desc`;

  return mysqlDB.all(sql, queryParams);
}

function getRoleRecordsSql(queryParams) {
  const sql = `select count(*) record,id role_id
  from uc_role
  where role_name=:role_name`;

  return mysqlDB.one(sql, queryParams);
}

function insertRoleSql(insertData) {
  const sql = `insert into uc_role
  (role_name,status,remark,create_user,create_time)
  values
  (:role_name,:status,:remark,:create_user,now())`;

  return mysqlDB.insert(sql, insertData);
}

function updateRoleSql(updateData) {
  const sql = `update uc_role
  set role_name=:role_name,status=:status,remark=:remark,update_user=:update_user,update_time=now()
  where id=:role_id`;

  return mysqlDB.update(sql, updateData);
}

function getRolePermissionSql(queryParams) {
  const sql = `select up.id per_id,up.parent_id,up.per_name,up.path
  from uc_role_per urp
  join uc_permission up on up.id=urp.per_id and urp.role_id=:role_id
  left join (select distinct parent_id from uc_permission) p1 on urp.per_id=p1.parent_id
  where p1.parent_id is null
  order by up.parent_id,up.id`;

  return mysqlDB.all(sql, queryParams);
}

function saveRolePermissionSql(rolePerData) {
  const sqlTasks = [];

  const deleteSql = "delete from uc_role_per where role_id=:role_id";

  const insertSql =
    "insert into uc_role_per (role_id,per_id) value (:role_id,:per_id)";

  sqlTasks.push({ sql: deleteSql, params: rolePerData });

  rolePerData.objRolePer.forEach((e) => {
    sqlTasks.push({ sql: insertSql, params: e });
  });

  return mysqlDB.executeTransaction(sqlTasks);
}

module.exports = {
  getRoleSqlList,
  getRoleDropDownSqlList,
  getRoleRecordsSql,
  getRolePermissionSql,
  insertRoleSql,
  updateRoleSql,
  saveRolePermissionSql,
};
