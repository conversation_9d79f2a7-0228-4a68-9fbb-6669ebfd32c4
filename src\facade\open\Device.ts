import { Context } from "../../types";
import { injectable, inject } from "inversify";
import { Redis } from "ioredis";
import { In, Repository } from "typeorm";
import { Device } from "../../domain/entity/Device";
import { Organ } from "../../domain/entity/Organ";

/**
 * 授权接口
 */
@injectable()
export default class {
  @inject("redis")
  redis: Redis;

  @inject("deviceRepository")
  deviceRepository: Repository<Device>;

  @inject("organRepository")
  organRepository: Repository<Organ>;

  /**
   * 获取子组织
   * @param id
   */
  async getChildrens(id: number) {
    const res = [id];
    const list = await this.organRepository.find({
      where: {
        parentId: id,
        status: 1,
      },
    });
    if (list.length <= 0) return res;
    const jobs = list.map((item) => this.getChildrens(item.id));
    const childrens = await Promise.all(jobs);
    return res.concat(...childrens);
  }

  /**
   * 创建访问凭据
   * @param ctx
   */
  async index(ctx: Context) {
    const withLocation = ctx.request.query.with_location === "true";
    const job = [];
    const ids = await this.getChildrens(ctx.token.aud);

    // 需要定位信息
    const devices = await this.deviceRepository.find({
      where: {
        organId: In(ids),
        isDelete: false,
      },
    });

    // 生成结果
    ctx.body = devices.map((device) => {
      const data: any = {
        id: device.id,
        plate_no: device.plateNo,
        name: device.name,
        type: device.type,
        sim_no: device.simNo,
        organ: device.organId,
      };

      if (withLocation) {
        job.push(
          (async () => {
            const cache = await this.redis.hgetall(`device:${device.imei}`);
            if (!cache) return;
            data.speed = Number(cache.speed || 0);
            data.lon = Number.parseFloat(cache.lon || "0");
            data.lat = Number.parseFloat(cache.lat || "0");
            data.address = cache.address || "";
          })()
        );
      }

      return data;
    });

    // 等待结果
    if (withLocation) {
      await Promise.all(job);
    }
  }
}
