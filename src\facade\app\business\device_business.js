const moment = require("moment");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");
const { env } = require("process");
const deviceDao = require("../lib/dao/device_dao");
const organDao = require("../lib/dao/uc_organ_dao");
const carBusiness = require("./car_business");

/**
 * 获取设备列表
 * 请求参数:
 "organId": "机构Id",
 "deviceType": "设备类型",
 "deviceModel": "设备型号",
 "onlineState": "在线状态",
 "hasSubOrgan": "是否包含子组织",
 "isSettle": "是否结算"
 *
 */
async function getDeviceListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    {
      field: "organId",
      title: "车组Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }
  const [deviceList, keyValConfig] = await Promise.all([
    deviceDao.getDeviceSqlList(queryParams, currentUser),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "SIM_STATE",
      "ONLINE_STATE",
      "ATTENTION",
    ]),
  ]);

  deviceList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    e.attentionName = keyValConfig.ATTENTION[e.attention] || "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.simStateName = keyValConfig.SIM_STATE[e.simState] || "";
    e.onlineStateName = keyValConfig.ONLINE_STATE[e.onlineState] || "";
    e.policyExpDate = e.policyExpDate
      ? moment.unix(e.policyExpDate).format("YYYY-MM-DD")
      : "";
    e.installDate = e.installDate
      ? moment.unix(e.installDate).format("YYYY-MM-DD")
      : "";
    e.simExpireTime = e.simExpireTime
      ? moment.unix(e.simExpireTime).format("YYYY-MM-DD")
      : "";
    e.lastOnlineTime = e.lastOnlineTime
      ? moment.unix(e.lastOnlineTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.expireTime = e.expireTime
      ? moment.unix(e.expireTime).format("YYYY-MM-DD")
      : "";
    e.expireTime =
      !e.expireTime && e.activeTime
        ? moment.unix(e.activeTime).add(1, "year").format("YYYY-MM-DD")
        : e.expireTime;
    // // 有线设备: 激活时间+1年
    // const deviceTypePatformExpirationTimeArr = {
    //   2: 2,
    //   4: 2,
    // };
    // if (e.activeTime && deviceTypePatformExpirationTimeArr[e.deviceType]) {
    // 有线设备: 设备已激活
    e.patformExpirationTime = e.patformExpirationTime
      ? moment(e.patformExpirationTime).format("YYYY-MM-DD")
      : "";
    e.patformExpirationTime =
      !e.patformExpirationTime && e.activeTime
        ? moment.unix(e.activeTime).add(1, "year").format("YYYY-MM-DD")
        : e.patformExpirationTime;

    // } else if (!deviceTypePatformExpirationTimeArr[e.deviceType]) {
    //   // 无线设备
    //   e.patformExpirationTime = "2050-12-31 23:59:59";
    // }
    e.activeTime = e.activeTime
      ? moment.unix(e.activeTime).format("YYYY-MM-DD HH:mm")
      : "";
    commFunc.formatNullToString(e);
  });

  return deviceList;
}

/**
 * 获取渣土设备列表
 * 请求参数:
 "organId": "机构Id",
 "onlineState": "在线状态",
 "hasSubOrgan": "是否包含子组织",
 *
 */
async function getBigCarDeviceListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "车组Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [deviceList, keyValConfig] = await Promise.all([
    deviceDao.getBigCarDeviceSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "ONLINE_STATE",
    ]),
  ]);

  for (const e of deviceList.rows) {
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.onlineStateName = keyValConfig.ONLINE_STATE[e.onlineState] || "";
    e.lastOnlineTime = e.lastOnlineTime
      ? moment.unix(e.lastOnlineTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.deviceState = e.onlineState;

    const deviceCache = await global.context.redisClient.hgetall(
      `device:${e.imei}`
    );
    if (deviceCache) {
      e["satellite"] = deviceCache.satellite || "";
      e["bigCarSpeed"] = deviceCache.bigCarSpeed || "";
      e["bigCarRSpeed"] = deviceCache.bigCarRSpeed || "";
      e["bigCarMileage"] = deviceCache.bigCarMileage || "";
      e["bigCarTotalMileage"] = deviceCache.bigCarTotalMileage || "";
      e["bigCarOilPos"] = deviceCache.bigCarOilPos || "";
      e["bigCarOilPower"] = deviceCache.bigCarOilPower || "";
      e["bigCarOpTorque"] = deviceCache.bigCarOpTorque || "";
      e["bigCarActualTorque"] = deviceCache.bigCarActualTorque || "";
      e["bigCarNo"] = deviceCache.bigCarNo || "";
      e["bigCarDriverId"] = deviceCache.bigCarDriverId || "";
      e["bigCarStatus"] = deviceCache.bigCarStatus || "";
      e["bigCarCarriageState"] = deviceCache.bigCarCarriageState || "";
      e["bigCarLiftState"] = deviceCache.bigCarLiftState || "";
      e["bigCarWeightState"] = deviceCache.bigCarWeightState || "";
      e["bigCarRuleState"] = deviceCache.bigCarRuleState || "";
      e["bigCarStateTime"] = deviceCache.bigCarStateTime
        ? moment.unix(deviceCache.bigCarStateTime).format("YYYY-MM-DD HH:mm:ss")
        : "";
      e["bigCarEvent"] = deviceCache.bigCarEvent || "";
      e["bigCarEventTime"] = deviceCache.bigCarEventTime
        ? moment.unix(deviceCache.bigCarEventTime).format("YYYY-MM-DD HH:mm:ss")
        : "";
      e["bigCarOil"] = deviceCache.bigCarOil || "";
      e["bigCarLiftControlState"] = deviceCache.bigCarLiftControlState || "";
      e["bigCarCanState"] = deviceCache.bigCarCanState || "";
      e["bigCarClosedState"] = deviceCache.bigCarClosedState || "";
    }

    commFunc.formatNullToString(e);
  }

  return deviceList;
}

/**
 * 获取设备数据
 * 请求参数:
 "organId": "机构Id",
 "imei": "imei"
 *
 */
async function getDeviceDataLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "车组Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "", required: true },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }
  const [deviceData, keyValConfig] = await Promise.all([
    deviceDao.getDeviceSqlData(queryParams),
    global.context.getPropertymapKeyVal(["DEVICE_TYPE", "DEVICE_MODEL"]),
  ]);

  if (!deviceData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  deviceData.deviceTypeName =
    keyValConfig.DEVICE_TYPE[deviceData.deviceType] || "";
  deviceData.deviceModelName =
    keyValConfig.DEVICE_MODEL[deviceData.deviceModel] || "";

  commFunc.formatNullToString(deviceData);
  return deviceData;
}

/**
 * 获取关注设备列表
 * 请求参数:
 "organId": "当前车组Id"
 *
 */
async function getAttentionDeviceListLogic(queryParams) {
  const [deviceList, keyValConfig, nameKeyConfig] = await Promise.all([
    deviceDao.getAttentionDeviceSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "LOCATION_TYPE",
      "ONLINE_STATE",
      "ALARM_TYPE",
      "ATTENTION",
    ]),
    global.context.getPropertymapNameKey(["ONLINE_STATE", "SIM_STATE"]),
  ]);

  for (const e of deviceList.rows) {
    e.attentionName = keyValConfig.ATTENTION[e.attention] || "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.alarmTypeName = keyValConfig.ALARM_TYPE[e.alarmType] || "";

    commFunc.formatNullToString(e);
    e.speed = "";
    e.electric = "";
    e.locationType = 0;
    e.lon = 0;
    e.lat = 0;
    e.currentAddress = "";

    let statusTime = 0;
    let statePeriod = 0;
    e.deviceState = e.onlineState;

    const deviceCache = await global.context.redisClient.hgetall(
      `device:${e.imei}`
    );
    if (deviceCache) {
      e.deviceState = e.onlineState = deviceCache.onlineState || e.onlineState;
      e.lastLocationTime = deviceCache.locTime || e.lastLocationTime;

      statusTime = deviceCache.statusTime || 0;
      statePeriod = Number.parseInt(`${deviceCache.stopPeriod}` || "0", 10);

      e.speed = deviceCache.speed || "";
      e.electric = deviceCache.electricity || "";
      e.locationType = deviceCache.locType || 0;
      e.lastSignalTime = deviceCache.signalTime || "";

      e.lon = Number.parseFloat(deviceCache.lon || "0");
      e.lat = Number.parseFloat(deviceCache.lat || "0");
    }

    if (`${e.onlineState}` === nameKeyConfig.ONLINE_STATE.ONLINE) {
      e.lastOnlineTime = statusTime || "";
      // 0 表示运动，1 表示静止
      if (deviceCache && `${deviceCache.stopFlag}` === "0") {
        statePeriod = moment().format("X") - e.lastOnlineTime;
      } else {
        e.deviceState = 10;
      }
    }

    if (`${e.onlineState}` === nameKeyConfig.ONLINE_STATE.OFFLINE) {
      e.lastOfflineTime = statusTime || "";
      statePeriod = moment().format("X") - e.lastOfflineTime;
    }

    // deviceState，0 从未上线，1 在线，2 离线，10 静止，100 已到期
    e.onlineStateName = keyValConfig.ONLINE_STATE[e.onlineState] || "";
    e.shortStateName = `${commFunc.secondsToShortHumanize(
      statePeriod
    )}`;
    e.deviceStateName = `${commFunc.secondsToHumanize(statePeriod)}`;
    e.offlineLong =
      `${e.onlineState}` === nameKeyConfig.ONLINE_STATE.OFFLINE
        ? e.deviceStateName
        : "";

    if (`${e.simState}` === nameKeyConfig.SIM_STATE.GQ) {
      e.deviceState = 100;
      e.shortStateName = "已到期";
    }

    e.locationTypeName = keyValConfig.LOCATION_TYPE[e.locationType] || "";

    e.lastLocationTime = e.lastLocationTime
      ? moment.unix(e.lastLocationTime).format("YYYY-MM-DD HH:mm")
      : "";

    e.lastOnlineTime = e.lastOnlineTime
      ? moment.unix(e.lastOnlineTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.lastSignalTime = e.lastSignalTime
      ? moment.unix(e.lastSignalTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.lastOfflineTime = e.lastOfflineTime
      ? moment.unix(e.lastOfflineTime).format("YYYY-MM-DD HH:mm")
      : "";

    if (deviceCache.hasOwnProperty("ACC")) {
      e.ACC = deviceCache.ACC;
    }
  }
  deviceList.rows.forEach((row, i) => {
    row.index = queryParams.offset + i + 1;
  });
  return deviceList;
}

/**
 * 获取车组设备列表
 * 请求参数:
 "organId": "车组Id",
 "searchKey": "",
 "deviceState": "",
 "hasSubOrgan": "包含子组",
 "currentOrganId": "当前车组Id"
 *
 */
async function getOrganDeviceListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  queryParams.isAttention = queryParams.organId === "-10" ? 1 : 0;
  queryParams.hasSubOrgan = queryParams.hasSubOrgan === "true" ? 1 : 0;

  if (
    queryParams.isAttention !== 1 &&
    !validator.isIntFormat(queryParams.organId, { min: 1 })
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  // 使用缓存优化配置数据获取
  const cacheKey = 'device_config_cache';
  let configCache = await global.context.redisClient.get(cacheKey);
  let nameKeyConfig, keyValConfig, keyIdenConfig;

  if (configCache) {
    const parsedCache = JSON.parse(configCache);
    nameKeyConfig = parsedCache.nameKeyConfig;
    keyValConfig = parsedCache.keyValConfig;
    keyIdenConfig = parsedCache.keyIdenConfig;
  } else {
    // 缓存不存在，从数据库获取并缓存
    [nameKeyConfig, keyValConfig, keyIdenConfig] = await Promise.all([
      context.getPropertymapNameKey(["ONLINE_STATE", "SIM_STATE"]),
      global.context.getPropertymapKeyVal([
        "DEVICE_TYPE",
        "DEVICE_MODEL",
        "LOCATION_TYPE",
        "SIM_STATE",
        "ONLINE_STATE",
        "ALARM_TYPE",
      ]),
      global.context.getPropertymapKeyIden(["ONLINE_STATE", "DEVICE_TYPE"]),
    ]);

    // 缓存配置数据 5 分钟
    const cacheData = { nameKeyConfig, keyValConfig, keyIdenConfig };
    await global.context.redisClient.setex(cacheKey, 300, JSON.stringify(cacheData));
  }

  const onlineState =
    nameKeyConfig.ONLINE_STATE[
    (queryParams.deviceState || "").replace("ONLINE_STATE.", "")
    ];
  queryParams.onlineState = onlineState === undefined ? -1 : onlineState;
  queryParams.online = nameKeyConfig.ONLINE_STATE.ONLINE;
  queryParams.offline = nameKeyConfig.ONLINE_STATE.OFFLINE;
  queryParams.notuse = nameKeyConfig.ONLINE_STATE.NOTUSE;

  if (!validator.isIntFormat(queryParams.onlineState, { min: -1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_DATA_ERROR);
  }

  const [deviceList, deviceStateCount] = await Promise.all([
    deviceDao.getOrganDeviceSqlList(queryParams, currentUser),
    deviceDao.getOrganDeviceStaSqlList(queryParams, currentUser),
  ]);

  // 批量获取设备缓存数据，优化性能
  const deviceCacheMap = new Map();
  const imeiList = deviceList.rows.map(e => e.imei);

  // 批量获取 Redis 缓存
  if (imeiList.length > 0) {
    const pipeline = global.context.redisClient.pipeline();
    imeiList.forEach(imei => {
      pipeline.hgetall(`device:${imei}`);
    });

    try {
      const cacheResults = await pipeline.exec();
      imeiList.forEach((imei, index) => {
        if (cacheResults[index] && cacheResults[index][1]) {
          deviceCacheMap.set(imei, cacheResults[index][1]);
        }
      });
    } catch (err) {
      global.context.logger.warn('批量获取设备缓存失败:', err);
    }

    // 如果启用了旧 Redis 同步，批量处理缺失的缓存
    if (env.OLD_REDIS_SYNC === 'true') {
      const missingCacheImeis = [];
      imeiList.forEach(imei => {
        const cache = deviceCacheMap.get(imei);
        if (!cache || !cache.lat || !cache.lon) {
          missingCacheImeis.push(imei);
        }
      });

      if (missingCacheImeis.length > 0) {
        try {
          const oldPipeline = global.context.oldRedisClient.pipeline();
          missingCacheImeis.forEach(imei => {
            oldPipeline.hgetall(`device:${imei}`);
          });

          const oldCacheResults = await oldPipeline.exec();
          const updatePipeline = global.context.redisClient.pipeline();

          missingCacheImeis.forEach((imei, index) => {
            if (oldCacheResults[index] && oldCacheResults[index][1] &&
              oldCacheResults[index][1].lat && oldCacheResults[index][1].lon) {
              const oldCache = oldCacheResults[index][1];
              deviceCacheMap.set(imei, oldCache);
              updatePipeline.hsetall(`device:${imei}`, oldCache);
            }
          });

          if (updatePipeline.length > 0) {
            await updatePipeline.exec();
          }
        } catch (err) {
          global.context.logger.warn('同步旧缓存异常:', err);
        }
      }
    }
  }

  // 预计算常用值，避免在循环中重复计算
  const currentTimestamp = moment().format("X");
  const overtimeThreshold = moment().add(-5, "minutes").valueOf() / 1000;
  const deviceStateTimeOffset = parseInt(env.DEVICE_STATE_TIME) || 0;

  for (const e of deviceList.rows) {
    // 基础字段映射 - 批量处理
    Object.assign(e, {
      deviceTypeName: keyValConfig.DEVICE_TYPE[e.deviceType] || "",
      deviceTypeIden: keyIdenConfig.DEVICE_TYPE[e.deviceType] || "",
      deviceModelName: keyValConfig.DEVICE_MODEL[e.deviceModel] || "",
      simStateName: keyValConfig.SIM_STATE[e.simState] || "",
      alarmTypeName: keyValConfig.ALARM_TYPE[e.alarmType] || "",
      // 初始化默认值
      speed: "",
      electric: "",
      locationType: 0,
      lon: 0,
      lat: 0,
      currentAddress: ""
    });

    // 日期格式化 - 优化条件判断
    e.policyExpDate = e.policyExpDate ? moment.unix(e.policyExpDate).format("YYYY-MM-DD") : "";
    e.installDate = e.installDate ? moment.unix(e.installDate).format("YYYY-MM-DD") : "";
    e.simExpireTime = e.simExpireTime ? moment.unix(e.simExpireTime).format("YYYY-MM-DD") : "";
    e.createTime = e.createTime ? moment(e.createTime).format("YYYY-MM-DD HH:mm") : "";

    // 过期时间处理
    if (e.expireTime) {
      e.expireTime = moment.unix(e.expireTime).format("YYYY-MM-DD HH:mm");
    } else if (e.activeTime) {
      e.expireTime = moment.unix(e.activeTime).add(1, "year").format("YYYY-MM-DD HH:mm");
    } else {
      e.expireTime = "";
    }

    e.activeTime = e.activeTime ? moment.unix(e.activeTime).format("YYYY-MM-DD HH:mm") : e.expireTime;

    commFunc.formatNullToString(e);

    let statusTime = 0;
    let statePeriod = 0;
    e.deviceState = e.onlineState;

    // 从缓存 Map 中获取设备缓存数据
    const deviceCache = deviceCacheMap.get(e.imei);

    if (deviceCache) {
      e.deviceState = e.onlineState = deviceCache.onlineState || e.onlineState;
      e.lastLocationTime = deviceCache.locTime || e.lastLocationTime;

      statusTime = deviceCache.statusTime || 0;
      statePeriod = Number.parseInt(`${deviceCache.stopPeriod}` || "0", 10);

      e.speed = deviceCache.speed || "";
      e.electric = deviceCache.electricity || "";
      e.locationType = deviceCache.locType || 0;
      e.lastSignalTime = deviceCache.signalTime || "";
      e.direct = deviceCache.direct || 0;
      e.signals = deviceCache.signals || 0;
      e.outVoltage = deviceCache.outVoltage || "";
      e.lon = Number.parseFloat(deviceCache.lon || "0");
      e.lat = Number.parseFloat(deviceCache.lat || "0");
      if (deviceCache.hasOwnProperty("ACC")) {
        e.ACC = deviceCache.ACC;
      }

      for (let i = 1; i <= 12; i++) {
        e[`statusInfo_${i}`] = deviceCache[`statusInfo_${i}`] || "";
      }

      if (
        deviceCache.bigCarSpeed !== undefined ||
        deviceCache.bigCarStateTime !== undefined ||
        deviceCache.bigCarCarriageState !== undefined
      ) {
        e.bigCar = {
          bigCarSpeed: deviceCache.bigCarSpeed || "",
          bigCarRSpeed: deviceCache.bigCarRSpeed || "",
          bigCarMileage: deviceCache.bigCarMileage || "",
          bigCarTotalMileage: deviceCache.bigCarTotalMileage || "",
          bigCarOilPos: deviceCache.bigCarOilPos || "",
          bigCarOilPower: deviceCache.bigCarOilPower || "",
          bigCarOpTorque: deviceCache.bigCarOpTorque || "",
          bigCarActualTorque: deviceCache.bigCarActualTorque || "",
          bigCarNo: deviceCache.bigCarNo || "",
          bigCarDriverId: deviceCache.bigCarDriverId || "",
          bigCarStatus: deviceCache.bigCarStatus || "",
          bigCarCarriageState: deviceCache.bigCarCarriageState || "",
          bigCarLiftState: deviceCache.bigCarLiftState || "",
          bigCarWeightState: deviceCache.bigCarWeightState || "",
          bigCarRuleState: deviceCache.bigCarRuleState || "",
          bigCarStateTime: deviceCache.bigCarStateTime
            ? moment
              .unix(deviceCache.bigCarStateTime)
              .format("YYYY-MM-DD HH:mm:ss")
            : "",
          bigCarEvent: deviceCache.bigCarEvent || "",
          bigCarEventTime: deviceCache.bigCarEventTime
            ? moment
              .unix(deviceCache.bigCarEventTime)
              .format("YYYY-MM-DD HH:mm:ss")
            : "",
          bigCarOil: deviceCache.bigCarOil || "",
          bigCarLiftControlState: deviceCache.bigCarLiftControlState || "",
          bigCarCanState: deviceCache.bigCarCanState || "",
          bigCarClosedState: deviceCache.bigCarClosedState || "",
        };
      }
    }

    // 优化在线状态处理逻辑
    const onlineStateStr = `${e.onlineState}`;
    if (onlineStateStr === nameKeyConfig.ONLINE_STATE.ONLINE) {
      e.lastOnlineTime = statusTime || "";
      // 0 表示运动，1 表示静止
      if (deviceCache && deviceCache.stopFlag === "0") {
        statePeriod = e.lastOnlineTime ? currentTimestamp - e.lastOnlineTime : 0;
      } else {
        e.deviceState = 10;
      }
      // 信号时间 < 当前时间 5分钟, 修改 1.'在线状态'为离线, 2.设备状态为离线
      if (deviceCache && deviceCache.signalTime > 0 && deviceCache.signalTime < overtimeThreshold) {
        e.onlineState = nameKeyConfig.ONLINE_STATE.OFFLINE;
        e.deviceState = nameKeyConfig.ONLINE_STATE.OFFLINE;
      }
    } else if (onlineStateStr === nameKeyConfig.ONLINE_STATE.OFFLINE) {
      e.lastOfflineTime = statusTime || "";
      statePeriod = e.lastOfflineTime ? currentTimestamp - e.lastOfflineTime : 0;
    }

    // 添加误差时间, 条件: 时间 大于 env.DEVICE_STATE_TIME
    if (e.deviceState === 2) {
      statePeriod += deviceStateTimeOffset;
    }

    // deviceState，0 从未上线，1 在线，2 离线，10 静止，100 已到期
    e.onlineStateName = keyValConfig.ONLINE_STATE[e.onlineState] || "";
    e.onlineStateIden = keyIdenConfig.ONLINE_STATE[e.onlineState] || "";
    e.shortStateName = `${commFunc.secondsToShortHumanize(
      statePeriod
    )}`;
    e.deviceStateName = `${commFunc.secondsToHumanize(statePeriod)}`;

    if (`${e.simState}` === nameKeyConfig.SIM_STATE.GQ) {
      e.deviceState = 100;
      e.shortStateName = "已到期";
    }

    e.locationTypeName = keyValConfig.LOCATION_TYPE[e.locationType] || "";

    e.lastLocationTime = e.lastLocationTime
      ? moment.unix(e.lastLocationTime).format("YYYY-MM-DD HH:mm")
      : "";

    e.lastOnlineTime = e.lastOnlineTime
      ? moment.unix(e.lastOnlineTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.lastSignalTime = e.lastSignalTime
      ? moment.unix(e.lastSignalTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.lastOfflineTime = e.lastOfflineTime
      ? moment.unix(e.lastOfflineTime).format("YYYY-MM-DD HH:mm")
      : "";
  }

  deviceList.rows.forEach((row, i) => {
    row.index = i + 1;
  });

  return [deviceList, deviceStateCount];
}

/**
 * 获取设备信息下拉列表
 * 请求参数:
 "organId": "机构Id",
 "searchKey": "搜索关键词"
 *
 */
async function getDeviceInfoListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await deviceDao.getDeviceInfoSqlList(queryParams);
}

/**
 * 获取设备在线状态数量
 * 请求参数:
 "organId": "机构Id"
 *
 */
async function getDeviceStateCountLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "ONLINE_STATE",
  ]);
  queryParams.online = nameKeyConfig.ONLINE_STATE.ONLINE;
  queryParams.offline = nameKeyConfig.ONLINE_STATE.OFFLINE;
  queryParams.notuse = nameKeyConfig.ONLINE_STATE.NOTUSE;

  return await deviceDao.getDeviceStateCountSql(queryParams);
}

/**
 * 获取设备下拉列表
 * 请求参数:
 "organId": "机构Id",
 "searchKey": "搜索关键词",
 "hasSubOrgan": "是否包含子组织",
 *
 */
async function getDeviceDropdownListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  return await deviceDao.getDeviceDropdownSqlList(queryParams);
}

/**
 * 设备数据验证
 *
 */
async function _validateDeviceRequest(requestData) {
  const rules = [
    {
      field: "deviceId",
      title: "设备Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    },
    {
      field: "organId",
      title: "所属车组",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "i",
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "", required: true },
    {
      field: "plateNo",
      title: "车牌号",
      rule: "isCarPlateNo",
      msg: "",
      required: true,
    },
    {
      field: "deviceModel",
      title: "设备型号",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "installDate",
      title: "安装日期",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDD" }],
      msg: "",
    },
    {
      field: "expireTime",
      title: "客户到期时间",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDD" }],
      msg: "",
    },
    {
      field: "simNo",
      title: "SIM卡号",
      rule: "isSimNo",
      msg: "",
      required: true,
    },
    { field: "iccid", title: "ICCID", rule: "isICCID", msg: "" },

    {
      field: "policyExpDate",
      title: "保单过期日期",
      rule: [{ name: "isDateFormat", opt: "YYYYMMDD" }],
      msg: "",
    },
    { field: "ownerPhone", title: "车主手机", rule: "isInt11", msg: "" },
  ];

  let [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  const [carData, keyValConfig] = await Promise.all([
    carBusiness.getCarDataLogic({
      plateNo: requestData.plateNo,
      organId: requestData.currentOrganId,
    }),
    global.context.getPropertymapKeyVal(["DEVICE_MODEL_TYPE"]),
  ]);
  if (carData) {
    model.carId = carData.carId;
  }
  model.deviceName = model.deviceName || "";
  model.deviceType =
    keyValConfig.DEVICE_MODEL_TYPE[`${model.deviceModel}`] || 0;
  commFunc.formatNullToString(model);
  model.installDate = model.installDate
    ? moment(model.installDate).format("X")
    : 0;
  model.expireTime = model.expireTime
    ? moment(model.expireTime).format("X")
    : 0;
  model.policyExpDate = model.policyExpDate
    ? moment(model.policyExpDate).format("X")
    : 0;
  model.patformExpirationTime = model.patformExpirationTime || null;
  model.isAlarmPhone = model.isAlarmPhone || 0;
  model.isAlarmSms = model.isAlarmSms || 0;
  model.isAlarmWechat = model.isAlarmWechat || 0;
  model.alarmPhone = model.alarmPhone || "";
  model.alarmSms = model.alarmSms || "";
  model.alarmWechatName = model.alarmWechatName || "";
  model.alarmWechatMsgId = model.alarmWechatMsgId || "";
  model.alarmPhoneTypes = model.alarmPhoneTypes || "";
  model.alarmSmsTypes = model.alarmSmsTypes || "";
  model.alarmWechatTypes = model.alarmWechatTypes || "";

  return [undefined, model];
}

/**
 * 新增设备
 * 请求参数:
 "organId": "机构Id",
 "imei": "设备号",
 "deviceName": "设备名称",
 "deviceModel": "设备型号",
 "installLocation": "安装位置",
 "installer": "安装人",
 "installDate": "安装日期",
 "expireTime": "客户到期时间",
 "simNo": "sim卡号",
 "iccid": "iccid",
 "servicePswd": "服务密码",
 "deviceRemark": "设备备注",
 "isUpdateCar": "是否更新车辆信息",

 "plateNo": "车牌号",
 "vinNo": "车架号",
 "engineNo": "发动机号",
 "policyNo": "保单号",
 "policyExpDate": "保单过期日期",
 "carBrand": "车品牌",
 "carModel": "车型号",
 "carOwner": "车主",
 "ownerPhone": "车主手机",
 "carRemark": "备注",

 "userName": "操作人"
 *
 */
async function insertDeviceLogic(insertData, currentUser) {
  insertData.opt = "i";
  const [err, model] = await _validateDeviceRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [isExists, nameKeyConfig] = await Promise.all([
    deviceDao.getDeviceRecordsSql(model),
    global.context.getPropertymapNameKey(["DEVICE_MODEL"]),
  ]);
  if (isExists.record) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  const isEncode = [
    nameKeyConfig.DEVICE_MODEL.BQ03B,
    nameKeyConfig.DEVICE_MODEL.BQ03F,
    nameKeyConfig.DEVICE_MODEL.BQ03W,
    nameKeyConfig.DEVICE_MODEL.BQ03A,
    nameKeyConfig.DEVICE_MODEL.BQ03D,
  ].includes(`${model.deviceModel}`);

  const encodeImei = commFunc.encodeImei(
    model.imei.substr(model.imei.length - 11)
  );
  model.encrytedImei = isEncode ? encodeImei : model.imei;

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "新增设备",
    plateNo: model.plateNo,
    imei: model.imei,
    content: `新增设备 车牌${model.plateNo}, IMEI：${model.imei} `,
  };
  return await deviceDao.insertDeviceSql(model, operationModel);
}

/**
 * 更新设备
 * 请求参数:
 "organId": "机构Id",
 "deviceId": "设备Id",
 "imei": "设备号",
 "deviceName": "设备名称",
 "deviceModel": "设备型号",
 "installLocation": "安装位置",
 "installer": "安装人",
 "installDate": "安装日期",
 "expireTime": "客户到期时间",
 "simNo": "sim卡号",
 "iccid": "iccid",
 "servicePswd": "服务密码",
 "deviceRemark": "设备备注",
 "isUpdateCar": "是否更新车辆信息",

 "plateNo": "车牌号",
 "vinNo": "车架号",
 "engineNo": "发动机号",
 "policyNo": "保单号",
 "policyExpDate": "保单过期日期",
 "carBrand": "车品牌",
 "carModel": "车型号",
 "carOwner": "车主",
 "ownerPhone": "车主手机",
 "carRemark": "备注",

 "userName": "操作人"
 "patformExpirationTime": "平台到期时间"
 *
 */
async function updateDeviceLogic(updateData, currentUser) {
  updateData.opt = "u";
  const [err, model] = await _validateDeviceRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await deviceDao.getDeviceRecordsSql(model);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_OVERTWO_EXISTS);
  }
  if (
    Number.parseInt(model.deviceId, 10) !== isExists.deviceId &&
    isExists.record
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType:
      updateData.deviceName !== isExists.deviceName ? "设备改名" : "编辑设备",
    plateNo: updateData.plateNo,
    imei: updateData.imei,
    content:
      updateData.deviceName !== isExists.deviceName
        ? `设备改名 IMEI：${updateData.imei} 从 ${isExists.deviceName} 改为 ${updateData.deviceName}`
        : `编辑设备信息 设备名称：${updateData.deviceName}, IMEI：${updateData.imei} `,
    patformExpirationTime: updateData.patformExpirationTime || null,
  };
  return await deviceDao.updateDeviceSql(model, operationModel);
}

/**
 * 删除设备
 * 请求参数:
 "deviceIds": "设备Ids"
 *
 */
async function deleteDeviceLogic(deleteData, currentUser) {
  if (
    !validator.isArray(deleteData.deviceIds, { min: 1 }, [
      { rule: "isIntFormat", opt: { min: 1 }, required: true },
    ])
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "删除设备",
    plateNo: "",
    imei: "",
  };
  return await deviceDao.deleteDeviceSql(deleteData, operationModel);
}

/**
 * 更改设备车组
 * 请求参数:
 "organId": "机构Id",
 "currentOrganId": "当前机构Id",
 *
 */
async function updateDeviceOrganLogic(updateData, currentUser) {
  if (!validator.isIntFormat(updateData.organId, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  if (
    !validator.isArray(updateData.imeis, { min: 1 }, [
      { rule: "isIMEI", required: true },
    ])
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_FIELD_ERROR);
  }

  const organData = await organDao.getOrganParentSqlData({
    organId: updateData.organId,
    parentId: updateData.organId,
  });
  if (!organData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_FIELD_ERROR);
  }

  updateData.imeis = updateData.imeis.map((m) => `'${m}'`);
  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: organData.organId,
    organName: organData.organName,
    operationType: "设备改组",
    plateNo: "",
    imei: "",
  };
  return await deviceDao.updateDeviceOrganSql(updateData, operationModel);
}

/**
 * 获取离线设备列表
 * 请求参数:
 "organId": "机构Id",
 "deviceType": "设备类型",
 "startSecond": "开始秒",
 "endSecond": "结束秒"
 *
 */
async function getOfflineDeviceListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    {
      field: "deviceType",
      title: "设备类型",
      rule: [{ name: "isIntFormat", opt: { min: -1, max: 2 } }],
      msg: "",
      required: true,
    },
    {
      field: "startSecond",
      title: "开始时间",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
    {
      field: "endSecond",
      title: "结束时间",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  // 计算离线时间区间值，如果传过来的是 大于30天 这样的值，如 startSecond:30*3600*24，endSecond:0，则startTime设置为0
  queryParams.startTime = moment().format("X") - queryParams.endSecond;
  queryParams.endTime = moment().format("X") - queryParams.startSecond;
  if (`${queryParams.endSecond}` === "0") {
    queryParams.startTime = 0;
    queryParams.endTime = moment().format("X") + queryParams.startSecond;
  }

  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "ONLINE_STATE",
  ]);
  queryParams.onlineState = nameKeyConfig.ONLINE_STATE.OFFLINE;
  const [deviceList, keyValConfig] = await Promise.all([
    deviceDao.getOfflineDeviceSqlList(queryParams),
    global.context.getPropertymapKeyVal([
      "DEVICE_TYPE",
      "DEVICE_MODEL",
      "ATTENTION",
    ]),
  ]);

  deviceList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.attentionName = keyValConfig.ATTENTION[e.attention] || "";
    e.currentAddress = "";
    e.offlineLong = e.lastOfflineTime
      ? commFunc.secondsToHumanize(moment().format("X") - e.lastOfflineTime)
      : "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.lastOnlineTime = e.lastOnlineTime
      ? moment.unix(e.lastOnlineTime).format("YYYY-MM-DD HH:mm")
      : "";
  });

  return deviceList;
}

/**
 * 获取同一台车多个设备
 * 请求参数:
 "organId": "机构Id",
 "plateNo": "车牌号",
 "imei": "设备号"
 *
 */
async function getSameCarDeviceListLogic(queryParams) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    { field: "plateNo", title: "车牌号", rule: "", msg: "", required: true },
    { field: "imei", title: "设备号", rule: "", msg: "", required: true },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  queryParams.imei = queryParams.plateNo === "粤B00000" ? queryParams.imei : "";
  queryParams.plateNo =
    queryParams.plateNo === "粤B00000" ? "" : queryParams.plateNo;

  const [deviceList, keyValConfig] = await Promise.all([
    deviceDao.getSameCarDeviceSqlList(queryParams),
    global.context.getPropertymapKeyVal(["DEVICE_TYPE", "DEVICE_MODEL"]),
  ]);

  deviceList.forEach((e) => {
    commFunc.formatNullToString(e);
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
  });

  return deviceList;
}

/**
 * 更新车辆关注度
 * 请求参数:
 "organId": "机构Id",
 "imei": "设备号",
 "plateNo": "车牌",
 "attention": "关注度",
 "attentionRemark": "关注备注",
 *
 */
async function updateDeviceAttentionLogic(updateData) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "", required: true },
    {
      field: "attention",
      title: "关注度",
      rule: [{ name: "isIntFormat", opt: { min: 0 } }],
      msg: "",
      required: true,
    },
  ];

  const [err, updateModel] = commFunc.checkRequestData(updateData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  updateModel.plateNo = updateModel.plateNo || "";
  updateModel.imei = updateModel.imei || "";
  updateModel.plateNo = updateModel.plateNo === "粤B00000" ? "" : updateModel.plateNo;

  updateModel.attentionRemark = updateModel.attentionRemark || "";

  return await deviceDao.updateDeviceAttentionSql(updateModel);
}

/**
 * 获取车辆结清列表
 * 请求参数:
 "organId": "机构Id",
 "imei": "设备号",
 "hasSubOrgan": "是否包含子组织",
 "isSettle": "是否结清"
 *
 */
async function getCarSettleListLogic(queryParams, currentUser) {
  currentUser = currentUser || {}
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: -1 } }],
      msg: "",
      required: true,
    },
    { field: "imei", title: "设备号", rule: "isIMEI", msg: "" },
    {
      field: "isSettle",
      title: "是否结清",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "hasSubOrgan",
      title: "是否包含子组织",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [deviceList, keyValConfig] = await Promise.all([
    deviceDao.getCarSettleSqlList(queryParams, currentUser),
    global.context.getPropertymapKeyVal(["DEVICE_TYPE", "DEVICE_MODEL"]),
  ]);

  deviceList.rows.forEach((e) => {
    commFunc.formatNullToString(e);
    e.settleTime = e.settleTime
      ? moment(e.settleTime, "YYYYMMDDHHmmss").format("YYYY-MM-DD HH:mm")
      : "";
    e.deviceTypeName = keyValConfig.DEVICE_TYPE[e.deviceType] || "";
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
  });

  return deviceList;
}

/**
 * 车辆结清
 * 请求参数:
 "organId": "机构Id",
 "plateNo": "车牌",
 "isSettle": "是否结清",
 "settleRemark": "结清备注",
 *
 */
async function updateCarSettleLogic(updateData, currentUser) {
  const rules = [
    {
      field: "organId",
      title: "机构Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    { field: "plateNo", title: "车牌号", rule: "", msg: "", required: true },
    { field: "imei", title: "设备号", rule: "", msg: "", required: true },
    {
      field: "isSettle",
      title: "结清",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err, updateModel] = commFunc.checkRequestData(updateData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  updateModel.imei = updateModel.plateNo === "粤B00000" ? updateModel.imei : "";
  updateModel.plateNo =
    updateModel.plateNo === "粤B00000" ? "" : updateModel.plateNo;
  updateModel.settleRemark = updateModel.settleRemark || "";

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: updateData.isSettle === 1 ? "结清" : "撤消结清",
    plateNo: updateData.plateNo,
    imei: updateData.imei,
    content: `车辆： ${updateData.plateNo}, ${updateData.imei},  ${updateData.isSettle === 1 ? "结清" : "撤消结清"
      }`,
  };
  return await deviceDao.updateCarSettleSql(updateModel, operationModel);
}

/**
 * 获取导入设备列表
 * 请求参数:
 "isValidate": "验证结果",
 "updateName": "设备类型",
 *
 */
async function getDeviceImportListLogic(queryParams) {
  const rules = [
    {
      field: "isValidate",
      title: "验证结果",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const [deviceList, keyValConfig] = await Promise.all([
    deviceDao.getDeviceImportSqlList(queryParams),
    global.context.getPropertymapKeyVal(["DEVICE_MODEL"]),
  ]);

  deviceList.rows.forEach((e, i) => {
    e.index = queryParams.offset + i + 1;
    commFunc.formatNullToString(e);
    e.deviceModelName = keyValConfig.DEVICE_MODEL[e.deviceModel] || "";
    e.policyExpDate = e.policyExpDate
      ? moment.unix(e.policyExpDate).format("YYYY-MM-DD")
      : "";
    e.installDate = e.installDate
      ? moment.unix(e.installDate).format("YYYY-MM-DD")
      : "";
    e.expireTime = e.expireTime
      ? moment.unix(e.expireTime).format("YYYY-MM-DD")
      : "";
    e.failReason = `${e.plateNoValidate} ${e.imeiValidate} ${e.deviceModelValidate} ${e.simNoValidate}`.trim();
  });

  return deviceList;
}

/**
 * 插入车辆导入数据
 * 请求参数:
 "imei": "设备号",
 "deviceModelName": "设备型号",
 "installLocation": "安装位置",
 "installer": "安装人",
 "installDate": "安装日期",
 "expireTime": "客户到期时间",
 "simNo": "sim卡号",
 "iccid": "iccid",
 "servicePswd": "服务密码",
 "deviceRemark": "设备备注",
 "isUpdateCar": "是否更新车辆信息",

 "plateNo": "车牌号",
 "vinNo": "车架号",
 "engineNo": "发动机号",
 "policyNo": "保单号",
 "policyExpDate": "保单过期日期",
 "carBrand": "车品牌",
 "carModel": "车型号",
 "carOwner": "车主",
 "ownerPhone": "车主手机",
 "carRemark": "备注",

 "userName": "操作人"
 *
 */
async function insertDeviceImportLogic(insertList, currentUser) {
  const nameKeyConfig = await global.context.getPropertymapNameKey([
    "DEVICE_MODEL",
  ]);

  if (
    Array.from(new Set(insertList.map((m) => m.imei))).length !==
    insertList.length
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.UPLOAD_FILE_IMEI_REPEAT);
  }

  insertList.forEach((f) => {
    f.plateNo = f.plateNo || "粤B00000";
    f.deviceName = f.deviceName || "";
    f.deviceModel = nameKeyConfig.DEVICE_MODEL[f.deviceModelName] || "";
    f.imeiValidate = validator.isIMEI(f.imei) ? "" : "设备IMEI号输入有误";
    f.plateNoValidate = validator.isCarPlateNo(f.plateNo)
      ? ""
      : "车牌号输入有误";
    f.simNoValidate = validator.isSimNo(f.simNo) ? "" : "SIM卡号输入有误";
    f.deviceModelValidate = validator.isIntFormat(f.deviceModel, { min: 1 })
      ? ""
      : "设备类型输入有误";
    f.isValidate =
      f.imeiValidate ||
        f.plateNoValidate ||
        f.simNoValidate ||
        f.deviceModelValidate
        ? 1
        : 0;
    f.isUpdateCar = f.isUpdateCar === "是" ? 1 : 0;

    f.iccid = validator.isICCID(f.iccid) ? f.iccid : "";
    f.ownerPhone = validator.isInt11(f.ownerPhone, "YYYYMMDD")
      ? f.ownerPhone
      : "";
    f.installDate = validator.isDateFormat(f.installDate, "YYYYMMDD")
      ? f.installDate
      : 0;
    f.expireTime = validator.isDateFormat(f.expireTime, "YYYYMMDD")
      ? f.expireTime
      : 0;
    f.policyExpDate = validator.isDateFormat(f.policyExpDate, "YYYYMMDD")
      ? f.installDate
      : 0;
    f.userName = currentUser.loginName;
  });
  return await deviceDao.insertDeviceImportSql(
    insertList,
    currentUser.loginName
  );
}

/**
 * 更新车辆导入数据
 * 请求参数:
 "deviceImportId": "设备导入Id",
 "imei": "设备号",
 "deviceModel": "设备型号",
 "simNo": "sim卡号",
 "plateNo": "车牌号",
 "userName": "操作人"
 *
 */
async function updateDeviceImportLogic(updateData, currentUser) {
  if (!validator.isIntFormat(updateData.deviceImportId, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  updateData.deviceName = updateData.deviceName || "";
  updateData.plateNo = updateData.plateNo || "粤B00000";
  updateData.imeiValidate = validator.isIMEI(updateData.imei)
    ? ""
    : "设备IMEI号输入有误";
  updateData.plateNoValidate = validator.isCarPlateNo(updateData.plateNo)
    ? ""
    : "车牌号输入有误";
  updateData.simNoValidate = validator.isSimNo(updateData.simNo)
    ? ""
    : "SIM卡号输入有误";
  updateData.deviceModelValidate = validator.isIntFormat(
    updateData.deviceModel,
    { min: 1 }
  )
    ? ""
    : "设备类型输入有误";
  updateData.isValidate =
    updateData.imeiValidate ||
      updateData.plateNoValidate ||
      updateData.simNoValidate ||
      updateData.deviceModelValidate
      ? 1
      : 0;
  updateData.userName = currentUser.loginName;

  return await deviceDao.updateDeviceImportSql(updateData);
}

/**
 * 完成车辆导入数据
 * 请求参数:
 "organId": "机构Id",
 "userName": "操作人"
 *
 */
async function finishImportDeviceLogic(requestData, currentUser) {
  if (!validator.isIntFormat(requestData.organId, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  const deviceList = await this.getDeviceImportListLogic({
    isValidate: 0,
    userName: currentUser.loginName,
    pagination: false,
    offset: 0,
  });

  for (const deviceData of deviceList.rows) {
    try {
      deviceData.organId = requestData.organId;
      deviceData.currentOrganId = requestData.currentOrganId;
      deviceData.userName = currentUser.loginName;
      await this.insertDeviceLogic(deviceData, currentUser);
    } catch (ex) {
      console.error(ex);
    }
  }

  return deviceList.rows.map((m) => m.imei);
}

module.exports = {
  getDeviceListLogic,
  getDeviceDataLogic,
  getDeviceDropdownListLogic,
  getOrganDeviceListLogic,
  getDeviceInfoListLogic,
  getDeviceStateCountLogic,
  insertDeviceLogic,
  updateDeviceLogic,
  deleteDeviceLogic,
  updateDeviceOrganLogic,
  getOfflineDeviceListLogic,
  getSameCarDeviceListLogic,
  updateDeviceAttentionLogic,
  getCarSettleListLogic,
  updateCarSettleLogic,
  getAttentionDeviceListLogic,
  getDeviceImportListLogic,
  insertDeviceImportLogic,
  updateDeviceImportLogic,
  finishImportDeviceLogic,
  getBigCarDeviceListLogic,
};
