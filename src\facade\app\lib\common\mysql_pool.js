const mysql = require("node-async-mysql");
const commFunc = require("./comm_func");
const staticConfig = require("../../config/config_static");
const mysqlPool = new mysql.Pool(staticConfig.mysqlConfig);

function queryFormat(query, values) {
  if (!values) return query;
  return query.replace(/\:(\w+_?)+/g, (txt, key) => {
    if (values.hasOwnProperty(commFunc.toCamelCase(key))) {
      return this.escape(values[commFunc.toCamelCase(key)]);
    }
    return txt;
  });
}

/* sql, params */
async function one(sql, params) {
  let connection = null;
  try {
    connection = await mysqlPool.getConn();
    connection.config.queryFormat = queryFormat;
    const results = await connection.query(`${sql} limit 1`, params);
    const result = results && results.length > 0 ? results[0] : undefined;
    return commFunc.camelCaseObjectKeys(result);
  } catch (err) {
    throw err ? err.message || err : undefined;
  } finally {
    if (connection) {
      await connection.release();
    }
  }
}

exports.one = one;

/* sql, params */
async function all(sql, params) {
  let connection = null;
  try {
    connection = await mysqlPool.getConn();
    connection.config.queryFormat = queryFormat;
    const results = await connection.query(sql, params);
    return results.map((m) => commFunc.camelCaseObjectKeys(m));
  } catch (err) {
    throw err ? err.message || err : undefined;
  } finally {
    if (connection) {
      await connection.release();
    }
  }
}

exports.all = all;

/* sql, params */
async function update(sql, params) {
  let connection = null;
  try {
    connection = await mysqlPool.getConn();
    connection.config.queryFormat = queryFormat;
    const results = await connection.query(sql, params);
    return results && results.hasOwnProperty("changedRows")
      ? results.changedRows
      : 0;
  } catch (err) {
    throw err ? err.message || err : undefined;
  } finally {
    if (connection) {
      await connection.release();
    }
  }
}

exports.update = update;

/* sql, params */
async function insert(sql, params) {
  let connection = null;
  try {
    connection = await mysqlPool.getConn();
    connection.config.queryFormat = queryFormat;
    const results = await connection.query(sql, params);
    return results && results.hasOwnProperty("insertId") ? results.insertId : 0;
  } catch (err) {
    throw err ? err.message || err : undefined;
  } finally {
    if (connection) {
      await connection.release();
    }
  }
}

exports.insert = insert;

/* sql, params */
async function del(sql, params) {
  let connection = null;
  try {
    connection = await mysqlPool.getConn();
    connection.config.queryFormat = queryFormat;
    const results = await connection.query(sql, params);
    return results && results.hasOwnProperty("changedRows")
      ? results.changedRows
      : 0;
  } catch (err) {
    throw err ? err.message || err : undefined;
  } finally {
    if (connection) {
      await connection.release();
    }
  }
}

exports.del = del;

/* sql, params */
async function queryForPagination(sql, params) {
  try {
    const sqlCount = `select count(1) as count from (${sql}) sqltotal`;
    const sqlLimit =
      params && params.hasOwnProperty("pagination") && !params.pagination
        ? ""
        : " limit :offset, :pageSize";
    const [countResult, rowsResult] = await Promise.all([
      one(sqlCount, params),
      all(sql + sqlLimit, params),
    ]);

    const total = countResult ? countResult.count : 0;
    return {
      total: total,
      rows: rowsResult || [],
    };
  } catch (err) {
    throw err ? err.message || err : undefined;
  }
}

exports.queryForPagination = queryForPagination;

async function executeTransaction(sqlTasks) {
  let connection;
  try {
    connection = await mysqlPool.getConn();

    await connection.beginTran();
    connection.config.queryFormat = queryFormat;

    const taskResult = {};

    for (const i in sqlTasks) {
      const { sql, params, field } = sqlTasks[i];
      let result = await connection.query(sql, params);
      result = result || undefined;
      taskResult[field ? field : i] = result;
    }

    await connection.commit();
    connection.release();

    return taskResult;
  } catch (err) {
    console.log("SQL语句执行失败：", sqlTasks, err);
    if (connection) {
      try {
        connection.rollback();
      } catch (ex) {
        console.error(ex);
      } finally {
        connection.release();
      }
    }
    throw err ? err.message || err : undefined;
  }
}

exports.executeTransaction = executeTransaction;
